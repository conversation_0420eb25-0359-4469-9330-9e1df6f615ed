<?php

use App\Http\Controllers\BannerController;
use App\Http\Controllers\BonusController;
use App\Http\Controllers\BonusHistoryLogController;
use App\Http\Controllers\BonusInfoController;
use App\Http\Controllers\DepositController;
use App\Http\Controllers\FreeSpinBoundController;
use App\Http\Controllers\FreeSpinController;
use App\Http\Controllers\BonusApplyController;
use App\Http\Controllers\FreeSpinHistoryLogController;
use App\Http\Controllers\GamicorpController;
use App\Http\Controllers\MaintenanceController;
use App\Http\Controllers\PromoCodeController;
use App\Http\Controllers\SmarticoController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => '/free-spins'], static function (): void {
    Route::group(['middleware' => ['logRequest', 'sign', 'cluster-connection']], static function (): void {
        Route::get('', [FreeSpinController::class, 'list']);
        Route::get('show/{id}', [FreeSpinController::class, 'show']);
        Route::post('add', [FreeSpinController::class, 'create']);
        Route::put('update/{id}', [FreeSpinController::class, 'update']);
        Route::put('delete/{id}', [FreeSpinController::class, 'delete']);
        Route::post('attach', [FreeSpinBoundController::class, 'attach']);
        Route::post('resend', [FreeSpinController::class, 'resend']);
        Route::post('check-free-spin-name', [FreeSpinController::class, 'checkFreeSpinName']);
        Route::get('player-bound-history/{id}', [FreeSpinBoundController::class, 'playerBoundHistory']);
        Route::get('player-bound-active/{id}', [FreeSpinBoundController::class, 'playerBoundActive']);
        Route::get('get-players/{id}', [FreeSpinBoundController::class, 'getFreeSpinBoundsByFreeSpinID']);

        Route::get('get-bounds', [FreeSpinBoundController::class, 'getFreeSpinBounds']);
        Route::put('update-bound/{id}', [FreeSpinBoundController::class, 'updateBound']);

        Route::get('/get-history-logs/{id}', [FreeSpinHistoryLogController::class, 'list']);
    });

    Route::group(['middleware' => ['logRequest', 'user_auth']], static function (): void {
        Route::put('cancel-bound/{id}', [FreeSpinBoundController::class, 'cancelBound']);
        Route::get('player/{status}', [FreeSpinBoundController::class, 'playerMeFreeSpinsByStatus']);
    });
});

Route::group(['prefix' => '/bonuses'], static function (): void {
    Route::group(['middleware' => ['logRequest', 'sign', 'cluster-connection'], 'as' => 'bonuses.'], static function (): void {
        Route::post('add', [BonusController::class, 'create'])->name('add');
        Route::get('/get-bonus/{bonus}', [BonusController::class, 'show'])->name('show');
        Route::get('/get-history-logs/{bonus}', [BonusHistoryLogController::class, 'list']);
        Route::put('/{id}', [BonusController::class, 'update'])->name('update');
        Route::get('', [BonusController::class, 'list'])->name('list');
        Route::delete('/{bonus}', [BonusController::class, 'destroy'])->name('delete');

        Route::post('/add-betby-template-bonus/{id}', [BonusController::class, 'createBetbyBonus']);
        Route::get('is-bonus-related-to-slot', [BonusController::class, 'isBonusRelatedToSlot']);

        Route::group(['prefix' => '/info'], static function (): void {
            Route::get('', [BonusInfoController::class, 'index']);
            Route::post('', [BonusInfoController::class, 'create']);
            Route::put('/{id}', [BonusInfoController::class, 'update']);

            Route::get('/enabled/{player_id}', [BonusInfoController::class, 'enabledBonusesInfoForAdmin']);
        });

        Route::post('/promo', [PromoCodeController::class, 'create']);
        Route::get('/promo', [PromoCodeController::class, 'index']);
        Route::put('/promo/{id}', [PromoCodeController::class, 'update']);
        Route::get('/promo/alanbase/{playerId}', [PromoCodeController::class, 'getAlanbasePromoCodeByPlayerId']);
        Route::get('/get-player-promocodes/{player_id}', [PromoCodeController::class, 'playerPromoCodes']);

        Route::get('{bonusType}/details/{bonusId}', [BonusApplyController::class, 'index']);
        Route::post('apply-admin/{bonusType}', [BonusApplyController::class, 'applyBonusForPlayer']);
        Route::post('mass-apply-admin', [BonusApplyController::class, 'massBonusApply']);
        Route::post('/smartico-onetime', [BonusApplyController::class, 'applyBonusSmarticoOnetime']);

        Route::post('deposit-selected-bonus', [DepositController::class, 'depositSelectedBonus']);

        Route::get('/player-selected-bonus/{player_uuid}', [BonusController::class, 'getPlayerSelectedBonusByUuid']);

        Route::get('/get-bonus-ids-for-slot/{slotId}', [BonusController::class, 'bonusesIdsBySlotId']);
        Route::get('/get-bonus-slot-provider-ids/{bonusId}', [BonusController::class, 'slotProviderIdsByBonusId']);
        Route::get('/is-wager-playable', [BonusController::class, 'isWagerPayable']);
        Route::get('/is-general/{bonus_id}', [BonusController::class, 'isGeneralBonus']);

        Route::post('/apply-to-player', [BonusController::class, 'applyToPlayer']);
        Route::post('/promo/apply-to-player', [PromoCodeController::class, 'applyToPlayer']);
    });

    Route::group(['middleware' => ['logRequest', 'user_auth']], static function (): void {
        Route::post('/apply', [BonusController::class, 'apply'])->name('apply')->middleware(['throttle:10,1,apply']);
        Route::post('/promo/apply', [PromoCodeController::class, 'apply']);
        Route::post('/detach', [BonusController::class, 'detach'])->name('detach')->middleware(['throttle:1000,1,detach']);
    });

    Route::middleware(['logRequest'])->patch('/promo', [PromoCodeController::class, 'check']);

    Route::group(['middleware' => ['logRequest', 'user_auth_with_public_access:has_deposits']], static function (): void {
        Route::get('/info/enabled', [BonusInfoController::class, 'enabledBonusesInfo']);
    });
});

Route::group(['prefix' => '/record', 'middleware' => ['logRequest', 'sign']], static function (): void {
    Route::post('welcome-bonus', [BonusController::class, 'registrationPlayerBonus']);
});

Route::group(['prefix' => '/bonuses', 'middleware' => ['logRequest']], static function (): void {
    Route::get('get-templates', [BonusController::class, 'getTemplates']);

    Route::group(['middleware' => ['cache']], static function (): void {
        Route::get('/welcome', [BonusController::class, 'activeWelcomeBonuses']);
        Route::get('/welcome-for-land', [BonusController::class, 'activeWelcomeBonuses']);
        Route::get('/enabled', [BonusController::class, 'enabledBonuses']);
        Route::get('/info/enabled-cards', [BonusInfoController::class, 'enabledBonusesInfoCards']);
    });
});

Route::group(['prefix' => '/bonuses', 'middleware' => ['logRequest', 'token-check']], static function (): void {
    Route::get('/info-data', [BonusController::class, 'getBonusInfo']);
});

Route::group(['prefix' => '/smartico'], static function (): void {
    Route::group(['middleware' => ['logRequest', 'smartico-auth', 'cluster-connection']], static function (): void {
        Route::post('issue-bonus', [SmarticoController::class, 'issuePlayerBonus']);
        Route::post('issue-freespin', [SmarticoController::class, 'issueFreeSpin']);
    });
});

Route::group(['prefix' => '/settings'], static function (): void {
    Route::group(['prefix' => '/banners', 'as' => 'banners.'], static function (): void {
        Route::group(['middleware' => ['logRequest']], static function (): void {
            Route::group(['middleware' => ['sign']], static function (): void {
                Route::get('', [BannerController::class, 'index'])->name('index');
                Route::post('', [BannerController::class, 'create'])->name('create');
                Route::put('/{id}', [BannerController::class, 'update'])->name('update');
                Route::put('/{id}/change-status', [BannerController::class, 'changeStatus'])
                    ->name('changeStatus');
                Route::put('/change-status/many', [BannerController::class, 'changeStatusMany'])
                    ->name('changeStatusMany');
                Route::delete('/{id}', [BannerController::class, 'delete'])->name('delete');
            });
            Route::group(['middleware' => ['cluster-connection']], static function (): void {
                Route::get('/enabled', [BannerController::class, 'enabledBanners'])->name('enabled');
            });
        });
    });
});

Route::group(['prefix' => '/free-bets'], static function (): void {
    Route::group(['middleware' => ['logRequest']], static function (): void {
        Route::post('finish', [GamicorpController::class, 'finish']);
    });
});

// Maintenance routes
Route::group(['prefix' => '/maintenance', 'middleware' => ['logRequest']], static function (): void {
    Route::delete('/clear-cache', [MaintenanceController::class, 'clearCache']);
});
