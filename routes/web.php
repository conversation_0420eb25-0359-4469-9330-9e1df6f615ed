<?php

use App\Http\Middleware\SwaggerIsAuthorizedMiddleware;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

Route::get('/swagger', function () {
    $request = request();
    $file = collect(config('swagger-ui.files'))->filter(function ($values) use ($request) {
        return ltrim($values['path'], '/') === $request->path();
    })->firstOrFail();
    $isSecure = null;
    if (!app()->environment('local')) {
        $isSecure = true;
    }
    return view('swagger', ['data' => collect($file), 'isSecure' => $isSecure]);
})->middleware(['web', SwaggerIsAuthorizedMiddleware::class]);
