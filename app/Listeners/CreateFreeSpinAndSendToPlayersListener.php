<?php

namespace App\Listeners;

use App\Events\FreeSpinCreateEvent;
use App\Jobs\FreeSpins\NewFreeSpinJob;
use App\Models\DataSubscriptions;
use App\Services\Dto\FreeSpinIntegrationSlotEgratorDto;
use App\Services\Dto\SlotDto;
use App\Services\SlotService;
use Throwable;

class CreateFreeSpinAndSendToPlayersListener
{
    public function __construct(
        private readonly SlotService $slotService,
    ) {
    }

    /**
     * Handle the event.
     *
     * @throws Throwable
     */
    public function handle(FreeSpinCreateEvent $event): void
    {
        $chunkedPlayerIds = array_chunk($event->playerIds, 100);

        $slot = $this->slotService->getSlotById($event->freespin->slot_id);
        $subscription = $this->slotService->getSubscriptionByProvider($slot->slot_provider);
        foreach ($chunkedPlayerIds as $playerIds) {
            $dto = $this->getDTO($event, $subscription, $slot, $playerIds);
            dispatch(new NewFreeSpinJob($dto));
        }
    }

    /**
     * @param int[] $players
     */
    private function getDTO(
        FreeSpinCreateEvent $event,
        DataSubscriptions $subscription,
        SlotDto $slot,
        array $players
    ): FreeSpinIntegrationSlotEgratorDto {
        /** @var FreeSpinIntegrationSlotEgratorDto */
        return FreeSpinIntegrationSlotEgratorDto::fromArray([
            'id' => $event->freespin->id,
            'bet' => $event->freespin->bet,
            'bet_id' => $event->freespin->bet_id,
            'count' => $event->freespin->count,
            'denomination' => $event->freespin->denomination,
            'currency' => $event->freespin->currency,
            'start_at' => (string)$event->startAt->timestamp,
            'expired_at' => (string)$event->expiredAt->timestamp,
            'sub_id' => $subscription->sub_id,
            'game_token' => $subscription->game_token,
            'external_id' => $slot->externalId,
            'bonus_id' => $event->freespin->bonus_id,
            'players' => $players,
        ]);
    }
}
