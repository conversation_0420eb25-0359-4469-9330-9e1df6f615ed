<?php

namespace App\Listeners;

use App\Events\FreeSpinCudEvent;
use App\Services\FreeSpinHistoryLogService;
use Throwable;

class FreeSpinCudListener
{
    public function __construct(
        private readonly FreeSpinHistoryLogService $freeSpinHistoryLogService,
    ) {
    }

    /**
     * Handle the event.
     *
     * @throws Throwable
     */
    public function handle(FreeSpinCudEvent $event): void
    {
        if (!$event->prevFreeSpin && !$event->currentFreeSpin) {
            $this->freeSpinHistoryLogService->createFreeSpinHistoryLog(
                $event->freeSpinId,
                null,
                'delete',
                [],
                $event->authorEmail
            );
        } else {
            $this->freeSpinHistoryLogService->createLog(
                $event->prevFreeSpin,
                $event->currentFreeSpin,
                $event->authorEmail
            );
        }
    }
}
