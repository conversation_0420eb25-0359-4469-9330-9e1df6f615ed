<?php

namespace App\Listeners;

use App\Events\FreeSpinResendEvent;
use App\Jobs\FreeSpins\ResendFreeSpinJob;
use App\Models\DataSubscriptions;
use App\Services\Dto\FreeSpinIntegrationSlotEgratorDto;
use App\Services\Dto\SlotDto;
use App\Services\SlotService;
use Throwable;

class FreeSpinResendToPlayersListener
{
    public function __construct(
        private readonly SlotService $slotService,
    ) {
    }

    /**
     * Handle the event.
     *
     * @throws Throwable
     */
    public function handle(FreeSpinResendEvent $event): void
    {
        $chunkedFreeSpinBoundIds = array_chunk($event->freespinBoundIdsArray, 100);

        $slot = $this->slotService->getSlotById($event->freespin->slot_id);
        $subscription = $this->slotService->getSubscriptionByProvider($slot->slot_provider);
        foreach ($chunkedFreeSpinBoundIds as $boundIds) {
            $dto = $this->getDTO($event, $subscription, $slot, $boundIds);
            dispatch(new ResendFreeSpinJob($dto));
        }
    }

    /**
     * @param int[] $players
     */
    private function getDTO(
        FreeSpinResendEvent $event,
        DataSubscriptions $subscription,
        SlotDto $slot,
        array $players
    ): FreeSpinIntegrationSlotEgratorDto {
        /** @var FreeSpinIntegrationSlotEgratorDto */
        return FreeSpinIntegrationSlotEgratorDto::fromArray([
            'id' => $event->freespin->id,
            'bet' => $event->freespin->bet,
            'bet_id' => $event->freespin->bet_id,
            'count' => $event->freespin->count,
            'denomination' => $event->freespin->denomination,
            'currency' => $event->freespin->currency,
            'start_at' => '',
            'expired_at' => '',
            'sub_id' => $subscription->sub_id,
            'game_token' => $subscription->game_token,
            'external_id' => $slot->externalId,
            'bonus_id' => $event->freespin->bonus_id,
            'players' => $players,
        ]);
    }
}
