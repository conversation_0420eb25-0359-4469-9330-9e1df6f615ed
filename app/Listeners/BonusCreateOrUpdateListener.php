<?php

namespace App\Listeners;

use App\Events\BonusCreateOrUpdateEvent;
use App\Services\BonusHistoryLogService;
use Throwable;

class BonusCreateOrUpdateListener
{
    public function __construct(
        private readonly BonusHistoryLogService $bonusHistoryLogService,
    )
    {}

    /**
     * Handle the event.
     *
     * @param BonusCreateOrUpdateEvent $event
     * @return void
     * @throws Throwable
     */
    public function handle(BonusCreateOrUpdateEvent $event): void
    {
        if ($event->prevBonus) {
            $this->bonusHistoryLogService->createLog($event->prevBonus, 'update', false);
            $this->bonusHistoryLogService->updateLog($event->currentBonus);
        } else {
            $this->bonusHistoryLogService->createLog($event->currentBonus);
        }
    }
}
