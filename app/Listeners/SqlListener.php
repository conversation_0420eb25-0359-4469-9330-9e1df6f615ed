<?php

namespace App\Listeners;

use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Support\Facades\DB;
use Psr\Log\LoggerInterface;

class SqlListener
{
    private LoggerInterface $logger;
    private int $totalQueries = 0;
    /**
     * @var array<string, array<string, string|int>>
     */
    private array $queryDuplicates = [];

    protected bool $isLoggingEnabled;
    protected bool $isLoggingPerformanceEnabled;

    public function __construct(
        LoggerInterface $logger
    ) {
        $this->logger = $logger;

        $this->isLoggingEnabled = config('database.log_database_query');
        $this->isLoggingPerformanceEnabled = config('database.log_database_query_performance');
    }

    public function __invoke(QueryExecuted $executed): void
    {
        if ($this->isLoggingEnabled || $this->isLoggingPerformanceEnabled) {
            $this->logQuery($executed);
        }
    }

    public function __destruct()
    {
        $this->logPerformedQueries();
    }

    private function logQuery(QueryExecuted $executed): void
    {
        if ($this->isLoggingPerformanceEnabled) {
            $this->appendQuery($executed);
        }

        if (!$this->isLoggingEnabled) {
            return;
        }

        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);

        $skipClass = [
            self::class,
            'Illuminate\Database\Eloquent\Builder',
            'Illuminate\Database\Connection',
            'Illuminate\Events\Dispatcher',
            'Illuminate\Database\Query\Builder',
            'Illuminate\Container\Container',
            'Illuminate\Container\BoundMethod',
            'Illuminate\Pipeline\Pipeline',
            'Illuminate\Container\Util',
            'Laravel\Lumen\Application',
            'Laravel\Lumen\Routing\Pipeline',
        ];

        $trace = array_filter($trace, static fn($call) => !in_array($call['class'] ?? '', $skipClass, true));

        $trace = array_map(static fn($call) => [
            'file' => implode(':', [$call['file'] ?? '', $call['line'] ?? '']),
            'call' => implode('::', [$call['class'] ?? null ?: '', $call['function'] ?: '']),
        ], $trace);

        $this->logger->notice('Database query executed', [
            'connection' => DB::connection(),
            'sql_query' => $executed->sql,
            'sql_bindings' => $executed->bindings,
            'sql_time' => round($executed->time / 1e3, 10),
            'trace' => $trace,
        ]);
    }

    private function appendQuery(QueryExecuted $executed): void
    {
        ++$this->totalQueries;
        $queryKey = md5($executed->sql);
        if (!isset($this->queryDuplicates[$queryKey])) {
            $this->queryDuplicates[$queryKey] = ['sql' => $executed->sql, 'count' => 0];
        }
        ++$this->queryDuplicates[$queryKey]['count'];
    }

    private function logPerformedQueries(): void
    {
        if (!$this->isLoggingPerformanceEnabled || $this->totalQueries < 1) {
            return;
        }

        $queries = array_filter($this->queryDuplicates, static fn($row) => $row['count'] > 1);
        usort($queries, static fn($row1, $row2) => (int)($row1['count'] < $row2['count']));

        $duplicatesData = '-';
        if (count($queries) > 0) {
            $duplicatesData = implode("\n\t-", array_map(static fn($row) => sprintf('[%d] %s', $row['count'], $row['sql']), $queries));
        }

        $this->logger->notice(sprintf("Performed queries: \n - total: %d\n - duplicated: %s", $this->totalQueries, $duplicatesData));
    }
}
