<?php

declare(strict_types=1);

namespace App\Exceptions;

use RuntimeException;

class GamicorpException extends RuntimeException
{
    /**
     * @var array<string, mixed>
     */
    protected array $context;

    /**
     * @param array<string, mixed> $context
     */
    public function __construct(string $message, int $code, array $context = [], mixed $previous = null)
    {
        parent::__construct($message, $code, $previous);

        $this->message = $message;
        $this->context = $context;
    }

    /**
     * @return array<string, array<string, mixed>|int|string>
     */
    public function render(): array
    {
        return [
            'code' => $this->getCode(),
            'status' => 'error',
            'message' => $this->getMessage(),
        ];
    }
}
