<?php

declare(strict_types=1);

namespace App\Exceptions;

use Throwable;

class Exception extends \Exception
{
    public function __construct(string $message = '', int $code = 0, ?Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * @return array<string, array<string, mixed>|int|string>
     */
    public function render(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            '_file' => $this->getFile(),
            '_line' => $this->getLine(),
            'trace' => $this->getTraceAsString(),
        ];
    }
}
