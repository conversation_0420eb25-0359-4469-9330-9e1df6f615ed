<?php

declare(strict_types=1);

namespace App\Exceptions;

use RuntimeException;

class InternalLogicException extends RuntimeException
{
    protected int $internalCode;

    /**
     * @var array<string, mixed>
     */
    protected array $context;

    /**
     * @param array<string, mixed> $context
     */
    public function __construct(int $internalCode, array $context = [], mixed $previous = null, int $code = 400)
    {
        parent::__construct(__("internal_errors.$internalCode"), $code, $previous);

        $this->internalCode = $internalCode;
        $this->context = $context;
    }

    /**
     * @return array<string, array<string, mixed>|int|string>
     */
    public function render(): array
    {
        $context = [
            '_file' => $this->getFile(),
            '_line' => $this->getLine(),
        ];

        if ($this->getPrevious()) {
            $context['_previous'] = [
                'message' => $this->getPrevious()->getMessage(),
                '_file' => $this->getPrevious()->getFile(),
                '_line' => $this->getPrevious()->getLine(),
            ];
        }

        return [
            'code' => $this->getCode(),
            'message' => $this->getMessage(),
            'error_code' => $this->internalCode,
            'context' => $context + $this->context,
            'trace' => $this->getTraceAsString(),
        ];
    }
}
