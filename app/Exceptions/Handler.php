<?php

namespace App\Exceptions;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(static function (Throwable $e): void {

        });
    }

    public function render($request, Throwable $e): \Illuminate\Http\Response|JsonResponse|RedirectResponse|Response
    {
        if ($e instanceof InvalidArgumentException) {
            $data = $e->render();
            if (! config('app.debug')) {
                unset(
                    $data['trace'],
                    $data['_file'],
                    $data['_line'],
                );
            }

            return response()->json($data, $e->getCode() ?: 500);
        }

        if ($e instanceof SmarticoException) {
            $data = $e->render();
            if (! config('app.debug')) {
                unset(
                    $data['trace'],
                    $data['context']['_file'],
                    $data['context']['_line'],
                    $data['context']['_previous'],
                );
            }

            return response()->json($data, $e->getCode() ?: 500);
        }

        if ($e instanceof InternalLogicException) {
            $data = $e->render();
            if (! config('app.debug')) {
                unset(
                    $data['trace'],
                    $data['context']['_file'],
                    $data['context']['_line'],
                    $data['context']['_previous'],
                );
            }

            return response()->json($data, $e->getCode() ?: 500);
        }

        if ($e instanceof ModelNotFoundException) {
            return response()->json([
                'error' => [
                    'message' => $e->getMessage(),
                    'code' => 404,
                ],
            ], 404);
        }

        if ($e instanceof AuthenticationException) {
            return response()->json([
                'message' => $e->getMessage(),
                'code' => Response::HTTP_UNAUTHORIZED,
            ], Response::HTTP_UNAUTHORIZED);
        }

        return parent::render($request, $e);
    }
}
