<?php
namespace App\Exceptions;

class ApiException extends Exception
{
    /**
     * @param array<string, mixed> $context
     */
    public function __construct(
        string $message,
        protected $context = [],
        int $code = 400,
    ) {
        parent::__construct($message, $code);
    }

    /**
     * @return array<string, array<string, mixed>|int|string>
     */
    public function render(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'context' => $this->context,
            '_file' => $this->getFile(),
            '_line' => $this->getLine(),
            'trace' => $this->getTraceAsString(),
        ];
    }
}
