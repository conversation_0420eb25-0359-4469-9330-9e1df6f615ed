<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Support\Facades\Log;

class InvalidArgumentException extends Exception
{
    /**
     * @param array<string, mixed> $context
     */
    public function __construct(
        protected string $argument,
        protected int|string $value,
        protected array $context = [],
        int $code = 400
    ) {
        $message = sprintf('Invalid argument provided for %s - %s', $this->argument, $this->value);
        parent::__construct($message, $code);
    }
    public function report(): void
    {
        Log::warning('Bonus does not meet reqs', $this->context);
    }

    /**
     * @return array<string, array<string, mixed>|int|string>
     */
    public function render(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'context' => $this->context,
            '_file' => $this->getFile(),
            '_line' => $this->getLine(),
            'trace' => $this->getTraceAsString(),
        ];
    }
}
