<?php

declare(strict_types=1);

namespace App\Clients;

use App\Clients\Requests\RequestBuilder;
use App\Services\Dto\GamicorpFreeSpinCreateDto;
use App\Services\Dto\GamicorpResponseDTO;
use Illuminate\Http\Client\PendingRequest;

class GamicorpClient
{
    private PendingRequest $request;

    public function __construct(RequestBuilder $builder)
    {
        $this->request = $builder
            ->withGamicorpSignatureMiddleware()
            ->withLoggingMiddleware(RequestBuilder::LOG_REQUESTS | RequestBuilder::LOG_ERRORS)
            ->withTrace()
            ->build();
    }

    public function createFreeSpin(GamicorpFreeSpinCreateDto $dto): GamicorpResponseDTO
    {
        $response = $this->request()->post('/api/free-bets/create', $dto->toArray(false));

        $gamicorpResponseDTO = GamicorpResponseDTO::fromArray([
            'status' => $response->json()['status'] ?? '',
            'error' => $response->json()['error'] ?? '',
            'meta' => $response->json()['meta'] ?? [],
        ]);

        $gamicorpResponseDTO->freespinBoundId = (int)$dto->freebetId;

        return $this->getErrorMessages($gamicorpResponseDTO);
    }

    public function cancelFreeSpinBound(int $freeSpinBoundId): GamicorpResponseDTO
    {
        $response = $this->request()->post('/api/free-bets/cancel', [
            'token' => config('auth.gamicorp_merchant_token'),
            'freebet_id' => (string)$freeSpinBoundId,
        ]);

        $gamicorpResponseDTO = GamicorpResponseDTO::fromArray([
            'status' => $response->json()['status'] ?? '',
            'error' => $response->json()['error'] ?? '',
            'meta' => $response->json()['meta'] ?? [],
        ]);

        $gamicorpResponseDTO->freespinBoundId = $freeSpinBoundId;

        return $this->getErrorMessages($gamicorpResponseDTO);
    }

    public function getErrorMessages(GamicorpResponseDTO $dto): GamicorpResponseDTO
    {
        if ($dto->status == 'fail') {
            $dto->errorMessage = 'Failed sign';
            $dto->statusCode = 401;
        }

        if ($dto->error != '') {
            if($dto->error == 'freebet_not_found') {
                $dto->errorMessage = 'Freebet not found';
                $dto->statusCode = 404;
            }
            elseif($dto->error == 'provider_not_found') {
                $dto->errorMessage = 'Provider not found';
                $dto->statusCode = 404;
            }
            elseif($dto->error == 'games_not_found') {
                $dto->errorMessage = 'Games not found';
                $dto->statusCode = 404;
            }
            elseif($dto->error == 'integration_error') {
                $dto->errorMessage = 'Integration error';
                $dto->statusCode = 400;
            }
            elseif($dto->error == 'icore_error') {
                $dto->errorMessage = 'Freebet not found';
                $dto->statusCode = 400;
            }
            else {
                $dto->errorMessage = 'Unknown error';
                $dto->statusCode = 400;
            }
        } elseif ($dto->status !== 'success') {
            $dto->errorMessage = 'Unknown error';
            $dto->statusCode = 400;
        }

        return $dto;
    }

    private function request(): PendingRequest
    {
        return clone $this->request;
    }
}
