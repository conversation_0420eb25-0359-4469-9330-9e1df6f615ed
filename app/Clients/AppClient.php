<?php

declare(strict_types=1);

namespace App\Clients;

use App\Clients\Requests\RequestBuilder;
use App\Dto\ResponseOutputDto;
use App\Traits\HandleResponseTrait;
use Carbon\Carbon;
use Illuminate\Auth\AuthenticationException;
use App\Services\Dto\IssueBonusBalanceDTO;
use Illuminate\Database\RecordsNotFoundException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\RequestException;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;

class AppClient
{
    use HandleResponseTrait;

    private PendingRequest $request;

    public function __construct(RequestBuilder $builder)
    {
        $this->request = $builder
            ->withSignatureMiddleware()
            ->withLoggingMiddleware(RequestBuilder::LOG_REQUESTS | RequestBuilder::LOG_ERRORS)
            ->withRetryPolicy(3, 100, 1000)
            ->withTrace()
            ->build();
    }

    /**
     * @return array<array-key, array<string, mixed>>
     *
     * @uses getSlotsByIds
     */
    public function getSlotsByIds(int ...$slotIds): array
    {
        $response = $this->request()->get('/api/v3/slots/list', [
            'ids' => implode(',', $slotIds),
            'all' => true,
        ]);

        return $response->json('data') ?? [];
    }

    /**
     * @return array<array<string, null|string|int|bool>>
     */
    public function getPlayersByUuids(string ...$uuid): array
    {
        $response = $this->request()->get('/api/v3/players/short-info', [
            'uuids' => $uuid,
        ]);

        return $response->json() ?? [];
    }

    /**
     * @return array<array<string, null|string|int|bool>>
     */
    public function getPlayersByIds(int ...$id): array
    {
        $response = $this->request()->get('/api/v3/players/short-info', [
            'ids' => $id,
        ]);

        return $response->json() ?? [];
    }

    /**
     * @param string[] $playerExtraData
     *
     * @return null|array<string, string|array<string, null|array<null|float|int|string>|bool|Carbon|float|int|string>>
     *
     * @throws RequestException
     * @throws AuthenticationException
     *
     * @uses getUserInfoByToken
     */
    public function getUserInfoByToken(string $token, array $playerExtraData = []): ?array
    {
        try {
            $response = $this->request()->withToken($token)->get('/api/v3/players/me/short-info', [
                'extra_data' => $playerExtraData,
            ]);

            return $response->json();
        } catch (RequestException $e) {
            if ($e->response->status() === Response::HTTP_UNAUTHORIZED) {
                throw new AuthenticationException('Unauthorized, please pass a valid core bearer token');
            }

            if ($e->response->status() === Response::HTTP_FORBIDDEN) {
                throw new HttpException(403, 'Your account has been blocked due to failed verification');
            }

            throw $e;
        }
    }

    /**
     * @return array<int, int>
     *
     * @uses getPlayerDeposits
     */
    public function getPlayerDeposits(int $id, int $limit): array
    {
        try {
            $response = $this->request()->get("/api/v3/players/$id/deposits", [
                'perPage' => $limit,
            ]);

            return $response->json('data') ?? [];
        } catch (RequestException) {
            return [1];
        }
    }

    public function hasPlayerDeposits(int $id): bool
    {
        try {
            $response = $this->request()->get("/api/v3/players/$id/has-deposit");

            return $response->json()['has_deposits'] ?? false;
        } catch (RequestException) {
            return false;
        }
    }

    /**
     * @return array<array<string, null|string|int|bool>>
     */
    public function getSlotProvidersByIds(int ...$providerIds): array
    {
        $response = $this->request()->get('/api/v3/slots/providers', [
            'ids' => implode(',', $providerIds),
            'limit' => count($providerIds),
        ]);

        return $response->json('data') ?? [];
    }

    /**
     * @return array<string, mixed>
     */
    public function getBalanceInfoByPlayerUuid(string $uuid): array
    {
        try {
            $response = $this->request()->get('/api/v3/players/get-balance-info', [
                'player_uuid' => $uuid,
            ]);

            return $response->json();
        } catch (RequestException $e) {
            if ($e->response->status() === Response::HTTP_NOT_FOUND) {
                throw new RecordsNotFoundException('Player with uuid not found');
            }
        }

        throw new RuntimeException('Player with uuid not found');
    }

    /**
     * @return array<string, mixed>
     *
     * @uses issueBonus
     */
    public function issueBonus(IssueBonusBalanceDTO $dto): array
    {
        try {
            $response = $this->request()->post('/api/v3/bonus-balances/issue-bonus', $dto->toArray(false));

            return $response->json();
        } catch (RequestException) {
            throw new RuntimeException('Bonus cannot be issued');
        }
    }

    /**
     * @param array<string, string> $data
     *
     * @uses clearPlayerCache
     */
    public function clearPlayerCache(array $data): ResponseOutputDto
    {
        try {
            $response = $this->request()->post('/api/v3/clear-player-cache', $data);

            return $this->handleSuccessResponse($response);
        } catch (RequestException $e) {
            return $this->handleErrorResponse($e);
        }
    }

    /**
     * @param array<string, mixed> $data
     *
     * @uses issueBonusBalances
     */
    public function issueBonusBalances(array $data): ResponseOutputDto
    {
        try {
            $response = $this->request()->post('/api/v3/bonus-balances/issue-bonus', $data);

            return $this->handleSuccessResponse($response);
        } catch (RequestException $e) {
            return $this->handleErrorResponse($e);
        }
    }

    public function getDepositsCount(int $playerId, string $currency): int
    {
        try {
            $response = $this->request()->get('/api/v3/get-deposits-count', [
                'player_id' => $playerId,
                'currency' => $currency,
            ]);

            return $response->json();
        } catch (RequestException) {
            throw new RuntimeException('Error getting deposits count');
        }
    }

    private function request(): PendingRequest
    {
        return clone $this->request;
    }
}
