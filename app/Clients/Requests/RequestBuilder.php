<?php

declare(strict_types=1);

namespace App\Clients\Requests;

use App\Traits\ExcludeHeadersListTrait;
use Deversin\Signature\SignatureInterface;
use GuzzleHttp\Promise\PromiseInterface;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class RequestBuilder
{
    use ExcludeHeadersListTrait;

    public const LOG_REQUESTS = 1;
    public const LOG_RESPONSES = 1;
    public const LOG_ERRORS = 4;

    public function __construct(
        private readonly PendingRequest $request,
        private readonly SignatureInterface $signatureService,
        private readonly string $baseUrl,
        private readonly mixed $httpVerify,
    ) {
    }

    public function withSignatureMiddleware(): self
    {
        $this->request->withMiddleware($this->getSignatureMiddleware())->withOptions(
            ['verify' => $this->httpVerify]
        );

        return $this;
    }

    /**
     * Generates a middleware function that adds a signature to the request if it does not already have one.
     *
     * @return callable
     */
    private function getSignatureMiddleware(): callable
    {
        return function ($handler): callable {
            return function (RequestInterface $request, array $options) use ($handler): PromiseInterface {
                if (!$request->hasHeader(SignatureInterface::SIGNATURE_HEADER)) {
                    parse_str($request->getUri()->getQuery(), $queryData);
                    $body = $request->getBody()->getContents();

                    $signingData = !empty($body) ? $body : json_encode($queryData, JSON_THROW_ON_ERROR);
                    $signature = $this->signatureService->sign($signingData);

                    $request = $request
                        ->withBody($request->getBody())
                        ->withHeader(SignatureInterface::SIGNATURE_HEADER, $signature);
                }

                return $handler($request, $options);
            };
        };
    }

    public function withRetryPolicy(int $retryAttempts, int $retryTimeoutMs, int $maxDelayMs): static
    {
        $this->request->retry(
            $retryAttempts,
            fn (int $attempt): int => $this->exponentialDelay($attempt, $retryTimeoutMs, $maxDelayMs),
            fn (Throwable $exception): bool => $this->isRetriable($exception)
        );

        return $this;
    }

    /**
     * Calculates the exponential delay based on the attempt count, retry timeout.
     *
     * @param int $attempt The number of attempts made.
     * @param int $retryTimeout The base timeout value for each attempt.
     * @param int $maxDelay The maximum delay allowed.
     * @return int The calculated exponential delay.
     */
    private function exponentialDelay(int $attempt, int $retryTimeout, int $maxDelay): int
    {
        return min(((2 ** ($attempt - 1)) * $retryTimeout), $maxDelay);
    }

    private function isRetriable(Throwable $exception): bool
    {
        return $exception instanceof ConnectionException
            || (
                $exception instanceof RequestException &&
                $this->isRetriableStatusCode($exception->getCode()) &&
                $this->isRetriableMethod($exception->response->transferStats->getRequest()->getMethod())
            );
    }

    private function isRetriableMethod(string $method): bool
    {
        return in_array($method, [
            Request::METHOD_GET,
            Request::METHOD_HEAD,
            Request::METHOD_OPTIONS,
            Request::METHOD_TRACE,
        ], true);
    }

    private function isRetriableStatusCode(int $statusCode): bool
    {
        return in_array($statusCode, [
            Response::HTTP_REQUEST_TIMEOUT,
            Response::HTTP_CONFLICT,
            Response::HTTP_TOO_EARLY,
            Response::HTTP_INTERNAL_SERVER_ERROR,
            Response::HTTP_BAD_GATEWAY,
            Response::HTTP_SERVICE_UNAVAILABLE,
            Response::HTTP_GATEWAY_TIMEOUT,
        ], true);
    }

    public function withLoggingMiddleware(int $loggingMode = self::LOG_REQUESTS): self
    {
        $this->request->withMiddleware($this->getLoggingMiddleware($loggingMode));

        return $this;
    }

    /**
     * Generates a middleware function that logs request and response information based on the provided logging mode.
     *
     * @param int $loggingMode (LOG_REQUESTS, LOG_RESPONSES, LOG_ERRORS)
     *
     * @return callable
     */
    private function getLoggingMiddleware(int $loggingMode): callable
    {
        return function (callable $handler) use ($loggingMode): callable {
            return function (RequestInterface $request, array $options) use (
                $handler,
                $loggingMode
            ): PromiseInterface {
                $requestId = Str::uuid()->toString();

                return $handler($request, $options)->then(
                    function (ResponseInterface $response) use ($request, $loggingMode, $requestId) {
                        if ($loggingMode & self::LOG_RESPONSES) {
                            $responseContent = $response->getBody()->getContents();

                            if ($request->getMethod() === 'GET') {
                                $responseContent = substr($responseContent, 0, 500) . '....';
                            }

                            Log::info(
                                sprintf('Request | Response [%s] | %s : %s', $response->getStatusCode(), $request->getMethod(), $request->getUri()),
                                [
                                    'url' => (string)$request->getUri(),
                                    'method' => $request->getMethod(),
                                    'headers' => collect($request->getHeaders())->except($this->getExcludeHeadersList())->all(),
                                    'body' => (string)$request->getBody(),
                                    'response' => $responseContent,
                                    'request_id' => $requestId,
                                ]
                            );
                        }

                        if ($this->isResponseFailed($response->getStatusCode())) {
                            if ($loggingMode & self::LOG_ERRORS) {
                                Log::error(
                                    sprintf('Request Failed: %s %s', $request->getMethod(), $request->getUri()),
                                    [
                                        'url' => (string)$request->getUri(),
                                        'error' => Response::$statusTexts[$response->getStatusCode()],
                                        'request_id' => $requestId,
                                    ]
                                );
                            }
                        }

                        return $response;
                    },
                    function (Throwable $exception) use ($request, $loggingMode, $requestId) {
                        if ($loggingMode & self::LOG_ERRORS) {
                            Log::error(
                                sprintf('Request Connection Failed: %s %s', $request->getMethod(), $request->getUri()),
                                [
                                    'url' => (string)$request->getUri(),
                                    'error' => $exception->getMessage(),
                                    'request_id' => $requestId,
                                ]
                            );
                        }
                        throw $exception;
                    }
                );
            };
        };
    }

    public function isResponseFailed(int $statusCode = 200): bool
    {
        return !($statusCode >= 200 && $statusCode < 300);
    }

    public function withTrace(): self
    {
        $traceIdHeader = Config::get('app.trace_id_header', 'X-Trace-Id');
        $traceId = request()?->header($traceIdHeader);

        if ($traceId) {
            $this->request->withHeader($traceIdHeader, request()?->header($traceIdHeader));
        }

        return $this;
    }

    /**
     * @return PendingRequest
     */
    public function build(): PendingRequest
    {
        return $this->request->baseUrl($this->baseUrl);
    }

    public function withGamicorpSignatureMiddleware(): self
    {
        $this->request->withMiddleware($this->getGamicorpSignatureMiddleware())->withOptions(
            ['verify' => $this->httpVerify]
        );

        return $this;
    }

    private function getGamicorpSignatureMiddleware(): callable
    {
        return function ($handler): callable {
            return function (RequestInterface $request, array $options) use ($handler): PromiseInterface {
                $stream = $request->getBody();
                $body = $stream->getContents();

                $sign = sha1(base64_encode($body).config('auth.gamicorp_merchant_key'));

                $request = $request
                    ->withBody($stream)
                    ->withHeader('Content-Type', 'application/json')
                    ->withHeader('X-SIGNATURE', $sign);

                return $handler($request, $options);
            };
        };
    }
}
