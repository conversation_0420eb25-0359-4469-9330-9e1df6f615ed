<?php

declare(strict_types=1);

namespace App\Clients;

use App\Dto\ResponseOutputDto;
use App\Traits\ExcludeHeadersListTrait;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class HttpService
{
    use ExcludeHeadersListTrait;

    protected const GET = 'GET';

    protected const POST = 'POST';

    protected const PUT = 'PUT';

    protected const DELETE = 'DELETE';

    protected const PATCH = 'PATCH';

    /**
     * @var array<string, mixed>
     */
    protected array $query = [];

    /**
     * @var array<string, mixed>
     */
    protected array $json = [];

    /**
     * @var array<string, mixed>
     */
    protected array $headers = [];

    /**
     * @var array<string, mixed>
     */
    protected array $form = [];

    /**
     * @var array<string, mixed>
     */
    protected array $multipart = [];

    protected bool $logging = true;

    public function __construct(protected readonly Client $httpClient)
    {
    }

    protected function doRequest(string $requestType, string $url): ResponseOutputDto
    {
        //        if ($this->logging) {
        //            $this->logRequest($requestType, $url);
        //        }

        $result = new ResponseOutputDto();

        try {
            $params = [
                'query' => $this->query,
                'json' => $this->json,
                'form_params' => $this->form,
                'headers' => $this->headers,
                'multipart' => $this->multipart,
            ];

            foreach ($params as $k => $param) {
                if (empty($param)) {
                    unset($params[$k]);
                }
            }

            if ($requestType === self::GET) {
                $response = $this->httpClient->get($url, $params);
            }
            if ($requestType === self::POST) {
                $response = $this->httpClient->post($url, $params);
            }
            if ($requestType === self::PUT) {
                $response = $this->httpClient->put($url, $params);
            }
            if ($requestType === self::PATCH) {
                $response = $this->httpClient->patch($url, $params);
            }
            if ($requestType === self::DELETE) {
                $response = $this->httpClient->delete($url, $params);
            }
        } catch (ConnectException) {
            $result->isSuccess = false;
            $result->responseCode = Response::HTTP_SERVICE_UNAVAILABLE;
            $result->content['message'] = Response::$statusTexts[Response::HTTP_SERVICE_UNAVAILABLE];
        } catch (GuzzleException|RequestException $e) {
            if ($e instanceof RequestException && $e->hasResponse()) {
                $response = $e->getResponse();
            }
            $result->isSuccess = false;
            $result->responseCode = $e->getCode();
        }

        if (isset($response) && $response->getStatusCode() !== Response::HTTP_OK) {
            $result->isSuccess = false;
            $result->responseCode = $response->getStatusCode();
        }

        if (isset($response)) {
            $result->rawContent = $response->getBody()->getContents();
            /** @noinspection JsonEncodingApiUsageInspection */
            $result->content = json_decode($result->rawContent, true);

            if ($result->isSuccess === false) {
                $result->errorCode = $result->content['error_code'] ?? null;
            }
        }

        //        if ($this->logging) {
        //            $this->logResponse($requestType, $url, $result);
        //        }

        $this->reset();

        return $result;
    }

    public function doGetRequest(string $url): ResponseOutputDto
    {
        return $this->doRequest(self::GET, $url);
    }

    public function doPostRequest(string $url): ResponseOutputDto
    {
        return $this->doRequest(self::POST, $url);
    }

    public function doPutRequest(string $url): ResponseOutputDto
    {
        return $this->doRequest(self::PUT, $url);
    }

    public function doPatchRequest(string $url): ResponseOutputDto
    {
        return $this->doRequest(self::PATCH, $url);
    }

    public function doDeleteRequest(string $url): ResponseOutputDto
    {
        return $this->doRequest(self::DELETE, $url);
    }

    /**
     * @param array<string, mixed> $json
     * @return $this
     */
    public function setJson(array $json): self
    {
        $this->json = $json;

        return $this;
    }

    protected function logRequest(string $requestType, string $url): void
    {
        Log::info(
            "Request | $requestType: $url",
            [
                'query' => $this->query,
                'json' => $this->json,
                'form' => $this->form,
                'multipart' => $this->multipart,
                'headers' => collect($this->headers)->except($this->getExcludeHeadersList())->all(),
            ]
        );
    }

    public function enableLog(): self
    {
        $this->logging = true;

        return $this;
    }

    protected function logResponse(string $requestType, string $url, ResponseOutputDto $response): void
    {
        Log::info("Response [$response->responseCode] | $requestType: $url", [$response->rawContent]);
    }

    public function reset(): void
    {
        $this->query = [];
        $this->json = [];
        $this->headers = [];
        $this->form = [];
        $this->multipart = [];
        $this->logging = false;
    }
}
