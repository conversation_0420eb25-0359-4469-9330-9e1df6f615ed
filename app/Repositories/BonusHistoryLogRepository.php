<?php

namespace App\Repositories;
use App\Models\BonusHistoryLog;
use App\Services\Dto\BonusHistoryLogDTO;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

/**
 * Class BonusHistoryLogRepository
 */
class BonusHistoryLogRepository extends BaseRepository
{
    public function getLogsByBonusId(BonusHistoryLogDTO $dto): LengthAwarePaginator
    {
        $query = BonusHistoryLog::on('mysql.bonus')
            ->selectRaw('id, type, created_at, author_email, JSON_UNQUOTE(JSON_EXTRACT(data, "$.diff")) AS diff')
            ->where('bonus_id', $dto->id);

        return $this->paginate($query, $dto);
    }
}
