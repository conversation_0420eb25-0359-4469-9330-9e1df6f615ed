<?php

namespace App\Repositories;

use App\Models\BonusBalance;

/**
 * Class BonusBalanceRepository
 */
class BonusBalanceRepository extends BaseRepository
{
    /**
     * @param array<string, int|float|string> $searchParams
     * @param array<string, mixed> $data
     */
    public function updateBonusBalance(array $searchParams, array $data): int
    {
        $bonusBalanceQuery = BonusBalance::query();
        foreach ($searchParams as $key => $value) {
            $bonusBalanceQuery->where($key, $value);
        }

        return $bonusBalanceQuery->update($data);
    }
}
