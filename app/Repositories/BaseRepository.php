<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Services\Dto\PaginationDto;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

abstract class BaseRepository
{
    public function save(Model $model): bool
    {
        return $model->save();
    }

    public function remove(Model $model): ?bool
    {
        return $model->delete();
    }

    protected function paginate(Builder|QueryBuilder $query, PaginationDto $paginationDto): LengthAwarePaginator
    {
        $limit =  $paginationDto->pagination['limit'] ?? null;
        $page = $paginationDto->pagination['page'] ?? null;

        $sortField = 'id';
        $sortOrder = 'desc';
        if ($paginationDto->asc || $paginationDto->desc) {
            $sortField = $paginationDto->asc ?? $paginationDto->desc;
            $sortOrder = $paginationDto->asc ? 'asc' : 'desc';
        }
        $query->orderBy($sortField, $sortOrder);

        /** @var LengthAwarePaginator $paginatedList */
        $paginatedList = $query->paginate($limit, ['*'], 'pagination[page]', $page);

        return $paginatedList;
    }

    /**
     * @param array<array-key, mixed> $data
     *
     * @return array{data: Collection<int, mixed>, links: array<string, mixed>, meta: array<string, mixed>}
     */
    protected function collectionPaginator(array $data, PaginationDto $dto): array
    {
        $limit = $dto->pagination['limit'] ?? 15;
        $page = $dto->pagination['page'] ?? 1;

        $collection = collect($data);
        $currentPageItems = $collection->forPage((int)$page, $limit);

        $paginator = new LengthAwarePaginator(
            $currentPageItems,
            $collection->count(),
            $limit,
            $page,
            ['path' => url()->current()]
        );

        $from = ($paginator->currentPage() - 1) * $paginator->perPage() + 1;
        $to = min($from + $paginator->count() - 1, $paginator->total());

        return [
            'data' => $paginator->values(),
            'links' => [
                'first' => $paginator->url(1),
                'last' => $paginator->url($paginator->lastPage()),
                'prev' => $paginator->previousPageUrl(),
                'next' => $paginator->nextPageUrl(),
            ],
            'meta' => [
                'current_page' => $paginator->currentPage(),
                'from' => $from,
                'last_page' => $paginator->lastPage(),
                'path' => $paginator->path(),
                'per_page' => $paginator->perPage(),
                'to' => $to,
                'total' => $paginator->total(),
            ],
        ];
    }
}
