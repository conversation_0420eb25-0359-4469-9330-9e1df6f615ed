<?php

namespace App\Repositories;

use App\Models\BonusPlayerCard;
use Illuminate\Contracts\Database\Query\Builder;

class BonusPlayerCardRepository
{
    public function hasPlayerNotUsedBonusPlayerCard(int $playerId, int $bonusId): bool
    {
        return BonusPlayerCard::query()
            ->where('player_id', $playerId)
            ->where('bonus_id', $bonusId)
            ->where('is_used', false)
            ->exists();
    }

    public function hasBonusPlayerCard(int $playerId, int $bonusId): bool
    {
        return BonusPlayerCard::query()
            ->where('player_id', $playerId)
            ->where('bonus_id', $bonusId)
            ->exists();
    }

    public function notUsedBonusPlayerCardQuery(int $playerId, int $bonusId): Builder
    {
        return BonusPlayerCard::query()
            ->where('player_id', $playerId)
            ->where('bonus_id', $bonusId)
            ->where('is_used', false)
            ->limit(1);
    }
}
