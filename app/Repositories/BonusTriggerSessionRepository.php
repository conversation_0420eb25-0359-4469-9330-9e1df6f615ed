<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\BonusTriggerSession;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class BonusTriggerSessionRepository
 */
class BonusTriggerSessionRepository extends BaseRepository
{
    /**
     * @return Collection<int, BonusTriggerSession>
     */
    public function getSessionsByBonusId(int $bonusId, ?string $orderBy = null, ?string $direction = null): Collection
    {
        $builder =  BonusTriggerSession::query()->where('bonus_id', $bonusId);
        $builder->orderBy($orderBy ?? 'id', $direction ?? 'desc');

        return $builder->get();
    }
}
