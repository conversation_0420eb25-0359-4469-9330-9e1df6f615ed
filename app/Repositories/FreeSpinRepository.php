<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\FreeSpin;
use App\Models\FreeSpinBound;
use App\Services\Dto\FreeSpinSearchDto;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Class FreeSpinRepository
 */
class FreeSpinRepository extends BaseRepository
{
    /**
     * @return LengthAwarePaginator<FreeSpin>
     */
    public function getWithPagination(FreeSpinSearchDto $searchDto): LengthAwarePaginator
    {
        $query = FreeSpin::query();

        if ($searchDto->currency) {
            $query->where('currency', $searchDto->currency);
        }

        if (isset($searchDto->option)) {
            $query->whereHas('bounds', static function (Builder $query) use ($searchDto): void {
                if ($searchDto->option === 1) {
                    $query->whereNotNull('bonus_id');
                } else {
                    $query->whereNull('bonus_id');
                }
            });
        }

        if ($searchDto->supplier) {
            $query->where('supplier', $searchDto->supplier);
        }

        if ($searchDto->aggregator) {
            $query->where('aggregator', $searchDto->aggregator);
        }

        if ($searchDto->providerId) {
            $query->where('provider_id', $searchDto->providerId);
        }

        if ($searchDto->slotId) {
            $query->where('slot_id', $searchDto->slotId);
        }

        if ($searchDto->id) {
            $query->where('id', $searchDto->id);
        }

        if ($searchDto->name) {
            $query->where('name', $searchDto->name);
        }

        if ($searchDto->title) {
            $query->where('title', $searchDto->title);
        }

        if ($searchDto->type) {
            $query->where('type', $searchDto->type);
        }

        if ($searchDto->createdAtFrom) {
            $query->where('created_at', '>=', $searchDto->createdAtFrom);
        }

        if ($searchDto->createdAtTo) {
            $query->where('created_at', '<=', $searchDto->createdAtTo);
        }

        if ($searchDto->startAtFrom) {
            $query->where('start_at', '>=', $searchDto->startAtFrom);
        }

        if ($searchDto->startAtTo) {
            $query->where('start_at', '<=', $searchDto->startAtTo);
        }

        if ($searchDto->expiredAtFrom) {
            $query->where('expired_at', '>=', $searchDto->expiredAtFrom);
        }

        if ($searchDto->expiredAtTo) {
            $query->where('expired_at', '<=', $searchDto->expiredAtTo);
        }

        if ($searchDto->updatedAtFrom) {
            $query->where('updated_at', '>=', $searchDto->updatedAtFrom);
        }

        if ($searchDto->updatedAtTo) {
            $query->where('updated_at', '<=', $searchDto->updatedAtTo);
        }

        if ($searchDto->countFrom) {
            $query->where('count', '>=', $searchDto->countFrom);
        }

        if ($searchDto->countTo) {
            $query->where('count', '<=', $searchDto->countTo);
        }

        if ($searchDto->betFrom) {
            $query->where('bet', '>=', $searchDto->betFrom);
        }

        if ($searchDto->betTo) {
            $query->where('bet', '<=', $searchDto->betTo);
        }

        if ($searchDto->playerIdWith) {
            $query->whereHas('bounds', static function (Builder $query) use ($searchDto): void {
                $query->where('player_id', $searchDto->playerIdWith);
            });
        }

        if ($searchDto->authorId !== null) {
            $query->where('author_id', $searchDto->authorId);
        }

        if ($searchDto->updatedByAuthorId !== null) {
            $query->where('updated_by_author_id', $searchDto->updatedByAuthorId);
        }

        return $this->paginate($query, $searchDto);
    }

    public function getFreeSpinDetailById(int $id): FreeSpin
    {
        /** @var FreeSpin */
        return FreeSpin::query()->findOrFail($id);
    }

    public function getFirstFreeSpinById(int $id): ?FreeSpin
    {
        /** @var FreeSpin */
        return FreeSpin::query()->find($id);
    }

    public function getFreeSpinDetailByBonusId(int $bonusId): FreeSpin
    {
        $freespin = FreeSpin::query()->where('bonus_id', $bonusId)->firstOrFail();

        /** @var FreeSpin */
        return $this->getFreeSpinDetailById($freespin->id ?? null);
    }

    public function changeFreeSpinProcessStatus(int $id, int $status): void
    {
        FreeSpin::query()->where('id', $id)->update(['in_process' => $status]);
    }

    public function changeFreeSpinProcessStatusAndActivity(int $id, int $status): void
    {
        $activeFreeSpinForPlayerExists = FreeSpinBound::query()
            ->where('free_spin_id', $id)
            ->where('is_active', true)
            ->exists();

        $freeSpin = FreeSpin::query()->find($id);

        if ($activeFreeSpinForPlayerExists) {
            $freeSpin->update(['in_process' => $status, 'is_active' => false, 'status' => 'inactive']);
        } else {
            $freeSpin->update(['in_process' => $status]);
        }
    }

    /**
     * @param array<string, mixed> $data
     * @param array<int> $ids
     */
    public function updateFreeSpinsByIds(array $data, array $ids): int
    {
        return FreeSpin::query()->whereIn('id', $ids)->update($data);
    }

    public function checkFreeSpinName(string $name, ?int $id = null): bool
    {
        $freeSpinQuery = FreeSpin::query()->where('name', $name);

        if ($id) {
            $freeSpinQuery->where('id', '!=', $id);
        }

        return $freeSpinQuery->exists();
    }
}
