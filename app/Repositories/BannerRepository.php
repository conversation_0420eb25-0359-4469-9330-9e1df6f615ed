<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\Banner;
use App\Services\Dto\BannerEnabledDTO;
use App\Services\Dto\SearchBannersDTO;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class BannerRepository
 */
class BannerRepository extends BaseRepository
{
    public function getBannerById(int $id): ?Banner
    {
        /** @var Banner */
        return Banner::query()->find($id);
    }

    public function saveBanner(Banner $banner): Banner
    {
        $this->save($banner);

        return $banner;
    }

    /**
     * @param array<int> $ids
     * @param array<string, mixed> $data
     */
    public function updateBanners(array $ids, array $data): int
    {
        return Banner::query()->whereIn('id', $ids)->update($data);
    }

    public function getWithPagination(SearchBannersDTO $searchBannersDTO): LengthAwarePaginator
    {
        $bannerBuilder = Banner::query();

        if ($searchBannersDTO->id) {
            $bannerBuilder->where('id', $searchBannersDTO->id);
        }

        if ($searchBannersDTO->name) {
            $bannerBuilder->where('name', 'like', '%' . $searchBannersDTO->name . '%');
        }

        if ($searchBannersDTO->lang) {
            if (mb_strtolower($searchBannersDTO->lang) === 'all') {
                $bannerBuilder->whereNull('language');
            } else {
                $bannerBuilder->where('language', $searchBannersDTO->lang);
            }
        }

        if ($searchBannersDTO->country) {
            if (mb_strtolower($searchBannersDTO->country) === 'all') {
                $bannerBuilder->whereNull('country');
            } else {
                $bannerBuilder->where('country', $searchBannersDTO->country);
            }
        }

        if ($searchBannersDTO->type) {
            $bannerBuilder->where('type', $searchBannersDTO->type);
        }

        if ($searchBannersDTO->url) {
            $bannerBuilder->where('location', 'like', '%' . $searchBannersDTO->url . '%');
        }

        if ($searchBannersDTO->enabled !== null) {
            $bannerBuilder->where('enabled', $searchBannersDTO->enabled);
            if ($searchBannersDTO->enabled) {
                $bannerBuilder->where(static function ($query): void {
                    $query->whereNull('end_at')->orWhere('end_at', '>', Carbon::now());
                });
            }
        }

        if ($searchBannersDTO->dispositions) {
            $dispositions = array_unique(
                /** @phpstan-ignore-next-line */
                array_filter(explode(',', $searchBannersDTO->dispositions), 'strlen')
            );

            $bannerBuilder->where(static function ($query) use ($dispositions): void {
                foreach ($dispositions as $disposition) {
                    $query->orWhereJsonContains('disposition', $disposition);
                }
            });
        }

        return $this->paginate($bannerBuilder, $searchBannersDTO);
    }

    public function getEnabledBanners(BannerEnabledDTO $dto): LengthAwarePaginator
    {
        $bannerBuilder = Banner::query()
            ->where('enabled', true)
            ->where(static function (Builder $query): void {
                $query->whereNull('start_at')->orWhere('start_at', '<=', now());
            })
            ->where(static function (Builder $query): void {
                $query->whereNull('end_at')->orWhere('end_at', '>', now());
            });

        if ($dto->country) {
            $bannerBuilder->where(static function ($query) use ($dto): void {
                $query->whereNull('country')->orWhere('country', $dto->country);
            });
        }

        return $this->paginate($bannerBuilder, $dto);
    }

    /**
     * @param array<int> $ids
     *
     * @return Collection<int, Banner>
     */
    public function getBannersForChangingEnabled(array $ids, bool $moreThanNow = true): Collection
    {
        $bannerBuilder = Banner::query()
            ->whereIn('id', $ids);

        if ($moreThanNow) {
            $bannerBuilder->where(static function ($query): void {
                $query->whereNull('end_at')->orWhere('end_at', '>', Carbon::now());
            });
        } else {
            $bannerBuilder->where('end_at', '<=', Carbon::now());
        }

        /** @var Collection<int, Banner> $banners */
        $banners = $bannerBuilder->get();

        return $banners;
    }

    /**
     * @param array<int> $ids
     *
     * @return Collection<int, Banner>
     */
    public function getBannersByIds(array $ids): Collection
    {
        /** @var Collection<int, Banner> $banners */
        $banners = Banner::query()->whereIn('id', $ids)->get();

        return $banners;
    }
}
