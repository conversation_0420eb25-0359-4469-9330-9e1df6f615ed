<?php

namespace App\Repositories;

use App\Enums\BonusPlayerStatusEnum;
use App\Models\BonusPlayer;
use App\Models\BonusTriggerSession;
use Illuminate\Database\Eloquent\Builder;

class BonusPlayerRepository extends BaseRepository
{
    public function getSelectedBonusPlayerData(int $playerId): ?BonusPlayer
    {
        /** @var null|BonusPlayer */
        return BonusPlayer::query()
            ->where('player_id', $playerId)
            ->where('status', BonusPlayerStatusEnum::WAITING_FOR_DEPOSIT)
            ->first();
    }

    /**
     * @param array<string, BonusPlayerStatusEnum> $data
     */
    public function updateUnselectedBonusPlayer(int $playerId, array $data): int
    {
        return BonusPlayer::query()
            ->where('player_id', $playerId)
            ->where('status', BonusPlayerStatusEnum::UNSELECTED)
            ->update($data);
    }

    public function getBonusPlayerQueryWithSession(int $playerId, int $bonusId, ?BonusTriggerSession $currentTriggerSession = null): Builder
    {
        return BonusPlayer::bonusPlayerQuery($playerId, $bonusId)
            ->where('trigger_session_id', optional($currentTriggerSession)->id);
    }

    public function getUnselectedBonusPlayerData(int $playerId, int $bonusId): ?BonusPlayer
    {
        /** @var null|BonusPlayer */
        return BonusPlayer::bonusPlayerQuery($playerId, $bonusId)
            ->where('status', BonusPlayerStatusEnum::UNSELECTED)
            ->first();
    }

    public function isNorUnselectedBonusPlayerWithSession(int $playerId, int $bonusId, ?int $triggerSessionId = null): bool
    {
        $query = BonusPlayer::bonusPlayerQuery($playerId, $bonusId)
            ->where('status', '!=', BonusPlayerStatusEnum::UNSELECTED);

        if ($triggerSessionId) {
            $query->where('trigger_session_id', $triggerSessionId);
        } else {
            $query->whereNull('trigger_session_id');
        }

        return $query->exists();
    }

    public function isNorUnselectedAndActivatedBonusPlayer(int $playerId, int $bonusId, ?int $triggerSessionId = null): bool
    {
        $query = BonusPlayer::bonusPlayerQuery($playerId, $bonusId)
            ->whereNotIn('status', [BonusPlayerStatusEnum::UNSELECTED, BonusPlayerStatusEnum::ACTIVATED]);

        if ($triggerSessionId) {
            $query->where('trigger_session_id', $triggerSessionId);
        } else {
            $query->whereNull('trigger_session_id');
        }

        return $query->exists();
    }
}
