<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Enums\BonusTypeEnum;
use App\Enums\ProductTypeEnum;
use App\Models\Bonus;
use App\Models\BonusInfo;
use App\Models\BonusSlot;
use App\Models\BonusSlotProvider;
use App\Models\BonusTriggerSession;
use App\Services\Dto\ActiveWelcomeBonusesDto;
use App\Services\Dto\EnabledBonusesDTO;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use App\Services\Dto\BonusSearchDto;
use Carbon\Carbon;

/**
 * Class BonusRepository
 */
class BonusRepository extends BaseRepository
{
    public function getBonusById(int $bonusId): ?Bonus
    {
        return Cache::remember(
            "bonus_data_for_id_$bonusId",
            60,
            static fn() => Bonus::on('mysql.replica')->findOrFail($bonusId)
        );
    }

    public function getBonusForSmarticoById(int $bonusId): ?Bonus
    {
        return Cache::remember(
            "bonus_for_id_$bonusId",
            60,
            static fn() => Bonus::on('mysql.replica')->find($bonusId)
        );
    }

    public function getActualBonusById(int $bonusId): ?Bonus
    {
        /** @var null|Bonus $bonus */
        $bonus = Bonus::on('mysql.replica')->find($bonusId);

        return $bonus;
    }

    public function getBonus(int $bonusId): Bonus
    {
        /** @var Bonus */
        return Bonus::query()->findOrFail($bonusId);
    }

    public function getActiveBonusTriggerSessionAtTimeByBonusId(int $bonusId, string $time): ?BonusTriggerSession
    {
        /** @var BonusTriggerSession */
        return BonusTriggerSession::query()
            ->where('bonus_id', $bonusId)
            ->where('start_at', '<=', $time)
            ->where('end_at', '>=', $time)
            ->first();
    }

    public function getSmarticoBonusInfoByBonusId(int $bonusId): ?BonusInfo
    {
        return Cache::remember(
            "bonus_info_card_for_bonus_$bonusId",
            60,
            static fn() => BonusInfo::on('mysql.replica')
                ->where('is_for_smartico', true)
                ->where('bonus_id', $bonusId)->first()
        );
    }

    public function searchFilteredBonusesForBonusInfoPage(BonusSearchDto $searchDto): LengthAwarePaginator
    {
        $query = Bonus::on('mysql.replica')
            ->scopes(['withoutFreeSpinBonusType'])
            ->with(['bonusSlotProviders', 'bonusSlots', 'triggerSessions', 'freespin']);

        if ($searchDto->name) {
            $query->where('name', 'like', '%' . $searchDto->name . '%');
        }

        if ($searchDto->id) {
            $query->where('id', $searchDto->id);
        }

        if ($searchDto->currency) {
            $query->where('currency', $searchDto->currency);
        }

        if ($searchDto->type) {
            $type = mb_strtolower($searchDto->type);
            if ($type === 'welcome_group') {
                $query->isWelcomeGroupType();
            } elseif ($type === 'active_welcome_group') {
                $query->isWelcomeGroupType()->where('active_til', '>', Carbon::now());
            } elseif ($type === 'active_deposit') {
                $query->where('type', BonusTypeEnum::DEPOSIT->value)->whereHas('activeTriggerSessions');
            } else {
                $query->where('type', $type);
            }
        }

        if ($searchDto->productType !== null) {
            if ($searchDto->productType === ProductTypeEnum::CASINO->value) {
                $query->where('casino', true);
            } elseif ($searchDto->productType === ProductTypeEnum::SPORT->value) {
                $query->where('bets', true);
            }
        }

        if ($searchDto->casino !== null) {
            $query->where('casino', $searchDto->casino);
        }

        if ($searchDto->bets !== null) {
            $query->where('bets', $searchDto->bets);
        }

        if ($searchDto->active !== null) {
            $query->where('active', $searchDto->active);
        }

        if ($searchDto->isOrganic !== null) {
            $query->where('is_organic', $searchDto->isOrganic);
        }

        if ($searchDto->genreId !== null) {
            $genreIds = explode(',', $searchDto->genreId);
            $query->whereIn('genre_id', $genreIds);
        }

        if ($searchDto->slot_provider_id) {
            $slotProviderIds = explode(',', $searchDto->slot_provider_id);
            $query->whereHas('bonusSlotProviders', static function (Builder $builder) use ($slotProviderIds): void {
                $builder->whereIn('slot_provider_id', $slotProviderIds);
            });
        }

        if ($searchDto->slot_id) {
            $slotIds = explode(',', $searchDto->slot_id);
            $query->whereHas('bonusSlots', static function (Builder $builder) use ($slotIds): void {
                $builder->whereIn('slot_id', $slotIds);
            });
        }

        if ($searchDto->externalId) {
            $query->where('data->external_id', $searchDto->externalId);
        }

        return $this->paginate($query, $searchDto);
    }

    public function enabledBonuses(EnabledBonusesDTO $searchDto): LengthAwarePaginator
    {
        $query = Bonus::on('mysql.replica')->scopes(['withoutFreeSpinBonusType'])
            ->where('active', true);

        if ($searchDto->id) {
            $query->where('id', $searchDto->id);
        }

        if ($searchDto->currency) {
            $query->where('currency', $searchDto->currency);
        }

        if ($searchDto->type) {
            $query->where('type', $searchDto->type);
        }

        return $this->paginate($query, $searchDto);
    }

    public function getActiveWelcomeBonus(int $bonusId, string $currency = 'INR'): ?Bonus
    {
        return Bonus::on('mysql.replica')
            ->scopes(['isWelcomeGroupType'])
            ->where('currency', $currency)->where('id', $bonusId)
            ->where('active', true)
            ->where('is_promo', false)
            ->where('is_organic', false)
            ->where(static function (Builder $query): void {
                $query->where('active_from', '<=', Carbon::now())
                    ->orWhereNull('active_from');
            })
            ->where(static function (Builder $query): void {
                $query->where('active_til', '>', Carbon::now())
                    ->orWhereNull('active_til');
            })->first();
    }

    /**
     * @return Collection<int,Bonus>|LengthAwarePaginator
     */
    public function activeWelcomeBonuses(ActiveWelcomeBonusesDto $dto): Collection|LengthAwarePaginator
    {
        if ($dto->welcomeBonus) {
            $bonus = $this->getActiveWelcomeBonus($dto->welcomeBonus, $dto->currency);
            if ($bonus) {
                return new Collection([$bonus]);
            }
        }
        $query = Bonus::on('mysql.replica')
            ->scopes(['isWelcomeGroupType'])
            ->where('active', true)
            ->where('is_promo', false)
            ->where('is_organic', true)
            ->where(static function (Builder $query): void {
                $query->where('active_from', '<=', Carbon::now())
                    ->orWhereNull('active_from');
            })
            ->where(static function (Builder $query): void {
                $query->where('active_til', '>', Carbon::now())
                    ->orWhereNull('active_til');
            })->where('is_organic', true);

        if ($dto->currency) {
            $query->where('currency', $dto->currency);
        }

        $query->orderBy('weight')->orderBy('id', 'desc');

        return $this->paginate($query, $dto);
    }

    /**
     * @param array<int> $providerIds
     */
    public function syncSlotProviders(Bonus $bonus, array $providerIds): void
    {
        $existingProviderIds = $bonus->bonusSlotProviders->pluck('slot_provider_id')->toArray();
        $toAttach = array_diff($providerIds, $existingProviderIds);
        $toDetach = array_diff($existingProviderIds, $providerIds);

        if ($toDetach) {
            $bonus->bonusSlotProviders()->whereIn('slot_provider_id', $toDetach)->delete();
        }

        $bonus->bonusSlotProviders()->insert(array_map(static fn($providerId) => [
            'bonus_id' => $bonus->id,
            'slot_provider_id' => $providerId,
        ], $toAttach));
    }

    /**
     * @param array<int> $slotIds
     */
    public function syncSlots(Bonus $bonus, array $slotIds): void
    {
        $existingIds = $bonus->bonusSlots->pluck('slot_id')->toArray();
        $toAttach = array_diff($slotIds, $existingIds);
        $toDetach = array_diff($existingIds, $slotIds);

        if ($toDetach) {
            $bonus->bonusSlots()->whereIn('slot_id', $toDetach)->delete();
        }

        $bonus->bonusSlots()->insert(array_map(static fn($slotId) => [
            'bonus_id' => $bonus->id,
            'slot_id' => $slotId,
        ], $toAttach));
    }

    /**
     * @param array<int> $ids
     */
    public function removeBonuses(array $ids): mixed
    {
        return Bonus::query()->whereIn('id', $ids)->delete();
    }

    public function isBonusRelatedToSlot(int $bonusId, int $slotId): bool
    {
        return Cache::remember(
            "bonus_$bonusId" . "_related_to_slot_$slotId",
            1800,
            static fn() => BonusSlot::query()
                ->where('bonus_id', $bonusId)
                ->where('slot_id', $slotId)
                ->exists()
        );
    }

    /**
     * @return Collection<int, BonusSlot>
     */
    public function getBonusSlotsBySlotId(int $slotId): Collection
    {
        return BonusSlot::query()->where('slot_id', $slotId)->get();
    }

    /**
     * @return Collection<int, BonusSlotProvider>
     */
    public function getBonusSlotProvidersByBonusId(int $bonusId): Collection
    {
        return BonusSlotProvider::query()->where('bonus_id', $bonusId)->get();
    }
}
