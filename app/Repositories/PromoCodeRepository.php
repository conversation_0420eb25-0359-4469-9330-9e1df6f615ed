<?php

namespace App\Repositories;

use App\Enums\BonusTypeEnum;
use App\Models\Bonus;
use App\Models\PlayerPromoCodePivot;
use App\Models\PromoCode;
use App\Services\Dto\BonusBalanceDTO;
use App\Services\Dto\PlayerPromoCodesDTO;
use App\Services\Dto\PromoCodeSearchDTO;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PromoCodeRepository extends BaseRepository
{
    public function list(PromoCodeSearchDTO $dto): LengthAwarePaginator
    {
        $query = PromoCode::query();

        if ($dto->id) {
            $query->where('id', $dto->id);
        }

        if ($dto->welcome) {
            $query->whereHas('bonus', static function (Bonus|Builder $query): void {
                $query->isWelcomeGroupType();
            });
        }

        if ($dto->noDep) {
            $query->whereHas('bonus', static function ($query): void {
                $query->where('type', BonusTypeEnum::NO_DEP->value);
            });
        }

        $query->when($dto->code, static function ($query) use ($dto): void {
            $query->where('code', $dto->code);
        });

        return $this->paginate($query, $dto);
    }

    /**
     * @return Collection<array-key, PromoCode>
     */
    public function playerPromoCodes(PlayerPromoCodesDTO $dto): Collection
    {
        $query = PromoCode::query()->whereHas('promoCodePlayers', static function ($query) use ($dto): void {
            $query->where('player_id', $dto->playerId);
        });

        if ($dto->bonusId) {
            $query->where('bonus_id', $dto->bonusId);
        }

        if ($dto->isAlanbase !== null) {
            $query->where('is_alanbase', $dto->isAlanbase);
        }

        if ($dto->isActive !== null) {
            $query->where('is_active', $dto->isActive);
        }

        /** @var Collection<array-key, PromoCode> $promoCodes */
        $promoCodes = $query->get();

        return $promoCodes;
    }

    public function getPromoCode(string $code): ?PromoCode
    {
        /** @var PromoCode */
        return PromoCode::query()->where([
            ['code', $code],
            ['client_id', config('app.special_client_id')],
        ])->first();
    }

    /**
     * @return array<int>
     */
    public function getPromoCodeIdsByPlayerId(int $playerId): array
    {
        return PlayerPromoCodePivot::query()
            ->where('player_id', $playerId)
            ->pluck('promocode_id')->toArray();
    }

    public function getPromoCodeById(int $id): ?PromoCode
    {
        /** @var PromoCode */
        return PromoCode::query()->find($id);
    }

    public function attachPlayer(int $promoCodeId, int $playerId): Builder|Model
    {
        return PlayerPromoCodePivot::query()->create([
            'player_id' => $playerId,
            'promocode_id' => $promoCodeId,
        ]);
    }

    public function attachBonusBalance(BonusBalanceDTO $dto): void
    {
        $promoCodeIds = $this->getPromoCodeIdsByPlayerId($dto->playerId);

        PromoCode::query()
            ->whereIn('id', $promoCodeIds)
            ->where('bonus_id', $dto->bonusId)->get()
            ->map(static fn(PromoCode $promoCode) => DB::table('promocodes_balances')->insert([
                'promocode_id' => $promoCode->id,
                'bonus_balance_id' => $dto->id,
                'uuid' => Str::uuid()->toString(),
            ]));
    }

    public function getAlanbasePromoCodeByPlayerId(int $playerId): ?PromoCode
    {
        /** @var PromoCode */
        return PromoCode::query()
            ->where('is_alanbase', true)
            ->whereHas('promoCodePlayers', static function (Builder $query) use ($playerId): void {
                $query->where('player_id', $playerId);
            })
            ->first('code');
    }
}
