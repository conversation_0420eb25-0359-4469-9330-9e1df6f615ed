<?php

namespace App\Repositories;

use App\Models\FreeSpinHistoryLog;
use App\Services\Dto\FreeSpinHistoryLogDTO;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

/**
 * Class FreeSpinHistoryLogRepository
 */
class FreeSpinHistoryLogRepository extends BaseRepository
{
    public function getLogsByFreeSpinId(FreeSpinHistoryLogDTO $dto): LengthAwarePaginator
    {
        $query = FreeSpinHistoryLog::on('mysql.bonus')
            ->selectRaw('id, type, created_at, author_email, JSON_UNQUOTE(JSON_EXTRACT(data, "$.diff")) AS diff')
            ->where('free_spin_id', $dto->id);

        return $this->paginate($query, $dto);
    }
}
