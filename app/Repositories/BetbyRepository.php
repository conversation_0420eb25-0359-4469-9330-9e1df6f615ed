<?php

namespace App\Repositories;

use App\Services\Dto\BetbySearchDTO;

class BetbyRepository extends BaseRepository
{
    /**
     * @param array<array-key, mixed> $betbyResponse
     *
     * @return array<array-key, mixed>
     */
    public function getFilteredData(array $betbyResponse, BetbySearchDTO $dto): array
    {
        $response = $betbyResponse;

        if ($dto->id) {
            $response = array_values(
                array_filter(
                    $response,
                    static fn(array $template) => isset($template['id']) && $template['id'] === $dto->id
                )
            );
        }

        if ($dto->isActive !== null) {
            $response = array_values(
                array_filter(
                    $betbyResponse,
                    static fn(array $template)
                        => isset($template['is_active']) && $template['is_active'] === $dto->isActive
                )
            );
        }

        if ($dto->type) {
            $response = array_values(
                array_filter(
                    $response,
                    static fn(array $template)
                        => isset($template['freebet_data']) && $template['freebet_data']['type'] === $dto->type
                )
            );
        } else {
            $response = array_values(
                array_filter(
                    $response,
                    static fn(array $template) => in_array(
                        $template['freebet_data']['type'] ?? null,
                        ['free_money', 'bet_refund', 'snr'],
                        true
                    )
                )
            );
        }

        return $this->collectionPaginator($response, $dto);
    }
}
