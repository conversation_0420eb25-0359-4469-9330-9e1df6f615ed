<?php

namespace App\Repositories;

use App\Models\Bonus;
use App\Models\BonusInfo;
use App\Models\BonusPlayerCard;
use App\Services\ActiveBonusesCacheService;
use App\Services\Dto\ActiveBonusesInfoDTO;
use App\Services\Dto\BonusInfoSearchDTO;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;

/**
 * Class BonusInfoRepository
 */
class BonusInfoRepository extends BaseRepository
{
    public function __construct(
        private readonly ActiveBonusesCacheService $activeBonusesCacheService,
    ) {
    }

    /**
     * @return Collection<int, BonusInfo>
     */
    public function getActiveBonusesInfoData(): Collection
    {
        return Cache::remember(
            'active_bonus_info_list',
            60,
            static fn() => BonusInfo::on('mysql.replica')->with(['bonus', 'bonus.triggerSessions'])
                ->where('visible_to', '>', Carbon::now())
                ->where('active', true)
                ->where('is_for_smartico', false)
                ->orderBy('is_welcome', 'desc')
                ->orderBy('sort_order')
                ->orderBy('id')
                ->get()
        );
    }

    /**
     * @return Collection<int, BonusInfo>
     */
    public function getEnabledBonusesInfo(ActiveBonusesInfoDTO $dto): Collection
    {
        $bonusesInfoQuery = BonusInfo::on('mysql.replica')->with(['bonus', 'bonus.triggerSessions', 'bonus.freespin'])
            ->where('is_for_smartico', false)
            ->where('currency', $dto->currency)
            ->where('visible_to', '>', Carbon::now())
            ->where('active', true);

        if ($dto->welcomeBonus) {
            $bonusesInfoQuery->where(static function (Builder $builder) use ($dto): void {
                $builder->whereHas('bonus', static function (Builder $builder) use ($dto): void {
                    $builder->where(static function (Bonus|Builder $builder) use ($dto): void {
                        $builder->where('id', (int)$dto->welcomeBonus);
                        $builder->where('is_organic', false);
                        $builder->isWelcomeGroupType();
                    })->orWhere(static function (Bonus|Builder $builder): void {
                        $builder->where('is_organic', true);
                        $builder->isNotWelcomeGroupType();
                    });
                })->orWhereDoesntHave('bonus');
            });
        } else {
            $bonusesInfoQuery->where(static function (Builder $builder): void {
                $builder->whereHas('bonus', static function (Builder $builder): void {
                    $builder->where('is_organic', true);
                });
                $builder->orWhereDoesntHave('bonus');
            });
        }

        /** @var Collection<int, BonusInfo> $bonusesInfo */
        $bonusesInfo = $bonusesInfoQuery->orderBy('is_welcome', 'desc')
            ->orderBy('sort_order')
            ->orderBy('id')
            ->get();

        return $bonusesInfo;
    }

    /**
     * @return Collection<int, BonusInfo>
     */
    public function getActiveBonuses(ActiveBonusesInfoDTO $dto, ?string $playerUUID = null): Collection
    {
        $playerOrganicBonus = $this->activeBonusesCacheService->getFirstPlayerOrganicBonus($playerUUID);

        if ($playerOrganicBonus) {
            $welcomeBonusList = $this->activeBonusesCacheService->getNonOrganicWelcomeBonusIds(
                $playerOrganicBonus->bonus_id
            );
        } else {
            $welcomeBonusList = $this->activeBonusesCacheService->getOrganicWelcomeBonusIds();
        }

        $notSmarticoBonusInfo = $this->activeBonusesCacheService->getActiveBonusInfoNotForSmarticoIds($dto->currency);
        $smarticoBonusInfoIds = $this->activeBonusesCacheService->getActiveBonusInfoForSmarticoIds($dto->currency);
        $notUsedPlayerCards = $this->getNotUsedPlayerCards($dto->playerId, $smarticoBonusInfoIds);

        $builder = BonusInfo::on('mysql.replica')->with([
            'bonus',
            'bonus.triggerSessions:start_at,end_at,id,bonus_id',
            'bonus.freespin:count',
        ])
            ->where(
                static fn(Builder $builder) => $builder->whereIn('id', $notSmarticoBonusInfo)
                    ->orWhereIn('id', $notUsedPlayerCards)
            )->where(
                static fn(Builder $builder) => $builder->whereHas(
                    'bonus',
                    static fn(Builder $builder) => $builder->where(
                        static function (Bonus|Builder $builder) use ($welcomeBonusList): void {
                            $builder->isNotWelcomeGroupType();
                            $builder->orWhereIn('id', $welcomeBonusList);
                        }
                    )
                )
                    ->orWhereDoesntHave('bonus')
            );

        $builder->when($dto->id, static function (Builder $builder) use ($dto): void {
            $builder->where('id', $dto->id);
        })->when($dto->bonusId, static function (Builder $builder) use ($dto): void {
            $builder->where('bonus_id', $dto->bonusId);
        })->when($dto->casino !== null, static function (Builder $builder) use ($dto): void {
            $builder->where('casino', $dto->casino);
        })->when($dto->visibleTo, static function (Builder $builder) use ($dto): void {
            $builder->where('visible_to', '<', Carbon::make($dto->visibleTo));
        })->when($dto->visibleFrom, static function (Builder $builder) use ($dto): void {
            $builder->where('visible_from', '>', Carbon::make($dto->visibleFrom));
        });

        /** @var Collection<int, BonusInfo> $activeBonuses */
        $activeBonuses = $builder->where('active', true)
            ->where('currency', $dto->currency)
            ->orderBy('is_welcome', 'desc')
            ->orderBy('sort_order')
            ->orderBy('id')
            ->get();

        return $activeBonuses;
    }

    /**
     * @param array<int> $bonusInfoIds
     *
     * @return array<int>
     */
    private function getNotUsedPlayerCards(int $playerId, array $bonusInfoIds): array
    {
        return BonusPlayerCard::query()
            ->select('bonus_info_id')
            ->where('player_id', $playerId)
            ->where('is_used', false)
            ->whereIn('bonus_info_id', $bonusInfoIds)
            ->pluck('bonus_info_id')->all();
    }

    public function getBonusInfoById(int $id): ?BonusInfo
    {
        /** @var BonusInfo */
        return BonusInfo::query()->find($id);
    }

    public function searchBonusesInfo(BonusInfoSearchDTO $dto): LengthAwarePaginator
    {
        $queryBuilder = BonusInfo::on('mysql.replica')->with(['bonus']);

        if ($dto->active !== null) {
            $queryBuilder->where('active', $dto->active);
        }

        if ($dto->casino !== null) {
            $queryBuilder->where('casino', $dto->casino);
        }

        if ($dto->isForSmartico !== null) {
            $queryBuilder->where('is_for_smartico', $dto->isForSmartico);
        }

        if ($dto->currency) {
            $queryBuilder->where('currency', $dto->currency);
        }

        if ($dto->name) {
            $queryBuilder->where('name', 'like', '%' . $dto->name . '%');
        }

        if ($dto->button) {
            if ($dto->button === 'get_bonus') {
                $queryBuilder->whereNull('proceed_link')
                    ->whereNotNull('bonus_id');
            } elseif ($dto->button === 'proceed') {
                $queryBuilder->whereNotNull('proceed_link')
                    ->whereNull('bonus_id');
            } else {
                $queryBuilder->whereNull('proceed_link')
                    ->whereNull('bonus_id');
            }
        }

        if ($dto->type || $dto->playerId) {
            $queryBuilder->whereHas('bonus', static function ($query) use ($dto): void {
                if ($dto->type) {
                    $query->where('type', $dto->type);
                }
                if ($dto->playerId) {
                    $query->whereHas('bonusPlayers', static function ($query) use ($dto): void {
                        $query->where('player_id', $dto->playerId);
                    });
                }
            });
        }

        if ($dto->bonusId) {
            $queryBuilder->where('bonus_id', $dto->bonusId);
        }

        if ($dto->id) {
            $queryBuilder->where('id', $dto->id);
        }

        if ($dto->visibleTo) {
            $queryBuilder->where('visible_to', '>', Carbon::createFromTimestamp($dto->visibleTo));
        }

        if ($dto->visibleFrom) {
            $queryBuilder->where('visible_from', '<', Carbon::createFromTimestamp($dto->visibleFrom));
        }

        return $this->paginate($queryBuilder, $dto);
    }

    public function isBonusForSmartico(int $bonusId): bool
    {
        return BonusInfo::query()
            ->where('bonus_id', $bonusId)
            ->where('is_for_smartico', true)
            ->exists();
    }
}
