<?php

namespace App\Repositories;

use App\Models\SmarticoFreeSpin;
use App\Services\Dto\SmarticoIssueFreeSpinToPlayerDTO;

class SmarticoFreeSpinRepository extends BaseRepository
{
    public function fillSmarticoFreeSpinData(SmarticoIssueFreeSpinToPlayerDTO $dto): void
    {
        $smarticoFreeSpin = new SmarticoFreeSpin();
        $smarticoFreeSpin->fill($dto->toArray(false));

        $this->save($smarticoFreeSpin);
    }

    public function isFreeSpinAlreadyTransferredToPlayer(string $smarticoFreeSpinId): bool
    {
        return SmarticoFreeSpin::query()
            ->where('smartico_freespin_id', $smarticoFreeSpinId)
            ->exists();
    }
}
