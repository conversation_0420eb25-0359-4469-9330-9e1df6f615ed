<?php

namespace App\Repositories;

use App\Models\FreeSpin;
use App\Models\FreeSpinBound;
use App\Services\Dto\GetFreeSpinBoundsDTO;
use App\Services\Dto\PlayerBoundSearchDTO;
use App\Services\Dto\UpdateBoundDTO;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use App\Services\Dto\FreeSpinBoundSearchDTO;
use App\Services\PlayerService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Class FreeSpinBoundRepository
 */
class FreeSpinBoundRepository extends BaseRepository
{
    public function __construct(
        private readonly PlayerService $playerService
    ) {
    }

    public function getFreeSpinDetailByBoundId(int $freespinBoundId): ?FreeSpin
    {
        $freespinBound = FreeSpinBound::query()->findOrFail($freespinBoundId);

        return (new FreeSpinRepository())->getFreeSpinDetailById($freespinBound->free_spin_id ?? 0);
    }

    public function getFreeSpinBoundDetailById(int $id): FreeSpinBound
    {
        /** @var FreeSpinBound */
        return FreeSpinBound::on('mysql.replica')->findOrFail($id);
    }

    public function getFreeSpinBoundById(int $id): ?FreeSpinBound
    {
        /** @var FreeSpinBound */
        return FreeSpinBound::on('mysql.replica')->find($id);
    }

    /**
     * @return array<int>
     */
    public function getAllNotSentBoundsByFreespinId(int $freespinId): array
    {
        return FreeSpinBound::query()->where('free_spin_id', $freespinId)
            ->where('is_active', 0)
            ->whereNull('canceled_at')
            ->pluck('id')->toArray();
    }

    /**
     * @return Collection<int, FreeSpinBound>
     */
    public function getPlayerActiveFreeSpinBounds(PlayerBoundSearchDTO $dto): Collection
    {
        $freeSpinBoundsQuery = FreeSpinBound::on('mysql.replica')->with(['freespinBonusSlot', 'freespin'])
            ->where('player_id', $dto->playerId)
            ->where('is_active', 1)
            ->where('is_archived', 0)
            ->whereNull('canceled_at');

        if ($dto->createdAtTo) {
            $freeSpinBoundsQuery->where('created_at', '>', Carbon::createFromTimestamp($dto->createdAtTo));
        }

        if ($dto->createdAtFrom) {
            $freeSpinBoundsQuery->where('created_at', '<', Carbon::createFromTimestamp($dto->createdAtFrom));
        }

        return $freeSpinBoundsQuery->get();
    }

    /**
     * @return Collection<int, FreeSpinBound>
     */
    public function getPlayerArchivedFreeSpinBounds(PlayerBoundSearchDTO $dto): Collection
    {
        $freeSpinBoundsQuery = FreeSpinBound::on('mysql.replica')->with(['freespinBonusSlot', 'freespin'])
            ->where('player_id', $dto->playerId)
            ->where('is_active', 0)
            ->where('is_archived', 1);

        if ($dto->createdAtTo) {
            $freeSpinBoundsQuery->where('created_at', '<', Carbon::createFromTimestamp($dto->createdAtTo));
        }

        if ($dto->createdAtFrom) {
            $freeSpinBoundsQuery->where('created_at', '>', Carbon::createFromTimestamp($dto->createdAtFrom));
        }

        return $freeSpinBoundsQuery->get();
    }

    /**
     * @return Collection<int, FreeSpinBound>
     */
    public function getPlayersByFreeSpinId(int $freeSpinId): Collection
    {
        return FreeSpinBound::on('mysql.replica')
            ->where('free_spin_id', $freeSpinId)->get();
    }

    /**
     * @return Collection<int, FreeSpinBound>
     */
    public function getLimitedPlayersByFreeSpinId(int $freeSpinId, int $limit = 5): Collection
    {
        /** @var Collection<int, FreeSpinBound> $freeSpinBounds */
        $freeSpinBounds = FreeSpinBound::on('mysql.replica')
            ->where('free_spin_id', $freeSpinId)->limit($limit)->get();

        return $freeSpinBounds;
    }

    /**
     * @param array<string, mixed> $data
     * @param array<int> $ids
     */
    public function updateFreeSpinBounds(array $data, array $ids): int
    {
        return FreeSpinBound::query()->whereIn('id', $ids)->update($data);
    }

    /**
     * @return Collection<int, FreeSpinBound>
     */
    public function getFreeSpinBoundsByFreespinId(int $freespinId): Collection
    {
        return FreeSpinBound::on('mysql.replica')->where('free_spin_id', $freespinId)->get();
    }

    /**
     * @param array<int> $ids
     */
    public function removeFreeSpinBoundsByFreeSpinIds(array $ids): mixed
    {
        return FreeSpinBound::query()->whereIn('free_spin_id', $ids)->delete();
    }

    public function existFreeSpinBoundByBonusId(int $bonusId): bool
    {
        return FreeSpinBound::query()->wherehas('freespin', static function ($builder) use ($bonusId): void {
            $builder->where('bonus_id', $bonusId);
        })->exists();
    }

    public function search(FreeSpinBoundSearchDTO $dto): LengthAwarePaginator
    {
        $query = FreeSpinBound::on('mysql.replica')
            ->where('free_spin_id', $dto->id);

        if ($dto->playerUuid) {
            $player = $this->playerService->getFirstPlayerByUUID($dto->playerUuid);

            $query->where('player_id', $player?->id);
        }

        return $this->paginate($query, $dto);
    }

    /**
     * @return Collection<int, FreeSpinBound>
     */
    public function searchFreeSpinBoundsWithoutPagination(GetFreeSpinBoundsDTO $dto): Collection
    {
        $query = FreeSpinBound::query();

        if ($dto->playerId) {
            $query->where('player_id', $dto->playerId);
        }

        if ($dto->ids) {
            $query->whereIn('id', $dto->ids);
        }

        if ($dto->currency || $dto->isActive !== null || $dto->slotId) {
            if ($dto->isActive !== null) {
                $query->where('is_active', $dto->isActive);
            }
            $query->whereHas('freespin', static function (Builder $builder) use ($dto): void {
                if ($dto->isActive !== null) {
                    $builder->where('is_active', $dto->isActive);
                }
                if ($dto->slotId) {
                    $builder->where('slot_id', $dto->slotId);
                }
                if ($dto->currency) {
                    $builder->where('currency', $dto->currency);
                }
                if ($dto->isActive) {
                    $now = Carbon::now();
                    $builder->where('expired_at', '>', $now)
                        ->where('start_at', '<', $now);
                }
            });
        }

        return $query->get();
    }

    public function updateBound(UpdateBoundDTO $dto): FreeSpinBound
    {
        /** @var FreeSpinBound $bound */
        $bound = FreeSpinBound::query()->findOrFail($dto->id);

        $data = $dto->getDataForUpdate();
        if ($data) {
            $bound->update($data);
        }

        return $bound;
    }

    public function hasFreeSpinActiveBound(int $id): bool
    {
        return FreeSpinBound::query()
            ->where('free_spin_id', $id)
            ->where('is_active', true)
            ->exists();
    }
}
