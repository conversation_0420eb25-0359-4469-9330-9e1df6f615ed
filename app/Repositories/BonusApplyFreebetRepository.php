<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\MassFreeBetBonusInfo;
use App\Services\Dto\BonusApplySearchDTO;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class BonusApplyFreebetRepository extends BaseRepository
{
    public function getFreeBetBonusDetail(int $id): MassFreeBetBonusInfo
    {
        /** @var MassFreeBetBonusInfo */
        return MassFreeBetBonusInfo::query()->findOrFail($id);
    }

    public function list(BonusApplySearchDTO $dto): LengthAwarePaginator
    {
        $query = MassFreeBetBonusInfo::query()->where('bonus_id', $dto->bonusId);

        if ($dto->status) {
            $query->where('status', $dto->status);
        }

        if ($dto->startAt) {
            $query->where('created_at', '>=', $dto->startAt);
        }

        if ($dto->endAt) {
            $query->where('created_at', '<=', $dto->endAt);
        }

        return $this->paginate($query, $dto);
    }

    public function isBonusAlreadyTransferredToPlayer(int $smarticoBonusId): bool
    {
        return MassFreeBetBonusInfo::query()
            ->where('smartico_bonus_id', (string)$smarticoBonusId)
            ->where('status', 'success')
            ->exists();
    }
}
