<?php

namespace App\Models;

use App\Models\Traits\HasUuid;
use DateTimeImmutable;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Contracts\Database\Query\Builder as BuilderContract;

/**
 * @property int $id
 * @property int $client_id
 * @property string $name
 * @property string $code
 * @property null|int $stream_id
 * @property int $bonus_id
 * @property null|string $description
 * @property null|bool $is_active
 * @property null|int $use_limit
 * @property int $uses
 * @property array<string, string> $condition
 * @property null|bool $is_alanbase
 * @property null|DateTimeImmutable $start_at
 * @property null|DateTimeImmutable $end_at
 * @property DateTimeImmutable $created_at
 * @property DateTimeImmutable $updated_at
 * @property string $uuid
 * @property-read Bonus $bonus
 * @property-read Collection<int, BonusBalance> $balances
 * @property-read int|null $balances_count
 * @property-read Collection<int, PlayerPromoCodePivot> $promoCodePlayers
 * @property-read null|int $promo_code_players_count
 */
class PromoCode extends Model
{
    use HasUuid;

    protected $table = 'promocodes';

    protected $fillable = [
        'client_id',
        'name',
        'code',
        'stream_id',
        'bonus_id',
        'description',
        'is_active',
        'use_limit',
        'start_at',
        'end_at',
        'condition',
        'is_alanbase',
    ];

    /**
     * @var string[]
     */
    protected $casts = [
        'is_active' => 'bool',
        'condition' => 'array',
        'is_alanbase' => 'bool',
        'start_at' => 'datetime',
        'end_at' => 'datetime',
    ];

    public function bonus(): BelongsTo|BuilderContract
    {
        return $this->belongsTo(Bonus::class, 'bonus_id', 'id');
    }

    public function promoCodePlayers(): HasMany
    {
        return $this->hasMany(PlayerPromoCodePivot::class, 'promocode_id', 'id');
    }

    public function balances(): BelongsToMany
    {
        return $this->belongsToMany(BonusBalance::class, 'promocodes_balances', 'promocode_id', 'bonus_balance_id', 'id', 'id');
    }

    public function isValidBonusAndPromoCode(): bool
    {
        $bonus = $this->bonus;

        return $bonus->isValidTimeBonus()
            && $bonus->active
            && $this->is_active
            && $this->isValidTimePromoCode()
            && (!$this->use_limit || $this->use_limit - 1 >= $this->uses);
    }

    public function isValidTimePromoCode(): bool
    {
        return time() > $this->start_at->getTimestamp()
            && (!$this->end_at || time() < $this->end_at->getTimestamp());
    }
}
