<?php

declare(strict_types=1);

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;

/**
 * Class MassOnetimeBonusInfo
 *
 * @property int $id
 * @property int $bonus_id
 * @property int $player_id
 * @property string $email
 * @property string $smartico_bonus_id
 * @property int $amount
 * @property string $status
 * @property string $text
 * @property DateTimeInterface $created_at
 * @property DateTimeInterface $updated_at
 * @method static findOrFail(int $id)
 */
final class MassOnetimeBonusInfo extends Model
{
    /**
     * @var string
     */
    protected $connection = 'mysql.admin';

    protected $table = 'mass_onetime_bonus_info';
}
