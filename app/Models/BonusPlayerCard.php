<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * Class BonusPlayerCard
 *
 * @mixin Builder
 *
 * @property int $id
 * @property int $player_id
 * @property int $bonus_id
 * @property int $bonus_info_id
 * @property bool $is_used
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Bonus $bonus
 */
class BonusPlayerCard extends Model
{
    protected $table = 'bonus_player_card';

    public function bonus(): BelongsTo
    {
        return $this->belongsTo(Bonus::class, 'bonus_id', 'id');
    }
}
