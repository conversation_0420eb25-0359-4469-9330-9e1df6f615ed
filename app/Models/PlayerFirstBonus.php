<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $player_uuid
 * @property int $bonus_id
 * @property bool $is_from_link
 */
class PlayerFirstBonus extends Model
{
    protected $table = 'player_first_bonus';

    /**
     * @var string[]
     */
    protected $casts = [
        'is_from_link' => 'boolean',
    ];

    protected $fillable = [
        'player_uuid',
        'bonus_id',
        'is_from_link'
    ];
}
