<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Traits\ImageLinkFixerTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

/**
 * Class Banner
 *
 * @property int $id
 * @property int $client_id
 * @property string $name
 * @property string|null $language
 * @property string|null $country
 * @property bool|null $mobile_apk_install
 * @property string $platform
 * @property string $is_for_signed_in
 * @property int|null $weight
 * @property string|null $location
 * @property array<string, string>|null $disposition
 * @property string|null $image
 * @property string $type
 * @property bool|null $enabled
 * @property bool $is_active
 * @property string|null $smartico_segment_id
 * @property Carbon|null $start_at
 * @property Carbon|null $end_at
 * @property string|null $uuid
 */
class Banner extends Model
{
    use ImageLinkFixerTrait;

    protected $table = 'banners';
    protected $primaryKey = 'id';

    /**
     * @var string[]
     */
    protected $casts = [
        'disposition' => 'array',
        'enabled' => 'boolean',
        'mobile_apk_install' => 'boolean'
    ];

    /**
     * @var array|string[]
     */
    protected array $dates = [
        'end_at',
        'start_at',
    ];

    protected $fillable = [
        'client_id',
        'name',
        'language',
        'country',
        'is_for_signed_in',
        'location',
        'weight',
        'mobile_apk_install',
        'platform',
        'disposition',
        'image',
        'type',
        'enabled',
        'smartico_segment_id',
        'start_at',
        'end_at'
    ];

    public static function boot(): void
    {
        parent::boot();

        static::creating(function($banner) {
            $banner->uuid = (string) Str::uuid();
        });
    }

    public function getIsActiveAttribute(): bool
    {
        return $this->attributes['enabled'] && false === $this->isExpired();
    }

    public function isExpired(): bool
    {
        if (!$this->attributes['end_at'] || Carbon::parse($this->attributes['end_at']) > Carbon::now()) {
            return false;
        }

        return true;
    }
}
