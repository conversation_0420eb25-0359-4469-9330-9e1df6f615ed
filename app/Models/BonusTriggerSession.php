<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

/**
 * Class BonusTriggerSession
 *
 * @property int $id
 * @property int $bonus_id
 * @property DateTimeInterface $start_at
 * @property DateTimeInterface $end_at
 */
final class BonusTriggerSession extends Model
{
    protected $table = 'bonus_trigger_sessions';

    protected $primaryKey = 'id';

    /**
     * @var string[]
     */
    protected $casts = [
        'bonus_id' => 'integer',
    ];

    /**
     * @var array|string[]
     */
    protected array $dates = [
        'start_at',
        'end_at',
    ];

    protected $hidden = [
        'id',
        'bonus_id',
        'created_at',
        'updated_at',
    ];

    public static function boot(): void
    {
        parent::boot();

        self::creating(static function ($bonusTriggerSession): void {
            $bonusTriggerSession->uuid = (string)Str::uuid();
        });
    }

    protected function serializeDate(DateTimeInterface $date): string
    {
        return (string)$date->getTimestamp();
    }
}
