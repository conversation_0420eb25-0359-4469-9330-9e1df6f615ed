<?php

declare(strict_types=1);

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $bonus_id
 * @property int $template_id
 * @property int $player_id
 * @property string $external_player_id
 * @property string $smartico_bonus_id
 * @property string $currency
 * @property int $amount
 * @property string $status
 * @property string $text
 * @property DateTimeInterface $created_at
 * @property DateTimeInterface $updated_at
 * @method static findOrFail(int $id)
 */
class MassFreeBetBonusInfo extends Model
{
    /**
     * @var string
     */
    protected $connection = 'mysql.admin';

    protected $table = 'mass_freebet_bonus_info';
}
