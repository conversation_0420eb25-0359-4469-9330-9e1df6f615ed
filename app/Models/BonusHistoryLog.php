<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $bonus_id
 * @property null|string $author_email
 * @property string $type
 * @property null|string $diff
 * @property array<string, array<string, null|string|int|bool>> $data
 * @property Carbon $created_at
 */
class BonusHistoryLog extends Model
{
    protected $connection = 'mysql.bonus';

    protected $table = 'bonus_history_logs';

    /**
     * @var array<string, string>
     */
    protected $casts = [
        'data' => 'array',
    ];

    protected $fillable = [
        'bonus_id',
        'author_email',
        'type',
        'data',
    ];
}
