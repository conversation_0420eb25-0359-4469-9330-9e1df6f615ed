<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class BonusTemplate
 *
 * @mixin Builder
 *
 * @property int $id
 * @property int $bonus_id
 * @property int $player_id
 * @property int $trigger_session_id
 * @property string $status
 * @property int $deposits_count
 * @property DateTimeInterface $created_at
 * @property DateTimeInterface $updated_at
 */
final class BonusPlayerPivot extends Model
{
    protected $table = 'bonus_player';

    protected $primaryKey = 'id';

    /**
     * @var string[]
     */
    protected $casts = [
        'bonus_id' => 'integer',
        'player_id' => 'integer',
        'trigger_session_id' => 'integer',
        'status' => 'string',
        'deposits_count' => 'integer',
    ];

    public function bonus(): BelongsTo
    {
        return $this->belongsTo(Bonus::class);
    }
}
