<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\Carbon;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

/**
 * Class BonusInfo
 *
 * @property int $id
 * @property int $client_id
 * @property string $name
 * @property null|DateTimeInterface $visible_from
 * @property null|DateTimeInterface $visible_to
 * @property string $currency
 * @property bool $active
 * @property bool $casino
 * @property bool $is_welcome
 * @property bool $is_for_smartico
 * @property string $image
 * @property string $bonus_name
 * @property string $description
 * @property string $description_info
 * @property string $condition_title
 * @property string $condition
 * @property string $colors
 * @property int $sort_order
 * @property string $proceed_link
 * @property-read null|Bonus $bonus
 * @property-read Collection<int, BonusPlayerCard> $bonusPlayerCards
 * @property int $bonus_id
 * @property DateTimeInterface $created_at
 * @property DateTimeInterface $updated_at
 * @property string $show_visible_date
 * @property bool $custom_type
 * @property string $general_visible_from
 * @property string $general_visible_to
 * @property null|string $uuid
 */
final class BonusInfo extends Model
{
    protected $table = 'bonus_info';

    protected $primaryKey = 'id';

    /**
     * @var true[]
     */
    protected $attributes = [
        'active' => true,
    ];

    /**
     * @var string[]
     */
    protected $casts = [
        'active' => 'boolean',
        'casino' => 'boolean',
        'is_welcome' => 'boolean',
        'is_for_smartico' => 'boolean',
        'show_visible_date' => 'boolean',
        'description' => 'array',
        'description_info' => 'array',
        'condition' => 'array',
        'condition_title' => 'array',
        'bonus_name' => 'array',
        'colors' => 'array',
    ];

    protected $fillable = [
        'name',
        'casino',
        'is_welcome',
        'active',
        'is_for_smartico',
        'description',
        'description_info',
        'condition',
        'condition_title',
        'bonus_name',
        'colors',
        'client_id',
        'currency',
        'visible_from',
        'visible_to',
        'image',
        'sort_order',
        'proceed_link',
        'bonus_id',
        'show_visible_date',
        'custom_type',
    ];

    public static function boot(): void
    {
        parent::boot();

        self::creating(static function ($bonusInfo): void {
            $bonusInfo->uuid = (string)Str::uuid();
        });
    }

    public function bonus(): BelongsTo
    {
        return $this->belongsTo(Bonus::class, 'bonus_id', 'id');
    }

    public function getGeneralVisibleFromAttribute(): mixed
    {
        return $this->getGeneralVisible(true);
    }

    public function getGeneralVisibleToAttribute(): mixed
    {
        return $this->getGeneralVisible(false);
    }

    private function getGeneralVisible(bool $getVisibleFrom): mixed
    {
        $bonus = $this->bonus;

        if ($bonus) {
            if ($bonus->type === 'deposit' || $bonus->type === 'free_spins_for_deposit') {
                $actualTriggerSession = $bonus->getCurrentTriggerSession() ?? $bonus->getNextTriggerSession() ?? $bonus->getPreviousTriggerSession();

                $bonusTime = $getVisibleFrom
                    ? $actualTriggerSession->start_at ?? Carbon::createFromTimestamp(0)->toDateTime()
                    : $actualTriggerSession->end_at ?? Carbon::createFromTimestamp(0)->toDateTime();
            } else {
                $bonusTime = $getVisibleFrom
                    ? $bonus->active_from
                    : $bonus->active_til;
            }
        } else {
            $bonusTime = null;
        }

        $bonusInfoTime = $getVisibleFrom
            ? $this->visible_from
            : $this->visible_to;

        if (null === $bonusTime && null === $bonusInfoTime) {
            return null;
        }

        return $getVisibleFrom
            ? max($bonusTime, $bonusInfoTime)
            : min($bonusTime, $bonusInfoTime) ?? $bonusInfoTime;
    }
}
