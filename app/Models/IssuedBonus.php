<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property null|string $log_key
 * @property int $bonus_id
 * @property string $type
 * @property int $player_id
 * @property int $balance
 * @property null|string $bonus_external_id
 * @property string $status
 * @property int $orig_bonus
 * @property int $orig_wager
 * @property int $wager
 * @property null|float $min_factor
 * @property null|int $min_bet
 * @property null|Carbon $expire_at
 * @property int $in_game
 * @property int $transfer
 * @property string $currency
 * @property bool $active
 * @property bool $popup_seen
 * @property bool $casino
 * @property bool $bets
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 * @property-read Bonus $bonus
 */
class IssuedBonus extends Model
{
    protected $connection = 'mysql.bonus';

    protected $table = 'issued_bonuses';

    /**
     * @var string[]
     */
    protected $casts = [
        'expire_at' => 'datetime',
        'active' => 'bool',
        'popup_seen' => 'bool',
        'casino' => 'bool',
        'bets' => 'bool',
    ];

    public function bonus(): BelongsTo
    {
        return $this->belongsTo(Bonus::class, 'bonus_id', 'id');
    }

    public function isValid(): bool
    {
        return $this->active && !$this->isExpired();
    }

    public function isExpired(): bool
    {
        return $this->expire_at && $this->expire_at->isPast();
    }
}
