<?php

namespace App\Models\Traits;

trait ImageLinkFixerTrait
{
    public function getImageAttribute(?string $value): ?string
    {
        if (empty($value)) {
            return $value;
        }

        $separator = '/storage/';

        [$host, $uri] = array_pad(
            explode($separator, $value),
            2,
            null
        );

        if (! empty($host) && ! empty($uri)) {
            return $separator.$uri;
        }

        return $value;
    }
}
