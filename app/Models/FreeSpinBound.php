<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Database\Eloquent\Builder as BuilderContract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class FreeSpinBound
 *
 * @property int $id
 * @property int $free_spin_id
 * @property string $uuid
 * @property int $player_id
 * @property int $bet
 * @property int $total_win
 * @property int $count_left
 * @property bool $is_active
 * @property bool $is_archived
 * @property bool $is_user_notified
 * @property string $bet_id
 * @property string $message
 * @property int $bonus_id
 * @property string $status
 * @property DateTimeInterface $start_at
 * @property DateTimeInterface $expire_at
 * @property DateTimeInterface $canceled_at
 * @property DateTimeInterface $created_at
 * @property DateTimeInterface $updated_at
 * @property FreeSpin $freespin
 * @property-read FreeSpin $freespinBonusSlot
 * @property-read null|FreeSpin $freespinBonusSlotWithTrashed
 */
class FreeSpinBound extends Model
{
    use SoftDeletes;

    /**
     * @var string
     */
    protected $table = 'free_spin_bounds';

    /**
     * @var string
     */
    protected $primaryKey = 'id';

    protected $fillable = [
        'free_spin_id',
        'uuid',
        'player_id',
        'bet',
        'bet_id',
        'bonus_id',
        'count_left',
        'is_active',
        'is_archived',
        'total_win',
    ];

    public function freespin(): BelongsTo|BuilderContract
    {
        return $this->belongsTo(FreeSpin::class, 'free_spin_id', 'id');
    }

    public function freespinBonusSlot(): BelongsTo|BuilderContract
    {
        return $this->freespin()->with(['bonus']);
    }

    public function freespinBonusSlotWithTrashed(): BelongsTo
    {
        /** @var BelongsTo&MorphTo $freeSpin */
        $freeSpin = $this->freespin();

        return $freeSpin->withTrashed();
    }
}
