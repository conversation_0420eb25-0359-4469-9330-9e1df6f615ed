<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\BonusPlayerStatusEnum;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * Class BonusPlayerCard
 *
 * @mixin Builder
 *
 * @property int $id
 * @property int $bonus_id
 * @property int $player_id
 * @property int $trigger_session_id
 * @property string $status
 * @property int $deposits_count
 * @property null|Carbon $created_at
 * @property null|Carbon $updated_at
 *
 * @method static Builder|BonusPlayer bonusPlayerQuery(int $playerId, int $bonusId)
 *
 * @property Bonus $bonus
 */
class BonusPlayer extends Model
{
    protected $table = 'bonus_player';

    public function bonus(): BelongsTo
    {
        return $this->belongsTo(Bonus::class);
    }

    /**
     * @uses scopeBonusPlayerQuery
     */
    public function scopeBonusPlayerQuery(Builder $query, int $playerId, int $bonusId): Builder
    {
        return $query->where('player_id', $playerId)
            ->where('bonus_id', $bonusId);
    }

    public function isStatus(BonusPlayerStatusEnum $status): bool
    {
        return $this->status === $status->value;
    }
}
