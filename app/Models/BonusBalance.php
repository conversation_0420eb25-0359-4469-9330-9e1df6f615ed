<?php

declare(strict_types=1);

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class BonusBalance
 *
 * @mixin Builder
 *
 * @property int $id
 * @property string|null $log_key
 * @property int $bonus_id
 * @property string $type
 * @property int $player_id
 * @property int $balance
 * @property string|null $bonus_external_id
 * @property string $status
 * @property int $orig_bonus
 * @property int $orig_wager
 * @property int $wager
 * @property float|null $min_factor
 * @property int|null $min_bet
 * @property DateTimeInterface|null $expire_at
 * @property int $in_game
 * @property int $transfer
 * @property string $currency
 * @property bool $active
 * @property bool $popup_seen
 * @property bool $casino
 * @property bool $bets
 * @property DateTimeInterface|null $created_at
 * @property DateTimeInterface|null $updated_at
 * @property-read Bonus $bonus
 */
class BonusBalance extends Model
{
    protected $table = 'bonus_balances';

    /**
     * @var string[]
     */
    protected $casts = [
        'expire_at' => 'datetime',
        'active' => 'bool',
        'popup_seen' => 'bool',
        'casino' => 'bool',
        'bets' => 'bool',
    ];

    public function bonus(): BelongsTo
    {
        return $this->belongsTo(Bonus::class, 'bonus_id', 'id');
    }
}
