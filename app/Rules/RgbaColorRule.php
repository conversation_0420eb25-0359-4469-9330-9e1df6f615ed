<?php

declare(strict_types=1);

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class RgbaColorRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_string($value) || !preg_match('/^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(?:,\s*(?:0?\.\d+|1(?:\.0)?))?\s*\)$/', $value)) {
            $fail('The :attribute must be a valid RGB/RGBA color format (e.g., rgb(255,255,255) or rgba(255,255,255,0.5)).');
        }
    }
}
