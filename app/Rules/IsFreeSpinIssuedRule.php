<?php

declare(strict_types=1);

namespace App\Rules;

use App\Models\FreeSpinBound;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class IsFreeSpinIssuedRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
         $isFreeSpinIssued = FreeSpinBound::query()->where('free_spin_id', $value)->exists();

        if ($isFreeSpinIssued) {
            $fail("Free spin with :attribute $value is already issued");
        }
    }
}
