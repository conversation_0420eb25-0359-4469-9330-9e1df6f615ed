<?php

namespace App\Rules;

use App\Models\PromoCode;
use App\Services\Dto\PlayerDto;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class MatchPromoCodeBonusAndPlayerCurrency implements ValidationRule
{
    public function __construct(
        protected PlayerDto $player,
        protected ?PromoCode $promoCode,
    ) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($this->promoCode->bonus->currency !== $this->player->currency) {
            $fail('The player currency does not match the promo code currency.');
        }
    }
}
