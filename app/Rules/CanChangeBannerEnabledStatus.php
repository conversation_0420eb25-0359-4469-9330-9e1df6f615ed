<?php

namespace App\Rules;

use App\Models\Banner;
use Carbon\Carbon;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CanChangeBannerEnabledStatus implements ValidationRule
{
    private int $bannerId;

    public function __construct(int $bannerId)
    {
        $this->bannerId = $bannerId;
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        /** @var Banner $banner */
        $banner = Banner::query()->find($this->bannerId);

        if ($value && $banner->end_at && Carbon::parse($banner->end_at) < Carbon::now()) {
            $fail('Banner time expired.');
        }
    }
}
