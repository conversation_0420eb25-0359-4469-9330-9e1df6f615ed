<?php

namespace App\Rules;

use App\Models\PromoCode;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class PromoCodeValidation implements ValidationRule
{
    public function __construct(
        protected ?PromoCode $promoCode
    ) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->promoCode->isValidBonusAndPromoCode()) {
            $fail('The promo code is invalid.');
        }
    }
}
