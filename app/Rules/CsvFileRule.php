<?php

namespace App\Rules;

use App\Traits\FileHelper;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CsvFileRule implements ValidationRule
{
    use FileHelper;

    private const VALID_FIELDS = [
        'onetime' => [
            'Id',
            'Email',
            'Amount'
        ],
        'freebet' => [
            'template_id',
            'currency',
            'amount',
            'external_player_id'
        ],
    ];

    public function __construct(
        protected ?string $bonusType,
    ) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->bonusType) {
            $fail('Bonus type is required');
            return;
        }

        if (!isset(self::VALID_FIELDS[$this->bonusType])) {
            $fail("Invalid bonus type: $this->bonusType");
            return;
        }

        $validFields = self::VALID_FIELDS[$this->bonusType];
        if ($separator = $this->getSeparator($value)) {
            $header = $this->getHeader($separator, $value);
        }

        if (empty($header)) {
            $fail('CSV file is not valid');
            return;
        }

        foreach ($header as $item) {
            if (!in_array($item, $validFields, true)) {
                $fail('CSV file is not valid');
                return;
            }
        }
    }
}
