<?php

namespace App\Rules;

use App\Models\PromoCode;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckPromocodeCurrency implements ValidationRule
{
    public function __construct(
        protected ?PromoCode $promoCode,
        protected ?string $currency,
    ) {
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($this->promoCode->bonus->currency !== ($this->currency ?? 'INR')) {
            $fail('The currency does not match promo code.');
        }
    }
}
