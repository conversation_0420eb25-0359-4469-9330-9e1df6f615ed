<?php

namespace App\Rules;

use App\Models\PromoCode;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckPromocodeIsNotWelcome implements ValidationRule
{
    public function __construct(
        protected ?PromoCode $promoCode,
    ) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->promoCode->bonus->is_welcome_group) {
            $fail('Promocode is not suitable in current place');
        }
    }
}
