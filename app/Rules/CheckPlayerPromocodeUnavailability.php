<?php

namespace App\Rules;

use App\Models\PromoCode;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CheckPlayerPromocodeUnavailability implements ValidationRule
{
    /**
     * @param int[] $promoCodeIds
     */
    public function __construct(
        protected ?PromoCode $promoCode,
        protected array $promoCodeIds,
    ) {
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (in_array($this->promoCode->id, $this->promoCodeIds, true)) {
            $fail('The player already has this promo code.');
        }
    }
}
