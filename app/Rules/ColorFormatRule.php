<?php

declare(strict_types=1);

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ColorFormatRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $hexRule = new HexColorRule();
        $rgbaRule = new RgbaColorRule();

        $hexValid = true;
        $rgbaValid = true;

        $hexRule->validate($attribute, $value, static function () use (&$hexValid): void {
            $hexValid = false;
        });

        $rgbaRule->validate($attribute, $value, static function () use (&$rgbaValid): void {
            $rgbaValid = false;
        });

        if (!$hexValid && !$rgbaValid) {
            $fail('The :attribute must be a valid color in HEX or RGBA format.');
        }
    }
}
