<?php

declare(strict_types=1);

namespace App\Enums;

enum BonusTypeEnum: string
{
    case WELCOME = 'welcome';
    case WELCOME_4_STEPS = 'welcome_4_steps';
    case ONETIME = 'onetime';
    case NO_DEP = 'no_dep';
    case DEPOSIT = 'deposit';
    case FREESPIN_BONUS = 'freespin_bonus';
    case FREE_SPINS_FOR_DEPOSIT = 'free_spins_for_deposit';
    case WAGER = 'wager';
    case FREEBET = 'freebet';
    case FREE_MONEY = 'free_money';
    case FREE_BET = 'bet_refund';
    case NO_RISK = 'no_risk';

    /**
     * @return BonusTypeEnum[]
     */
    public static function values(): array
    {
        return [
            self::WELCOME,
            self::WELCOME_4_STEPS,
            self::ONETIME,
            self::NO_DEP,
            self::DEPOSIT,
            self::FREESPIN_BONUS,
            self::FREE_SPINS_FOR_DEPOSIT,
            self::WAGE<PERSON>,
            self::FREEBET,
            self::FREE_MONEY,
            self::FREE_BET,
            self::NO_RISK,
        ];
    }
}
