<?php

namespace App\Queue;

use App\Interfaces\QueueRateLimiterInterface;
use App\Jobs\Middleware\QueueRateLimiterMiddleware;
use Illuminate\Queue\LuaScripts;
use Illuminate\Support\Facades\Cache;
use Illuminate\Redis\Connections\PhpRedisConnection;

class RedisQueue extends \Illuminate\Queue\RedisQueue
{
    public function deleteAndRelease($queue, $job, $delay): void
    {
        $queue = $this->getQueue($queue);


        /**
         * @var PhpRedisConnection $connection
         */
        $connection = $this->getConnection();

        $connection->eval(
            LuaScripts::release(),
            2,
            $queue . ':delayed',
            $queue . ':reserved',
            $this->getReservedJob($job),
            $this->availableAt($delay)
        );
    }

    /**
     * @phpstan-ignore-next-line
     */
    private function getReservedJob($job): string
    {
        $payload = $job->payload();

        if (!$this->isJobInstanceOfQueueRateLimiter($payload)) {
            return $job->getReservedJob();
        }

        if ($this->hasExceededException($job)) {
            return $job->getReservedJob();
        }

        return json_encode($payload);
    }

    /**
     * @param array<string, string> $payload
     */
    private function isJobInstanceOfQueueRateLimiter(array $payload): bool
    {
        return isset($payload['displayName']) && is_subclass_of($payload['displayName'], QueueRateLimiterInterface::class);
    }

    /**
     * @phpstan-ignore-next-line
     */
    private function hasExceededException($job): bool
    {
        return (bool)Cache::get(QueueRateLimiterMiddleware::jobExceptionCounterKey($job->getJobId()));
    }
}
