<?php

namespace App\Queue;

class RedisConnector extends \Illuminate\Queue\Connectors\RedisConnector
{
    /**
     * @param array<string, null|string|int|bool> $config
     */
    public function connect(array $config): RedisQueue
    {
        return new RedisQueue(
            $this->redis,
            $config['queue'],
            $config['connection'] ?? $this->connection,
            $config['retry_after'] ?? 60,
            $config['block_for'] ?? null,
            $config['after_commit'] ?? null
        );
    }
}
