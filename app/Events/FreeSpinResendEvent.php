<?php

namespace App\Events;

use App\Models\FreeSpin;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FreeSpinResendEvent implements ShouldQueue
{
    use Batchable;
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * @param int[] $freespinBoundIdsArray
     */
    public function __construct(
        public FreeSpin $freespin,
        public array $freespinBoundIdsArray,
    ) {
    }
}
