<?php

namespace App\Events;

use App\Models\FreeSpin;
use Illuminate\Contracts\Queue\ShouldQueue;

/**
 * Class FreeSpinCudEvent
 *
 * @property int $freeSpinId
 * @property null|FreeSpin $currentFreeSpin
 * @property null|FreeSpin $prevFreeSpin
 * @property null|string $authorEmail
 */
class FreeSpinCudEvent implements ShouldQueue
{
    /**
     * Create a new event instance.
     */
    public function __construct(
        public int $freeSpinId,
        public ?FreeSpin $currentFreeSpin = null,
        public ?FreeSpin $prevFreeSpin = null,
        public ?string $authorEmail = null,
    ) {
    }
}
