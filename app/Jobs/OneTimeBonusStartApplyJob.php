<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Services\OneTimeBonusService;
use Illuminate\Support\Facades\Cache;

class OneTimeBonusStartApplyJob extends Job
{
    public int $timeout = 600;

    public int $tries = 1;

    public function __construct(
        public readonly int $bonusId,
        public readonly string $key,
    ) {
    }

    public function handle(OneTimeBonusService $service): void
    {
        $items = Cache::get($this->key) ?? [];

        foreach ($items as $item) {
            $bonusInfo = $service->createBonusInfo([
                'bonusId' => $this->bonusId,
                'playerId' => $item['Id'],
                'email' => $item['Email'],
                'amount' => $item['Amount'],
            ]);

            dispatch(new ApplyOneTimeBonusJob($bonusInfo->id));
        }

        Cache::forget($this->key);
    }
}
