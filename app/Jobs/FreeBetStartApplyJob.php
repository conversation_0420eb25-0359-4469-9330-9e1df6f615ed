<?php

namespace App\Jobs;

use App\Services\FreeBetBonusService;
use Illuminate\Support\Facades\Cache;

class FreeBetStartApplyJob extends Job
{
    public int $timeout = 600;

    public int $tries = 1;

    public function __construct(
        public readonly int $bonusId,
        public readonly string $key,
    ) {
    }

    public function handle(FreeBetBonusService $service): void
    {
        $items = Cache::get($this->key) ?? [];

        foreach ($items as $item) {
            $bonusInfo = $service->createBonusInfo([
                'bonusId' => $this->bonusId,
                'templateId' => $item['template_id'],
                'externalPlayerId' => $item['external_player_id'],
                'amount' => $item['amount'],
                'currency' => $item['currency'],
            ]);

            dispatch(new ApplyFreeBetJob($bonusInfo->id));
        }

        Cache::forget($this->key);
    }
}
