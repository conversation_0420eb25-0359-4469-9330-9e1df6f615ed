<?php

namespace App\Jobs\FreeSpins;

use App\Jobs\Job;
use App\Models\FreeSpinBound;
use App\Services\Dto\FreeSpinIntegrationSlotEgratorDto;
use App\Services\FreeSpinBoundService;
use App\Services\FreeSpinService;
use Illuminate\Bus\Batchable;
use Illuminate\Foundation\Bus\Dispatchable;
use Throwable;

/**
 * Class ResendFreeSpinJob
 */
class ResendFreeSpinJob extends Job
{
    use Dispatchable, Batchable;

    protected FreeSpinBoundService $freeSpinBoundService;

    /**
     * ResendFreeSpinJob constructor.
     */
    public function __construct(
        protected FreeSpinIntegrationSlotEgratorDto $dto,
    )
    {
        $this->freeSpinBoundService = app(FreeSpinBoundService::class);
        $this->onQueue('freespins');
    }

    /**
     * @throws Throwable
     */
    public function handle(FreeSpinService $freeSpinService): void
    {
        $boundIds = $this->dto->players;
        foreach ($boundIds as $boundId) {
            /** @var FreeSpinBound $freeSpinBound */
            $freeSpinBound = $this->freeSpinBoundService->findById($boundId);

            dispatch(new SendFreeSpinToSlotEgratorJob($freeSpinBound->id, $this->dto));
        }

        $freeSpinService->changeFreeSpinProcessStatus($this->dto->id, FreeSpinService::FINISH_PROCESS_FREE_SPIN_STATUS);
    }
}
