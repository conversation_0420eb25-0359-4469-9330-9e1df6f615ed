<?php

namespace App\Jobs\FreeSpins;

use App\Enums\BonusTypeEnum;
use App\Jobs\Job;
use App\Models\FreeSpinBound;
use App\Services\Dto\FreeSpinIntegrationSlotEgratorDto;
use App\Services\Dto\PlayerDto;
use App\Services\FreeSpinBoundService;
use App\Services\PlayerService;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Batchable;
use Illuminate\Foundation\Bus\Dispatchable;

/**
 * Class SendFreeSpinToSlotEgratorJob
 */
class SendFreeSpinToSlotEgratorJob extends Job
{
    use Batchable;
    use Dispatchable;
    private PlayerService $playerService;

    /**
     * NewFreeSpinJob constructor.
     */
    public function __construct(
        private readonly int $freeSpinBoundId,
        private readonly FreeSpinIntegrationSlotEgratorDto $dto
    ) {
        $this->onQueue('freespins');
    }

    /**
     * @throws Exception
     */
    public function handle(FreeSpinBoundService $freeSpinBoundService, PlayerService $playerService): void
    {
        $this->playerService = $playerService;

        /** @var FreeSpinBound $freeSpinBound */
        $freeSpinBound = $freeSpinBoundService->findById($this->freeSpinBoundId);
        $data = [];
        $data['subscriberId'] = $this->dto->subId;
        $player = $this->playerService->getPlayerByID($freeSpinBound->player_id);
        $data['freeSpins'][] = $this->getFreeSpinPlayerData($player, $freeSpinBound);

        $freeSpinBoundService->sendFreeSpinsData($this->dto->gameToken, $this->freeSpinBoundId, $data);
    }

    /**
     * @return array<string, float|int|string|false|null>
     */
    protected function getFreeSpinPlayerData(PlayerDto $player, FreeSpinBound $freeSpinBound): array
    {
        $user_balance = $this->playerService->getBalanceByPlayerID($freeSpinBound->player_id);
        $freespin = $freeSpinBound->freespin;
        $bonus = $freespin->bonus;

        /** @var Carbon $startAt */
        $startAt = $freeSpinBound->start_at;
        /** @var Carbon $expireAt */
        $expireAt = $freeSpinBound->expire_at;

        $validFrom = strtotime($startAt);
        $validUntil = strtotime($expireAt);

        if ($bonus?->type === BonusTypeEnum::FREESPIN_BONUS->value) {
            /** @var Carbon $startAt */
            $startAt = $freespin->start_at;
            /** @var Carbon $expiredAt */
            $expiredAt = $freespin->expired_at;
            $validFrom = strtotime($startAt) + 60;
            $validUntil = strtotime($expiredAt);
        }

        return [
            'player_name' => $player->username,
            'player_id' => $player->uuid,
            'player_local_id' => (string)$player->id,
            'currency' => $this->dto->currency,
            'quantity' => $this->dto->count,
            'valid_from' => $validFrom,
            'valid_until' => $validUntil,
            'freespin_id' => (string)$freeSpinBound->id,
            'bet_id' => $this->dto->betId,
            'denomination' => $this->dto->denomination,
            'game_uuid' => $this->dto->externalId,
            'balance_id' => $user_balance->id,
        ];
    }
}
