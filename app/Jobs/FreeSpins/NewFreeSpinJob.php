<?php

namespace App\Jobs\FreeSpins;

use App\Jobs\Job;
use App\Services\Dto\FreeSpinIntegrationSlotEgratorDto;
use App\Services\FreeSpinBoundService;
use App\Services\FreeSpinService;
use App\Services\PlayerService;
use Illuminate\Bus\Batchable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * Class NewFreeSpinJob
 */
class NewFreeSpinJob extends Job
{
    use Dispatchable, Batchable;

    /**
     * NewFreeSpinJob constructor.
     */
    public function __construct(
        protected FreeSpinIntegrationSlotEgratorDto $dto,
    ) {
        $this->onQueue('freespins');
    }

    /**
     * @throws Throwable
     */
    public function handle(FreeSpinService $freeSpinService, FreeSpinBoundService $freeSpinBoundService, PlayerService $playerService): void {
        $players = $playerService->getPlayersByIds($this->dto->players);
        foreach ($players as $player) {
            $freeSpinBound = $freeSpinBoundService->createFromFreeSpinBound(
                $this->dto, $player->id,
            );

            if ($playerService->isPlayerBlocked($player->id)) {
                $freeSpinBound->message = 'Player is blocked';
                $freeSpinBound->save();
                continue;
            }

            if ($player->currency !== $this->dto->currency) {
                $freeSpinBound->message = 'Different currency is used';
                $freeSpinBound->save();
                continue;
            }

            dispatch(new SendFreeSpinToSlotEgratorJob($freeSpinBound->id, $this->dto));
        }

        $freeSpinService->changeFreeSpinProcessStatus($this->dto->id, FreeSpinService::FINISH_PROCESS_FREE_SPIN_STATUS);
    }
}
