<?php

namespace App\Jobs\FreeSpins;

use App\Jobs\Job;
use App\Models\FreeSpinBound;
use App\Services\FreeSpinBoundService;
use App\Services\SlotService;
use Exception;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class FreeSpinBoundCancelJob
 */
class FreeSpinBoundCancelJob extends Job
{
    public readonly int $freeSpinBoundId;
    public readonly int $slotId;

    /**
     * FreeSpinBoundCancelJob constructor.
     *
     * @param int $freeSpinBoundId
     * @param int $slotId
     */
    public function __construct(int $freeSpinBoundId, int $slotId)
    {
        $this->slotId = $slotId;
        $this->freeSpinBoundId = $freeSpinBoundId;
        $this->onQueue('freespins');
    }

    /**
     * @param FreeSpinBoundService $freeSpinBoundService
     * @param SlotService $slotService
     * @throws Exception
     */
    public function handle(FreeSpinBoundService $freeSpinBoundService, SlotService $slotService): void
    {
        $subscription = $slotService->getSubscriptionBySlotId($this->slotId);
        /** @var FreeSpinBound $freeSpinBound */
        $freeSpinBound = $freeSpinBoundService->findById($this->freeSpinBoundId);

        $freeSpinBoundService->cancelFreeSpins(new Collection([$freeSpinBound]), $subscription, true);
    }
}
