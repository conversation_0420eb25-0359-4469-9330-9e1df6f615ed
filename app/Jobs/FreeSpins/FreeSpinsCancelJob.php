<?php

namespace App\Jobs\FreeSpins;

use App\Enums\SupplierEnum;
use App\Jobs\Job;
use App\Models\FreeSpinBound;
use App\Services\FreeSpinBoundService;
use App\Services\FreeSpinService;
use App\Services\GamicorpFreeSpinBoundService;
use App\Services\SlotService;
use Exception;

class FreeSpinsCancelJob extends Job
{
    private int $freeSpinId;
    private ?string $authorEmail;

    /**
     * FreeSpinsCancelJob constructor.
     */
    public function __construct(int $freeSpinId, ?string $authorEmail = null)
    {
        $this->freeSpinId = $freeSpinId;
        $this->authorEmail = $authorEmail;
        $this->onQueue('freespins');
    }

    /**
     * @throws Exception
     */
    public function handle(
        FreeSpinService $service,
        GamicorpFreeSpinBoundService $gamicorpFreeSpinBoundService,
        FreeSpinBoundService $freeSpinBoundService,
        SlotService $slotService
    ): void {
        $freeSpin = $service->getFreeSpinDetailById($this->freeSpinId);
        if ($freeSpin->author_email !== $this->authorEmail) {
            $freeSpin->author_email = $this->authorEmail ?? 'system';
            $freeSpin->save();
        }
        $freeSpinBounds = $freeSpinBoundService->getPlayersByFreeSpinId($this->freeSpinId);

        $service->changeFreeSpinProcessStatus($this->freeSpinId, FreeSpinService::CANCEL_FREE_SPIN_STATUS, true);

        if ($freeSpin->supplier === SupplierEnum::GAMICORP_INTEGRATION->value) {
            /** @var FreeSpinBound $freeSpinBound */
            foreach ($freeSpinBounds as $freeSpinBound) {
                if (!$freeSpinBound->is_active) {
                    continue;
                }

                $freespinIsCancelled = $gamicorpFreeSpinBoundService->cancelFreespinBound($freeSpinBound);
                if ($freespinIsCancelled) {
                    $freeSpinBoundService->update(['message' => 'Canceled by system'], [$freeSpinBound->id]);
                    $freeSpinBound->delete();
                }
            }
        } else {
            $subscription = $slotService->getSubscriptionBySlotId($this->freeSpinId);
            $freeSpinBoundService->cancelFreeSpins($freeSpinBounds, $subscription);
            $freeSpinBoundService->removeCanceledFreeSpinFromDB($this->freeSpinId);
        }

        $service->removeFreeSpin($this->freeSpinId);
    }
}
