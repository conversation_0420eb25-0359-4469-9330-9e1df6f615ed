<?php

namespace App\Jobs\FreeSpins;

use App\Jobs\Job;
use App\Services\Dto\FreeSpinCrudDto;
use App\Services\FreeSpinBoundService;
use App\Services\FreeSpinService;
use App\Services\SlotService;
use Exception;

/**
 * Class FreeSpinsUpdateJob
 */
class FreeSpinsUpdateJob extends Job
{
    private int $freeSpinId;
    private int $slotId;
    private FreeSpinCrudDto $dto;

    /**
     * FreeSpinsUpdateJob constructor.
     *
     * @param int $freeSpinId
     * @param int $slotId
     * @param FreeSpinCrudDto $dto
     */
    public function __construct(int $freeSpinId, int $slotId, FreeSpinCrudDto $dto)
    {
        $this->slotId = $slotId;
        $this->freeSpinId = $freeSpinId;
        $this->dto = $dto;
        $this->onQueue('freespins');
    }

    /**
     * @param FreeSpinService $service
     * @param FreeSpinBoundService $freeSpinBoundService
     * @param SlotService $slotService
     * @throws Exception
     */
    public function handle(FreeSpinService $service, FreeSpinBoundService $freeSpinBoundService, SlotService $slotService): void
    {
        $subscription = $slotService->getSubscriptionBySlotId($this->slotId);
        $freeSpinBounds = $freeSpinBoundService->getPlayersByFreeSpinId($this->freeSpinId);
        $freeSpinBoundService->cancelFreeSpins($freeSpinBounds, $subscription);
        $service->update($this->dto);
    }
}
