<?php

namespace App\Jobs\Middleware;

use App\Interfaces\QueueRateLimiterInterface;
use App\Jobs\Job;
use Closure;
use Exception;
use Illuminate\Contracts\Redis\LimiterTimeoutException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

class QueueRateLimiterMiddleware
{
    protected bool $enabled;
    protected string $connectionName = '';
    protected ?string $aggregatorName = null;
    protected int $timeSpanInSeconds;
    protected int $allowedNumberOfJobsInTimeSpan;
    protected int $releaseInSeconds;
    protected bool $dontRelease;

    public function __construct()
    {
        $this->enabled = config('queue.rate_limiter.enabled');
        $this->dontRelease = config('queue.rate_limiter.dont_release');
        $this->allowedNumberOfJobsInTimeSpan = config('queue.rate_limiter.allowed_number');
        $this->timeSpanInSeconds = config('queue.rate_limiter.time_span_seconds');
        $this->releaseInSeconds = config('queue.rate_limiter.release_seconds');
    }

    public static function jobExceptionCounterKey(string $jobUUID): string
    {
        return 'job_exception_counter:' . $jobUUID;
    }

    public function enabled(bool $enabled = true): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    /**
     * @uses dontRelease
     *
     * @return $this
     */
    public function dontRelease(): self
    {
        $this->dontRelease = true;

        return $this;
    }

    public function connectionName(string $connectionName): self
    {
        $this->connectionName = $connectionName;

        return $this;
    }

    /**
     * @uses aggregatorName
     *
     * @return $this
     */
    public function aggregatorName(string $aggregatorName): self
    {
        $this->aggregatorName = $aggregatorName;

        return $this;
    }

    public function allow(int $allowedNumberOfJobsInTimeSpan): self
    {
        $this->allowedNumberOfJobsInTimeSpan = $allowedNumberOfJobsInTimeSpan;

        return $this;
    }

    public function everySeconds(int $timespanInSeconds = 1): self
    {
        $this->timeSpanInSeconds = $timespanInSeconds;

        return $this;
    }

    /**
     * @uses everyMinutes
     */
    public function everyMinutes(int $timespanInMinutes = 1): self
    {
        return $this->everySeconds($timespanInMinutes * 60);
    }

    public function releaseAfterSeconds(int $releaseInSeconds): self
    {
        $this->releaseInSeconds = $releaseInSeconds;

        return $this;
    }

    /**
     * @uses releaseAfterMinutes
     */
    public function releaseAfterMinutes(int $releaseInMinutes): self
    {
        return $this->releaseAfterSeconds($releaseInMinutes * 60);
    }

    protected function getThrottleKey(QueueRateLimiterInterface $job): string
    {
        $aggregatorName = $this->aggregatorName ?? $job::class;

        return "queue_rate_limiter_throttler:$aggregatorName";
    }

    /**
     * @throws LimiterTimeoutException
     */
    public function handle(QueueRateLimiterInterface $job, Closure $next): void
    {
        if (!$this->enabled) {
            $next($job);

            return;
        }

        Redis::connection($this->connectionName)
            ->throttle($this->getThrottleKey($job))
            ->block(0)
            ->allow($this->allowedNumberOfJobsInTimeSpan)
            ->every($this->timeSpanInSeconds)
            ->then(function () use ($job, $next): void {
                try {
                    $next($job);
                } catch (Exception $exception) {
                    $this->handleException($job, $exception);
                }
            }, function () use ($job): void {
                $this->releaseJob($job);
            });
    }

    /**
     * @throws Exception
     */
    private function handleException(QueueRateLimiterInterface $job, Exception $exception): void
    {
        /**
         * @var Job&QueueRateLimiterInterface $job
         */
        $jobId = $job->job->getJobId();
        $counterKey = self::jobExceptionCounterKey($jobId);
        $ttl = array_sum($job->backoff()) + $this->timeSpanInSeconds;

        if (!Cache::get($counterKey)) {
            Cache::set($counterKey, $jobId, $ttl);
        }

        throw $exception;
    }

    private function releaseJob(QueueRateLimiterInterface $job): void
    {
        /**
         * @var Job&QueueRateLimiterInterface $job
         */
        if (!$this->dontRelease) {
            $job->release($this->releaseInSeconds);
        }
    }
}
