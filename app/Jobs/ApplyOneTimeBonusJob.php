<?php

namespace App\Jobs;

use App\Exceptions\Exception;
use App\Interfaces\QueueRateLimiterInterface;
use App\Jobs\Middleware\QueueRateLimiterMiddleware;
use App\Services\OneTimeBonusService;

class ApplyOneTimeBonusJob extends Job implements QueueRateLimiterInterface
{
    public function __construct(
        private readonly int $bonusInfoId,
    ) {
    }

    /**
     * @throws Exception|\Exception
     */
    public function handle(OneTimeBonusService $service): void
    {
        $service->apply($this->bonusInfoId);
    }

    /**
     * @return class-string[]
     */
    public function middleware(): array
    {
        return [QueueRateLimiterMiddleware::class];
    }
}
