<?php

namespace App\Jobs;

use App\Interfaces\QueueRateLimiterInterface;
use App\Jobs\Middleware\QueueRateLimiterMiddleware;
use App\Services\FreeBetBonusService;

class ApplyFreeBetJob extends Job implements QueueRateLimiterInterface
{
    public function __construct(
        private readonly int $bonusInfoId,
    ) {
    }

    public function handle(FreeBetBonusService $service): void
    {
        $service->apply($this->bonusInfoId);
    }

    /**
     * @return class-string[]
     */
    public function middleware(): array
    {
        return [QueueRateLimiterMiddleware::class];
    }
}
