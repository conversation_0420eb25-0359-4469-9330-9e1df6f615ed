<?php

declare(strict_types=1);

namespace App\Http\Cache\Replacers;

use JsonException;
use Spatie\ResponseCache\Replacers\Replacer;
use Symfony\Component\HttpFoundation\Response;

class PersonalLoginTokenReplacer implements Replacer
{
    public function prepareResponseToCache(Response $response): void
    {

    }

    public function replaceInCachedResponse(Response $response): void
    {
        $content = $response->getContent();

        if (!$content) {
            return;
        }

        try {
            $content = json_decode($content, true, 512, JSON_THROW_ON_ERROR);
        } catch (JsonException) {
            return;
        }

        $response->setContent($content);
    }
}
