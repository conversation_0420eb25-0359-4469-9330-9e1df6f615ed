<?php

namespace App\Http\Resources;

use App\Models\Bonus;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin BonusAdminResource
 */
class BonusAdminResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Bonus $bonus */
        $bonus = $this->resource;
        return [
            'id' => $bonus->id,
            'name' => $bonus->name,
            'bonus_name' => $bonus->bonus_name,
            'currency' => $bonus->currency,
            'active' => $bonus->active,
            'casino' => $bonus->casino,
            'bets' => $bonus->bets,
            'type' => $bonus->type,
            'uses' => $bonus->uses,
            'client_id' => $bonus->client_id,
            'description' => $bonus->description,
            'condition' => $bonus->condition,
            'image' => $bonus->image,
            'bonuses' => $bonus->bonuses,
            'bonus_data' => $bonus->data,
            'max_transfers' => $bonus->max_transfers,
            'deposit_factors' => $bonus->deposit_factors,
            'wager' => $bonus->wager,
            'min_deposit' => $bonus->min_deposit,
            'min_factor' => $bonus->min_factor,
            'min_bet' => $bonus->min_bet,
            'crash' => $bonus->crash,
            'total_transferred' => $bonus->total_transferred,
            'transfers' => $bonus->transfers,
            'total_uses' => $bonus->total_uses,
            'is_external' => $bonus->is_external,
            'is_promo' => $bonus->is_promo,
            'weight' => $bonus->weight,
            'active_from' => $bonus->active_from,
            'active_til' => $bonus->active_til,
            'duration' => $bonus->duration,
            'uuid' => $bonus->uuid,
            'is_welcome' => $bonus->is_welcome_group,
            'is_onetime' => $bonus->is_onetime_group,
            'is_no_dep' => $bonus->is_no_dep,
            'is_deposit' => $bonus->is_deposit,
            'is_free_spin_for_deposit' => $bonus->is_free_spin_for_deposit,
            'is_organic' => $bonus->is_organic,
        ];
    }
}
