<?php

namespace App\Http\Resources;

use App\Models\Banner;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BannerCrudResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Banner $banner */
        $banner = $this->resource;

        return [
            'id' => $banner->id,
            'image' => $banner->image,
            'name' => $banner->name,
            'language' => $banner->language,
            'country' => $banner->country,
            'is_for_signed_in' => $banner->is_for_signed_in,
            'platform' => $banner->platform,
            'mobile_apk_install' => $banner->mobile_apk_install,
            'location' => $banner->location,
            'weight' => $banner->weight,
            'type' => $banner->type,
            'disposition' => $banner->disposition,
            'enabled' => $banner->enabled,
            'is_active' => $banner->is_active,
            'smartico_segment_id' => $banner->smartico_segment_id,
            'start_at' => $banner->start_at ? Carbon::parse($banner->start_at)->timestamp : null,
            'end_at' => $banner->end_at ? Carbon::parse($banner->end_at)->timestamp : null,
            'uuid' => $banner->uuid,
        ];
    }
}
