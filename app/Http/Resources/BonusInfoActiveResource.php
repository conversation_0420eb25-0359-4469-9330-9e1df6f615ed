<?php

namespace App\Http\Resources;

use App\Models\Bonus;
use App\Models\BonusInfo;
use App\Models\Traits\ImageLinkFixerTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin BonusInfoActiveResource
 */
class BonusInfoActiveResource extends JsonResource
{
    use ImageLinkFixerTrait;

    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var BonusInfo $resource */
        $resource = $this->resource;

        /** @var null|Bonus $bonusData */
        $bonusData = $resource->bonus ?? null;

        return [
            'id' => $resource->id,
            'name' => $resource->name,
            'currency' => $resource->currency,
            'active' => $resource->active,
            'visible_from' => !empty($resource->visible_from) ? Carbon::parse($resource->visible_from)->timestamp : null,
            'visible_to' => !empty($resource->visible_to) ? Carbon::parse($resource->visible_to)->timestamp : null,
            'timer_from' => $resource->general_visible_from ? Carbon::parse($resource->general_visible_from)->timestamp : null,
            'timer_to' => $resource->general_visible_to ? Carbon::parse($resource->general_visible_to)->timestamp : null,
            'casino' => $resource->casino,
            'is_welcome' => $resource->is_welcome,
            'is_for_smartico' => $resource->is_for_smartico,
            'image' => $this->getImageAttribute($resource->image),
            'bonus_name' => $resource->bonus_name,
            'description' => $resource->description,
            'description_info' => $resource->description_info,
            'condition_title' => $resource->condition_title,
            'condition' => $resource->condition,
            'sort_order' => $resource->sort_order,
            'proceed_link' => $resource->proceed_link,
            'bonus_id' => $resource->bonus_id,
            'created_at' => Carbon::parse($resource->created_at)->timestamp,
            'updated_at' => Carbon::parse($resource->updated_at)->timestamp,
            'colors' => $resource->colors,
            'bonus' => !empty($bonusData) ? [
                'id' => $bonusData->id,
                'type' => $bonusData->type,
                'deposit_factors' => $bonusData->deposit_factors,
                'free_spins_count' => !empty($bonusData->freespin) ? $bonusData->freespin->count : null,
            ] : null,
            'show_visible_date' => $resource->show_visible_date,
            'custom_type' => $resource->custom_type,
            'player_data' => $resource->getAttribute('player_data')
                ? $resource->getAttribute('player_data') : ['deposits_count' => null, 'status' => null],
        ];
    }
}
