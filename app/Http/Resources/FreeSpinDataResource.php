<?php

namespace App\Http\Resources;

use App\Models\FreeSpin;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class FreeSpinDataResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        /** @var FreeSpin $resource */
        $resource = $this->resource;

        $options = $resource->options ?? [];

        return [
            'id' => $resource->id,
            'title' => $resource->title,
            'aggregator' => $resource->aggregator,
            'supplier' => $resource->supplier,
            'slot_id' => $resource->slot_id,
            'slot_name' => $resource->getAttribute('slot')->name ?? null,
            'provider_id' => $resource->provider_id,
            'provider_name' => $resource->getAttribute('provider')->name ?? null,
            'bonus_id' => $resource->bonus_id,
            'type' => $resource->type,
            'name' => $resource->name,
            'count' => $resource->count,
            'bet' => $resource->bet,
            'bet_id' => $resource->bet_id,
            'denomination' => $resource->denomination,
            'currency' => $resource->currency,
            'status' => $resource->status,
            'is_active' => $resource->is_active,
            'author_id' => $resource->author_id,
            'updated_by_author_id' => $resource->updated_by_author_id,
            'in_process' => $resource->in_process,
            'start_at' => Carbon::parse($resource->start_at)->timestamp,
            'expired_at' => Carbon::parse($resource->expired_at)->timestamp,
            'data' => $resource->data,
            'created_at' => $resource->created_at,
            'updated_at' => $resource->updated_at,
            ...$options,
        ];
    }
}
