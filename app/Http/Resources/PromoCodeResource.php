<?php

namespace App\Http\Resources;

use App\Models\PromoCode;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PromoCodeResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var PromoCode $promoCode */
        $promoCode = $this->resource;
        return [
            'id' => $promoCode->id,
            'code' => $promoCode->code,
            'name' => $promoCode->name,
            'stream_id' => $promoCode->stream_id,
            'bonus_id' => $promoCode->bonus_id,
            'description' => $promoCode->description,
            'active' => $promoCode->is_active,
            'limit' => $promoCode->use_limit,
            'uses' => $promoCode->uses,
            'start_at' => $promoCode->start_at ? $promoCode->start_at->getTimestamp() : null,
            'end_at' => $promoCode->end_at ? $promoCode->end_at->getTimestamp() : null,
            'condition' => $promoCode->condition,
            'is_alanbase' => $promoCode->is_alanbase,
            'bonus' => BonusCrudResource::make($promoCode->bonus),
            'created_at' => $promoCode->created_at,
            'updated_at' => $promoCode->updated_at,
            'uuid' => $promoCode->uuid,
        ];
    }
}
