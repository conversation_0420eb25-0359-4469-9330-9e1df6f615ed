<?php

namespace App\Http\Resources;

use App\Models\Bonus;
use App\Models\IssuedBonus;
use App\Traits\MoneyWithCurrencyTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class IssuedBonusResource extends JsonResource
{
    use MoneyWithCurrencyTrait;

    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var IssuedBonus $issuedBonus */
        $issuedBonus = $this->resource;

        $bonus = Bonus::query()->find($issuedBonus->bonus_id);

        return [
            'id' => $issuedBonus->id,
            'player' => ['id' => $issuedBonus->player_id],
            'balance' => $this->geMoneyWithCurrency($issuedBonus->balance, $issuedBonus->currency),
            'in_game' => $this->geMoneyWithCurrency($issuedBonus->in_game, $issuedBonus->currency),
            'min_bet' => $this->geMoneyWithCurrency($issuedBonus->min_bet, $issuedBonus->currency),
            'wager' => $this->geMoneyWithCurrency($issuedBonus->wager, $issuedBonus->currency),
            'transfer' => $this->geMoneyWithCurrency($issuedBonus->transfer, $issuedBonus->currency),
            'active' => $issuedBonus->active,
            'status' => $issuedBonus->status,
            'popup_seen' => $issuedBonus->popup_seen,
            'casino' => $issuedBonus->casino,
            'bets' => $issuedBonus->bets,
            'valid' => $issuedBonus->isValid(),
            'orig_bonus' => $this->geMoneyWithCurrency($issuedBonus->orig_bonus, $issuedBonus->currency),
            'orig_wager' => $this->geMoneyWithCurrency($issuedBonus->orig_wager, $issuedBonus->currency),
            'expire_at' => $issuedBonus->expire_at?->timestamp,
            'bonus' => $bonus ? BonusAdminResource::make($bonus) : null,
            'created_at' => $issuedBonus->created_at?->timestamp,
            'updated_at' => $issuedBonus->updated_at?->timestamp,
            'bonus_external_id' => $issuedBonus->bonus_external_id,
            'type' => $issuedBonus->type,
        ];
    }
}
