<?php

namespace App\Http\Resources;

use App\Models\MassOnetimeBonusInfo;
use Illuminate\Http\Resources\Json\JsonResource;

class BonusApplyResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        /** @var MassOnetimeBonusInfo $massOnetimeBonusInfo */
        $massOnetimeBonusInfo = $this->resource;
        return [
            'id' => $massOnetimeBonusInfo->id,
            'bonus_id' => $massOnetimeBonusInfo->bonus_id,
            'smartico_bonus_id' => $massOnetimeBonusInfo->smartico_bonus_id,
            'player_id' => $massOnetimeBonusInfo->player_id,
            'email' => $massOnetimeBonusInfo->email,
            'amount' => $massOnetimeBonusInfo->amount,
            'status' => $massOnetimeBonusInfo->status,
            'text' => $massOnetimeBonusInfo->text,
            'created_at' => $massOnetimeBonusInfo->created_at,
            'updated_at' => $massOnetimeBonusInfo->updated_at,
        ];
    }
}
