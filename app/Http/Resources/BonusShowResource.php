<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\Bonus;
use App\Models\Traits\ImageLinkFixerTrait;
use Illuminate\Http\Resources\Json\JsonResource;

class BonusShowResource extends JsonResource
{
    use ImageLinkFixerTrait;

    /**
     * @param mixed $request
     *
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        /** @var Bonus $resource */
        $resource = $this->resource;

        return [
            'id' => $resource->id,
            'weight' => $resource->weight,
            'name' => $resource->name,
            'bonus_name' => $resource->bonus_name,
            'description' => $resource->description,
            'short_description' => $resource->short_description,
            'condition' => $resource->condition,
            'type' => $resource->type,
            'image' => $resource->image,
            'max_bonuses' => !$resource->is_free_spin_for_deposit ? $resource->bonuses : null,
            'max_transfers' => $resource->max_transfers,
            'deposit_factors' => $resource->deposit_factors,
            'currency' => $resource->currency,
            'wager' => $resource->wager,
            'min_deposit' => $resource->min_deposit,
            'min_factor' => $resource->min_factor,
            'min_bet' => $resource->min_bet,
            'active' => $resource->active,
            'casino' => $resource->casino,
            'bets' => $resource->bets,
            'crash' => $resource->crash,
            'total_transferred' => $resource->total_transferred,
            'uses' => $resource->uses,
            'transfers' => $resource->transfers,
            'total_uses' => $resource->total_uses,
            'is_external' => $resource->is_external,
            'is_promo' => $resource->is_promo,
            'from' => !empty($resource->active_from) ? $resource->active_from->getTimestamp() : null,
            'to' => !empty($resource->active_til) ? $resource->active_til->getTimestamp() : null,
            'duration' => $resource->duration,
            'bonus_data' => $resource->data,
            'is_welcome' => $resource->is_welcome_group,
            'is_onetime' => $resource->is_onetime_group,
            'is_no_dep' => $resource->is_no_dep,
            'is_deposit' => $resource->is_deposit,
            'is_free_spin_for_deposit' => $resource->is_free_spin_for_deposit,
            'max_real_balance' => $resource->max_real_balance,
            'auto_cancel_withdrawal' => $resource->auto_cancel_withdrawal,
            'slot_ids' => $resource->bonusSlots->pluck('slot_id')->toArray(),
            'slot_provider_ids' => $resource->bonusSlotProviders->pluck('slot_provider_id')->toArray(),
            'colors' => $resource->colors,
        ];
    }
}
