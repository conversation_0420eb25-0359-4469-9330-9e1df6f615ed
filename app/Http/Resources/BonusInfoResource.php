<?php

namespace App\Http\Resources;

use App\Models\Bonus;
use App\Models\BonusInfo;
use App\Models\Traits\ImageLinkFixerTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin BonusInfo
 */
class BonusInfoResource extends JsonResource
{
    use ImageLinkFixerTrait;

    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var BonusInfo $bonusInfo */
        $bonusInfo = $this->resource;
        /** @var null|Bonus $bonusData */
        $bonusData = $bonusInfo->bonus ?? null;

        return [
            'id' => $bonusInfo->id,
            'name' => $bonusInfo->name,
            'currency' => $bonusInfo->currency,
            'active' => $bonusInfo->active,
            'visible_from' => !empty($bonusInfo->visible_from) ? Carbon::parse($bonusInfo->visible_from)->timestamp : null,
            'visible_to' => !empty($bonusInfo->visible_to) ? Carbon::parse($bonusInfo->visible_to)->timestamp : null,
            'timer_from' => $bonusInfo->general_visible_from ? Carbon::parse($bonusInfo->general_visible_from)->timestamp : null,
            'timer_to' => $bonusInfo->general_visible_to ? Carbon::parse($bonusInfo->general_visible_to)->timestamp : null,
            'casino' => $bonusInfo->casino,
            'is_welcome' => $bonusInfo->is_welcome,
            'is_for_smartico' => $bonusInfo->is_for_smartico,
            'image' => $this->getImageAttribute($bonusInfo->image),
            'bonus_name' => $bonusInfo->bonus_name,
            'description' => $bonusInfo->description,
            'description_info' => $bonusInfo->description_info,
            'condition_title' => $bonusInfo->condition_title,
            'condition' => $bonusInfo->condition,
            'sort_order' => $bonusInfo->sort_order,
            'proceed_link' => $bonusInfo->proceed_link,
            'bonus_id' => $bonusInfo->bonus_id,
            'created_at' => Carbon::parse($bonusInfo->created_at)->timestamp,
            'updated_at' => Carbon::parse($bonusInfo->updated_at)->timestamp,
            'colors' => $bonusInfo->colors,
            'bonus' => !empty($bonusData) ? [
                'id' => $bonusData->id,
                'type' => $bonusData->type,
                'deposit_factors' => $bonusData->deposit_factors,
                'max_bonuses' => $bonusData->bonuses ? $bonusData->bonuses[0] : null,
            ] : null,
            'show_visible_date' => $bonusInfo->show_visible_date,
            'custom_type' => $bonusInfo->custom_type,
        ];
    }
}
