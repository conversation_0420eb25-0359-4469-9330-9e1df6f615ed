<?php

namespace App\Http\Resources;

use App\Models\BonusTriggerSession;
use App\Models\Traits\ImageLinkFixerTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TriggerSessionsDataResource extends JsonResource
{
    use ImageLinkFixerTrait;

    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var BonusTriggerSession $resource */
        $resource = $this->resource;

        return [
            'id' => $resource->id,
            'start_at' => !empty($resource->start_at) ? Carbon::parse($resource->start_at)->timestamp : null,
            'end_at' => !empty($resource->end_at) ? Carbon::parse($resource->end_at)->timestamp : null,
        ];
    }
}
