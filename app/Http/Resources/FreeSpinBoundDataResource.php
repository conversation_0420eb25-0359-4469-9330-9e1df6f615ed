<?php

namespace App\Http\Resources;

use App\Models\FreeSpinBound;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class FreeSpinBoundDataResource
 */
class FreeSpinBoundDataResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        /** @var FreeSpinBound $freeSpinBound */
        $freeSpinBound = $this->resource;

        return [
            'id' => $freeSpinBound->id,
            'free_spin_id' => $freeSpinBound->free_spin_id,
            'uuid' => $freeSpinBound->uuid,
            'player_id' => $freeSpinBound->player_id,
            'bonus_id' => $freeSpinBound->bonus_id,
            'bet_id' => $freeSpinBound->bet_id,
            'bet' => $freeSpinBound->bet,
            'total_win' => $freeSpinBound->total_win,
            'count_left' => $freeSpinBound->count_left,
            'is_active' => $freeSpinBound->is_active,
            'is_archived' => $freeSpinBound->is_archived,
            'message' => $freeSpinBound->message,
            'start_at' => $freeSpinBound->start_at,
            'expire_at' => $freeSpinBound->expire_at,
            'canceled_at' => $freeSpinBound->canceled_at,
            'is_user_notified' => $freeSpinBound->is_user_notified,
            'created_at' => $freeSpinBound->created_at,
            'updated_at' => $freeSpinBound->updated_at,
        ];
    }
}
