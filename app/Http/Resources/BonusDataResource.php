<?php

namespace App\Http\Resources;

use App\Models\Bonus;
use App\Models\Traits\ImageLinkFixerTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BonusDataResource extends JsonResource
{
    use ImageLinkFixerTrait;

    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Bonus $resource */
        $resource = $this->resource;

        return [
            'id' => $resource->id,
            'name' => $resource->name,
            'image' => $this->getImageAttribute($resource->image),
            'description' => $resource->description,
            'condition' => $resource->condition,
            'bonus_name' => $resource->bonus_name,
            'currency' => $resource->currency,
            'max_transfers' => $resource->max_transfers,
            'deposit_factors' => $resource->deposit_factors,
            'wager' => $resource->wager,
            'active' => $resource->active,
            'duration' => $resource->duration,
            'type' => $resource->type,
            'from' => !empty($resource->active_from) ? Carbon::parse($resource->active_from)->timestamp : null,
            'to' => !empty($resource->active_til) ? Carbon::parse($resource->active_til)->timestamp : null,
            'triggerSessions' => $resource->triggerSessions(),
            'is_organic' => $resource->is_organic,
        ];
    }
}
