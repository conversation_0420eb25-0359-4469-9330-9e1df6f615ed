<?php

namespace App\Http\Resources;

class FreeSpinShowResource extends FreeSpinResource
{
    public function toArray($request): array
    {
        $data = parent::toArray($request);

        $data['users'] = [];
        $uuids = $this->resource->players->pluck('uuid')->toArray();

        foreach ($uuids as $uuid) {
            $data['users'][] = ['player_info' => ['uuid' => $uuid]];
        }

        return $data;
    }
}
