<?php

namespace App\Http\Resources;

use App\Models\Bonus;
use App\Models\Traits\ImageLinkFixerTrait;
use App\Traits\MoneyWithCurrencyTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BonusForPlayerResource extends JsonResource
{
    use ImageLinkFixerTrait;
    use MoneyWithCurrencyTrait;

    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Bonus $resource */
        $resource = $this->resource;

        return [
            'id' => $resource->id,
            'name' => $resource->name,
            'bonus_name' => $resource->bonus_name,
            'description' => $resource->description,
            'condition' => $resource->condition,
            'type' => $resource->type,
            'image' => $this->getImageAttribute($resource->image),
            'crash' => $resource->crash,
            'max_bonus' => $this->geMoneyWithCurrency($resource->bonuses[0] ?? null, $resource->currency),
            'max_bonuses' => !$resource->is_free_spin_for_deposit ? $resource->bonuses : null,
            'max_transfers' => $resource->max_transfers,
            'deposit_factors' => $resource->deposit_factors,
            'currency' => $resource->currency,
            'wager' => $resource->wager,
            'min_deposit' => $resource->min_deposit ? (string)($resource->min_deposit) : null,
            'min_bet' => $resource->min_bet ? (string)($resource->min_bet) : null,
            'active' => $resource->active,
            'casino' => $resource->casino,
            'bets' => $resource->bets,
            'from' => !empty($resource->active_from) ? $resource->active_from->getTimestamp() : null,
            'to' => !empty($resource->active_til) ? $resource->active_til->getTimestamp() : null,
            'duration' => $resource->duration,
            'bonus_data' => $resource->data,
            'is_welcome' => $resource->is_welcome_group,
            'is_onetime' => $resource->is_onetime_group,
            'is_no_dep' => $resource->is_no_dep,
            'is_deposit' => $resource->is_deposit,
            'is_free_spin_for_deposit' => $resource->is_free_spin_for_deposit,
            'weight' => $resource->weight,
            'is_organic' => $resource->is_organic,
            'triggerSessions' => $resource->triggerSessions->isNotEmpty()
                ? TriggerSessionsDataResource::collection($resource->triggerSessions)
                : [],
        ];
    }
}
