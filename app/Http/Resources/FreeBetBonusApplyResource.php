<?php

namespace App\Http\Resources;

use App\Models\MassFreeBetBonusInfo;
use Illuminate\Http\Resources\Json\JsonResource;

class FreeBetBonusApplyResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        /** @var MassFreeBetBonusInfo $massFreeBetBonusInfo */
        $massFreeBetBonusInfo = $this->resource;

        return [
            'id' => $massFreeBetBonusInfo->id,
            'bonus_id' => $massFreeBetBonusInfo->bonus_id,
            'smartico_bonus_id' => $massFreeBetBonusInfo->smartico_bonus_id,
            'external_player_id' => $massFreeBetBonusInfo->external_player_id,
            'player_id' => $massFreeBetBonusInfo->player_id,
            'template_id' => $massFreeBetBonusInfo->template_id,
            'amount' => $massFreeBetBonusInfo->amount,
            'currency' => $massFreeBetBonusInfo->currency,
            'status' => $massFreeBetBonusInfo->status,
            'text' => $massFreeBetBonusInfo->text,
            'created_at' => $massFreeBetBonusInfo->created_at,
            'updated_at' => $massFreeBetBonusInfo->updated_at,
        ];
    }
}
