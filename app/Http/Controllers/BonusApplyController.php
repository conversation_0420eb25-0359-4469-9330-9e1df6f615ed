<?php

namespace App\Http\Controllers;

use App\Enums\BonusTypeEnum;
use App\Exceptions\Exception;
use App\Exceptions\InvalidArgumentException;
use App\Http\Requests\ApplyBonusSmarticoOnetimeRequest;
use App\Http\Requests\BonusApplyForPlayerRequest;
use App\Http\Requests\MassBonusApplyRequest;
use App\Http\Requests\BonusApplyViewRequest;
use App\Http\Resources\BonusApplyResource;
use App\Http\Resources\IssuedBonusResource;
use App\Services\Dto\ApplyBonusSmarticoOnetimeDTO;
use App\Http\Resources\FreeBetBonusApplyResource;
use App\Services\Dto\BonusApplyForPlayerDTO;
use App\Services\Dto\BonusApplySearchDTO;
use App\Services\Dto\MassBonusApplyDTO;
use App\Services\FreeBetBonusService;
use App\Services\OneTimeBonusService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class BonusApplyController extends Controller
{
    public function __construct(
        protected FreeBetBonusService $freeBetBonusService,
        protected OneTimeBonusService $oneTimeBonusService,
    ) {
    }

    public function index(BonusApplyViewRequest $request): AnonymousResourceCollection
    {
        /** @var BonusApplySearchDTO $dto */
        $dto = BonusApplySearchDTO::fromArray($request->validated());

        if ($request->bonus_type === BonusTypeEnum::FREEBET->value) {
            return FreeBetBonusApplyResource::collection($this->freeBetBonusService->list($dto));
        }

        return BonusApplyResource::collection($this->oneTimeBonusService->list($dto));
    }

    /**
     * @return string[]
     */
    public function massBonusApply(MassBonusApplyRequest $request): array
    {
        /** @var MassBonusApplyDTO $dto */
        $dto = MassBonusApplyDTO::fromArray($request->validated());

        if ($request->bonus_type === BonusTypeEnum::FREEBET->value) {
            $this->freeBetBonusService->startApplyJob($dto);
        } else {
            $this->oneTimeBonusService->startApplyJob($dto);
        }

        return ['ok'];
    }

    /**
     * @throws Exception|\Exception
     *
     * @return array<string, string|bool>
     */
    public function applyBonusForPlayer(BonusApplyForPlayerRequest $request): array
    {
        /** @var BonusApplyForPlayerDTO $dto */
        $dto = BonusApplyForPlayerDTO::fromArray($request->validated());

        if ($request->bonus_type === BonusTypeEnum::ONETIME->value) {
            return $this->oneTimeBonusService->applyBonusForPlayer($dto);
        }

        return $this->freeBetBonusService->applyBonusForPlayer($dto);
    }

    /**
     * @throws InvalidArgumentException
     */
    public function applyBonusSmarticoOnetime(ApplyBonusSmarticoOnetimeRequest $request): IssuedBonusResource
    {
        /** @var ApplyBonusSmarticoOnetimeDTO $dto */
        $dto = ApplyBonusSmarticoOnetimeDTO::fromArray($request->validated());
        $balance = $this->oneTimeBonusService->applySmarticoBonusOneTime($dto);

        return IssuedBonusResource::make($balance);
    }
}
