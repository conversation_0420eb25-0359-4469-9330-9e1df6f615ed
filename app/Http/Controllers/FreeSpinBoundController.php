<?php

namespace App\Http\Controllers;

use App\Exceptions\Exception;
use App\Http\Requests\FreeSpinAttachRequest;
use App\Http\Requests\FreeSpinCancelBoundRequest;
use App\Http\Requests\FreeSpinShowRequest;
use App\Http\Requests\GetFreeSpinBoundsRequest;
use App\Http\Requests\PlayerBoundActiveRequest;
use App\Http\Requests\UpdateBoundRequest;
use App\Http\Resources\FreeSpinBoundDataResource;
use App\Http\Resources\FreeSpinBoundResource;
use App\Services\Dto\FreeSpinBoundSearchDTO;
use App\Services\Dto\GetFreeSpinBoundsDTO;
use App\Services\Dto\PlayerBoundSearchDTO;
use App\Services\Dto\UpdateBoundDTO;
use App\Services\FreeSpinBoundService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

/**
 * Class FreeSpinBoundController
 */
class FreeSpinBoundController extends Controller
{
    /**
     * FreeSpinsController constructor.
     */
    public function __construct(
        private readonly FreeSpinBoundService $freeSpinBoundService,
    ) {
    }

    public function getFreeSpinBounds(GetFreeSpinBoundsRequest $request): AnonymousResourceCollection
    {
        /** @var GetFreeSpinBoundsDTO $dto */
        $dto = GetFreeSpinBoundsDTO::fromArray($request->validated());
        $bounds = $this->freeSpinBoundService->searchFreeSpinBoundsWithoutPagination($dto);

        return FreeSpinBoundDataResource::collection($bounds);
    }

    public function updateBound(UpdateBoundRequest $request): FreeSpinBoundDataResource
    {
        /** @var UpdateBoundDTO $dto */
        $dto = UpdateBoundDTO::fromArray($request->validated());
        $bounds = $this->freeSpinBoundService->updateBound($dto);

        return FreeSpinBoundDataResource::make($bounds);
    }

    /**
     * @throws Exception|\Exception
     *
     * @return string[]
     */
    public function attach(FreeSpinAttachRequest $request): array
    {
        $response =  $this->freeSpinBoundService->attachFreeSpinToPlayer($request->bonus_id, $request->player_id);

        return [$response ? 'ok' : 'failed'];
    }

    /**
     * @param FreeSpinCancelBoundRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function cancelBound(FreeSpinCancelBoundRequest $request): JsonResponse
    {
        $response = $this->freeSpinBoundService->cancelFreeSpinBound($request->id, (int)$request->attributes->get('playerId'));

        if (!$response) {
            $freeSpinBound = $this->freeSpinBoundService->findById($request->id);

            return response()->json([
                'status' => 'failed',
                'message' => $freeSpinBound?->message,
            ], 422);
        }

        return response()->json(['status' => 'ok']);
    }

    public function playerMeFreeSpinsByStatus(Request $request, string $status): AnonymousResourceCollection
    {
        $dto = PlayerBoundSearchDTO::fromArray(['status' => $status, 'player_id' => $request->attributes->get('playerId')]);
        $freespin = $this->freeSpinBoundService->getPlayerFreeSpinsByStatus($dto);

        return FreeSpinBoundResource::collection($freespin);
    }

    public function playerBoundHistory(int $playerId): AnonymousResourceCollection
    {
        $dto = PlayerBoundSearchDTO::fromArray(['status' => 'history', 'player_id' => $playerId]);
        $freespin = $this->freeSpinBoundService->getPlayerFreeSpinsByStatus($dto);

        return FreeSpinBoundResource::collection($freespin);
    }

    public function playerBoundActive(PlayerBoundActiveRequest $request): AnonymousResourceCollection
    {
        $dto = PlayerBoundSearchDTO::fromArray(array_merge($request->validated(), ['status' => 'active']));
        $freespin = $this->freeSpinBoundService->getPlayerFreeSpinsByStatus($dto);

        return FreeSpinBoundResource::collection($freespin);
    }

    public function getFreeSpinBoundsByFreeSpinID(FreeSpinShowRequest $request): LengthAwarePaginator
    {
        $dto = FreeSpinBoundSearchDTO::fromArray($request->validated());

        return $this->freeSpinBoundService->getFreeSpinBoundList($dto);
    }
}
