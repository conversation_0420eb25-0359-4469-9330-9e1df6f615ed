<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Exceptions\Exception;
use App\Http\Requests\CheckFreeSpinRequest;
use App\Http\Requests\FreeSpinCancelRequest;
use App\Http\Requests\FreeSpinCreateRequest;
use App\Http\Requests\FreeSpinResendRequest;
use App\Http\Requests\FreeSpinSearchRequest;
use App\Http\Requests\FreeSpinShowRequest;
use App\Http\Requests\FreeSpinUpdateRequest;
use App\Http\Resources\FreeSpinResource;
use App\Http\Resources\FreeSpinShowResource;
use App\Services\Dto\FreeSpinCrudDto;
use App\Services\Dto\FreeSpinSearchDto;
use App\Services\FreeSpinService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Throwable;

/**
 * Class FreeSpinsController
 */
class FreeSpinController extends Controller
{
    /**
     * FreeSpinsController constructor.
     */
    public function __construct(private readonly FreeSpinService $freeSpinService)
    {
    }

    public function list(FreeSpinSearchRequest $request): AnonymousResourceCollection
    {
        /** @var FreeSpinSearchDto $dto */
        $dto = FreeSpinSearchDto::fromArray($request->validated());

        return FreeSpinResource::collection($this->freeSpinService->list($dto));
    }

    public function show(FreeSpinShowRequest $request): FreeSpinResource
    {
        return new FreeSpinShowResource($this->freeSpinService->getFreeSpinById($request->id));
    }

    /**
     * @throws Throwable
     */
    public function create(FreeSpinCreateRequest $request): JsonResponse
    {
        /** @var FreeSpinCrudDto $dto */
        $dto = FreeSpinCrudDto::fromArray($request->validated());

        return $this->success([
            'data' => new FreeSpinResource($this->freeSpinService->store($dto)),
        ]);
    }

    /**
     * @throws Exception
     */
    public function update(FreeSpinUpdateRequest $request): FreeSpinResource
    {
        /** @var FreeSpinCrudDto $dto */
        $dto = FreeSpinCrudDto::fromArray($request->validated());

        return new FreeSpinResource($this->freeSpinService->updateFreeSpin($dto));
    }

    public function delete(FreeSpinCancelRequest $request): JsonResponse
    {
        return $this->success([
            'status' => $this->freeSpinService->delete($request->id, $request->author_email),
        ]);
    }

    /**
     * @throws Exception
     *
     * @return string[]
     */
    public function resend(FreeSpinResendRequest $request): array
    {
        $this->freeSpinService->resendFreeSpin($request->freespin_id, $request->freespin_bound_id);

        return ['ok'];
    }

    /**
     * @return array<string, bool>
     */
    public function checkFreeSpinName(CheckFreeSpinRequest $request): array
    {
        $name = $request->input('name');
        $id = $request->input('id');

        return $this->freeSpinService->checkFreeSpinName($name, $id);
    }
}
