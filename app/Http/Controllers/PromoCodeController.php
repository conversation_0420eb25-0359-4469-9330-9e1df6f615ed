<?php

namespace App\Http\Controllers;

use App\Exceptions\Exception;
use App\Http\Requests\GetAlanbasePromoCodeRequest;
use App\Http\Requests\PlayerPromoCodesRequest;
use App\Http\Requests\PromoCodeApplyRequest;
use App\Http\Requests\PromoCodeApplyToPlayerRequest;
use App\Http\Requests\PromoCodeCheckRequest;
use App\Http\Requests\PromoCodeCreateRequest;
use App\Http\Requests\PromoCodeViewRequest;
use App\Http\Resources\PromoCodeResource;
use App\Services\Dto\PlayerPromoCodesDTO;
use App\Models\PromoCode;
use App\Services\Dto\PromoCodeDTO;
use App\Services\Dto\PromoCodeSearchDTO;
use App\Services\PromoCodeService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class PromoCodeController extends Controller
{
    public function __construct(
        protected readonly PromoCodeService $promoCodeService
    ) {
    }

    /**
     * @throws Exception
     */
    public function create(PromoCodeCreateRequest $request): PromoCodeResource
    {
        /** @var PromoCodeDTO $dto */
        $dto = PromoCodeDTO::fromArray($request->validated());

        return PromoCodeResource::make($this->promoCodeService->create($dto));
    }

    public function index(PromoCodeViewRequest $request): AnonymousResourceCollection
    {
        /** @var PromoCodeSearchDTO $dto */
        $dto = PromoCodeSearchDTO::fromArray($request->validated());

        return PromoCodeResource::collection($this->promoCodeService->list($dto));
    }

    public function update(PromoCodeCreateRequest $request): PromoCodeResource
    {
        /** @var PromoCodeDTO $dto */
        $dto = PromoCodeDTO::fromArray($request->validated());

        return PromoCodeResource::make($this->promoCodeService->update($dto));
    }

    public function check(PromoCodeCheckRequest $request): PromoCodeResource
    {
        return PromoCodeResource::make($this->promoCodeService->getPromoCode($request->code));
    }

    /**
     * @throws Exception
     */
    public function apply(PromoCodeApplyRequest $request): PromoCodeResource
    {
        return PromoCodeResource::make($this->promoCodeService->apply($request->code, $request->player_uuid));
    }

    /**
     * @throws Exception
     */
    public function applyToPlayer(PromoCodeApplyToPlayerRequest $request): PromoCodeResource
    {
        return PromoCodeResource::make($this->promoCodeService->apply($request->code, $request->player_uuid));
    }

    public function getAlanbasePromoCodeByPlayerId(GetAlanbasePromoCodeRequest $request): ?PromoCode
    {
        return $this->promoCodeService->getAlanbasePromoCodeByPlayerId($request->player_id);
    }

    public function playerPromoCodes(PlayerPromoCodesRequest $request): AnonymousResourceCollection
    {
        /** @var PlayerPromoCodesDTO $dto */
        $dto = PlayerPromoCodesDTO::fromArray($request->validated());
        return PromoCodeResource::collection($this->promoCodeService->playerPromoCodes($dto));
    }
}
