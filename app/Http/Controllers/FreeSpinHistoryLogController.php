<?php

namespace App\Http\Controllers;

use App\Http\Requests\FreeSpinHistoryLogRequest;
use App\Http\Resources\FreeSpinHistoryLogResource;
use App\Services\Dto\FreeSpinHistoryLogDTO;
use App\Services\FreeSpinHistoryLogService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

/**
 * Class FreeSpinHistoryLogController
 */
class FreeSpinHistoryLogController extends Controller
{
    public function __construct(
        private readonly FreeSpinHistoryLogService $freeSpinHistoryLogService,
    ) {
    }

    public function list(FreeSpinHistoryLogRequest $request): AnonymousResourceCollection
    {
        /** @var FreeSpinHistoryLogDTO $dto */
        $dto = FreeSpinHistoryLogDTO::fromArray(
            array_merge($request->validated(), [
                'pagination' => [
                    'page' => $request->pagination['page'] ?? 1,
                    'limit' => $request->pagination['limit'] ?? 10,
                ],
            ])
        );
        $logs = $this->freeSpinHistoryLogService->list($dto);

        return FreeSpinHistoryLogResource::collection($logs);
    }
}
