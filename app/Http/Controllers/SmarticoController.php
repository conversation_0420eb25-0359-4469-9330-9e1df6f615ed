<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\SmarticoIssueFreeSpinRequest;
use App\Http\Requests\SmarticoIssueUserBonusEventRequest;
use App\Services\Dto\SmarticoIssueBonusToPlayerDTO;
use App\Services\Dto\SmarticoIssueFreeSpinToPlayerDTO;
use App\Services\SmarticoService;
use Exception;
use Illuminate\Http\JsonResponse;

class SmarticoController extends Controller
{
    public function __construct(
        private readonly SmarticoService $smarticoService
    ) {
    }

    /**
     * @throws Exception
     */
    public function issuePlayerBonus(SmarticoIssueUserBonusEventRequest $request): JsonResponse
    {
        /** @var SmarticoIssueBonusToPlayerDTO $dto */
        $dto = SmarticoIssueBonusToPlayerDTO::fromArray($request->validated());
        $response = $this->smarticoService->issueBonus($dto);

        if ($response['status']) {
            return $this->success($response);
        } else {
            return $this->error($response);
        }
    }

    public function issueFreeSpin(SmarticoIssueFreeSpinRequest $request): JsonResponse
    {
        /** @var SmarticoIssueFreeSpinToPlayerDTO $dto */
        $dto = SmarticoIssueFreeSpinToPlayerDTO::fromArray($request->validated());
        $response = $this->smarticoService->issueFreeSpin($dto);

        if ($response['status']) {
            return $this->success($response);
        } else {
            return $this->error($response);
        }
    }
}
