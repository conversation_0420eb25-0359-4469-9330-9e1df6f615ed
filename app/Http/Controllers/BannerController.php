<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\BannerChangeStatusManyRequest;
use App\Http\Requests\BannerChangeStatusRequest;
use App\Http\Requests\BannerCreateRequest;
use App\Http\Requests\BannerEnabledRequest;
use App\Http\Requests\BannerRemoveRequest;
use App\Http\Requests\SearchBannersRequest;
use App\Http\Resources\BannerCrudResource;
use App\Services\BannerService;
use App\Services\Dto\BannerCreateDTO;
use App\Services\Dto\BannerEnabledDTO;
use App\Services\Dto\SearchBannersDTO;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class BannerController extends Controller
{
    public function __construct(
        private readonly BannerService $bannerService
    ) {
    }

    public function index(SearchBannersRequest $request): AnonymousResourceCollection
    {
        /** @var SearchBannersDTO $searchBannersDTO */
        $searchBannersDTO = SearchBannersDTO::fromArray($request->validated());

        return BannerCrudResource::collection($this->bannerService->list($searchBannersDTO));
    }

    public function create(BannerCreateRequest $request): BannerCrudResource
    {
        $bannerCreateDTO = BannerCreateDTO::fromArray($request->validated());

        /** @var BannerCreateDTO $bannerCreateDTO*/
        return BannerCrudResource::make($this->bannerService->create($bannerCreateDTO));
    }

    public function update(BannerCreateRequest $request, int $id): BannerCrudResource
    {
        $bannerCreateDTO = BannerCreateDTO::fromArray($request->validated());

        /** @var BannerCreateDTO $bannerCreateDTO*/
        return BannerCrudResource::make($this->bannerService->update($bannerCreateDTO, $id));
    }

    public function changeStatus(BannerChangeStatusRequest $request, int $id): JsonResponse
    {
        return $this->success([
            'status' => $this->bannerService->changeStatus($id, (bool)$request->validated()['enabled']),
        ]);
    }

    public function changeStatusMany(BannerChangeStatusManyRequest $request): JsonResponse
    {
        $outputDTO = $this->bannerService->changeStatusMany($request->banners, $request->validated()['enabled']);
        return $this->success([
            'data' => [
                'processed_ids' => $outputDTO->processedIds,
                'non_processed_ids' => $outputDTO->nonProcessedIds,
            ],
            'status' => $outputDTO->status,
        ]);
    }

    public function delete(BannerRemoveRequest $request): JsonResponse
    {
        return $this->success([
            'status' => $this->bannerService->delete((int)$request->id),
        ]);
    }

    public function enabledBanners(BannerEnabledRequest $request): AnonymousResourceCollection
    {
        /** @var BannerEnabledDTO $dto */
        $dto = BannerEnabledDTO::fromArray($request->validated());

        return BannerCrudResource::collection($this->bannerService->enabledBanners($dto));
    }
}
