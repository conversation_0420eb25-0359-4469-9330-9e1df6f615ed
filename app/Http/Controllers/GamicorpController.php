<?php

namespace App\Http\Controllers;

use App\Http\Requests\FreeSpinFinishRequest;
use App\Services\FreeSpinBoundService;
use Exception as BaseException;

class GamicorpController extends Controller
{
    public function __construct(
        private readonly FreeSpinBoundService $freeSpinBoundService
    ) {
    }

    /**
     * @throws BaseException
     *
     * @return array<string, string|int>
     */
    public function finish(FreeSpinFinishRequest $request): array
    {
        return $this->freeSpinBoundService->freeSpinFinish(
            (int)$request->input('freebet_id'),
            (int)($request->input('amount') * 100)
        );
    }
}
