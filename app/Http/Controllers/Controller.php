<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller as BaseController;

class Controller extends BaseController
{
    use AuthorizesRequests;
    use ValidatesRequests;

    /**
     * @phpstan-ignore-next-line
     */
    public function success(array $data = []): JsonResponse
    {
        return response()->json($data);
    }

    /**
     * @phpstan-ignore-next-line
     */
    public function error(array $data = []): JsonResponse
    {
        return response()->json($data, 400);
    }
}
