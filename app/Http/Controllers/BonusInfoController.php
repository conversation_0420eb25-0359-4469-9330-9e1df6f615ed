<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\BonusInfoCreateRequest;
use App\Http\Requests\BonusInfoSearchRequest;
use App\Http\Requests\BonusInfoUpdateRequest;
use App\Http\Requests\EnabledBonusesInfoForAdminRequest;
use App\Http\Requests\EnabledBonusesInfoRequest;
use App\Http\Resources\BonusInfoActiveResource;
use App\Services\Helpers\CollectionPaginator;
use App\Services\PlayerService;
use App\Services\RealIpService;
use App\Http\Resources\BonusInfoResource;
use App\Services\BonusInfoService;
use App\Services\Dto\ActiveBonusesInfoDTO;
use App\Services\Dto\BonusInfoCreateDTO;
use App\Services\Dto\BonusInfoSearchDTO;
use App\Services\Dto\BonusInfoUpdateDTO;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class BonusInfoController extends Controller
{
    public function __construct(
        private readonly BonusInfoService $bonusInfoService
    ) {
    }

    public function index(BonusInfoSearchRequest $request): AnonymousResourceCollection
    {
        /** @var BonusInfoSearchDTO $dto */
        $dto = BonusInfoSearchDTO::fromArray($request->validated());

        return BonusInfoResource::collection($this->bonusInfoService->list($dto));
    }

    public function create(BonusInfoCreateRequest $request): BonusInfoResource
    {
        /** @var BonusInfoCreateDTO $dto */
        $dto = BonusInfoCreateDTO::fromArray($request->validated());

        return BonusInfoResource::make($this->bonusInfoService->create($dto));
    }

    public function update(BonusInfoUpdateRequest $request): BonusInfoResource
    {
        /** @var BonusInfoUpdateDTO $dto */
        $dto = BonusInfoUpdateDTO::fromArray($request->validated());

        return BonusInfoResource::make($this->bonusInfoService->update($dto));
    }

    public function enabledBonusesInfo(EnabledBonusesInfoRequest $request, RealIpService $realIpService): AnonymousResourceCollection
    {
        $data = $request->validated();
        if (isset($data['welcome_bonus']) && !is_numeric($data['welcome_bonus'])) {
            $data['welcome_bonus'] = null;
        }
        /** @var ActiveBonusesInfoDTO $dto */
        $dto = ActiveBonusesInfoDTO::fromArray($data);

        if (!$dto->playerId || !$request->get('player_uuid')) {
            Log::info('bonus-info-enabled. user without token', [
                'real-client-ip' => $realIpService->getIpAddress(),
            ]);

            return Cache::remember(
                'active_bonuses_info_cards:' . $dto->currency,
                config('responsecache.cache_lifetime_in_seconds'),
                fn() => BonusInfoActiveResource::collection(
                    $this->bonusInfoService->getActiveBonusesInfoCards($dto)
                )
            );
        }

        return BonusInfoActiveResource::collection(
            $this->bonusInfoService->getActiveBonuses($dto, $request->get('player_uuid'))
        );
    }

    /**
     * @phpstan-ignore-next-line
     */
    public function enabledBonusesInfoForAdmin(EnabledBonusesInfoForAdminRequest $request): array
    {
        /** @var ActiveBonusesInfoDTO $dto */
        $dto = ActiveBonusesInfoDTO::fromArray($request->validated());

        $player = app(PlayerService::class)->getPlayerById($dto->playerId);

        $data = BonusInfoActiveResource::collection($this->bonusInfoService->getActiveBonuses($dto, $player->uuid));

        return CollectionPaginator::paginate(
            $data,
            isset($dto->pagination['page']) ? (int)$dto->pagination['page'] : 1,
            isset($dto->pagination['limit']) ? (int)$dto->pagination['limit'] : 15
        );
    }

    public function enabledBonusesInfoCards(EnabledBonusesInfoRequest $request): AnonymousResourceCollection
    {
        $data = $request->validated();
        if (isset($data['welcome_bonus']) && !is_numeric($data['welcome_bonus'])) {
            $data['welcome_bonus'] = null;
        }
        /** @var ActiveBonusesInfoDTO $dto */
        $dto = ActiveBonusesInfoDTO::fromArray($data);

        return BonusInfoActiveResource::collection($this->bonusInfoService->getActiveBonusesInfoCards($dto));
    }
}
