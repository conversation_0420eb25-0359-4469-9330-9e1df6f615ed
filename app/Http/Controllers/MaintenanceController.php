<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\Maintenance\ClearCacheRequest;
use App\Services\RealIpService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Cache\Repository;
use Throwable;

class MaintenanceController extends Controller
{
    public function clearCache(
        ClearCacheRequest $request,
        RealIpService $realIpService,
        Repository $cache
    ): JsonResponse {
        if (!App::environment(['local', 'dev', 'staging'])) {
            Log::warning('Attempt to clear cache in unauthorized environment', [
                'ip' => $realIpService->getIpAddress(),
            ]);

            return $this->error([
                'message' => 'This operation is only allowed in Dev/Staging environments',
            ]);
        }

        try {
            if ($request->keys) {
                $cache->deleteMultiple($request->keys);
            } else {
                $cache->flush();
            }

            return $this->success([
                'message' => 'Cache cleared successfully',
            ]);
        } catch (Throwable $e) {
            return $this->error([
                'message' => 'Failed to clear cache: ' . $e->getMessage(),
            ]);
        }
    }
}
