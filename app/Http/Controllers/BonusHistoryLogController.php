<?php

namespace App\Http\Controllers;

use App\Http\Requests\BonusHistoryLogRequest;
use App\Http\Resources\BonusHistoryLogResource;
use App\Services\BonusHistoryLogService;
use App\Services\Dto\BonusHistoryLogDTO;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

/**
 * Class BonusHistoryLogController
 */
class BonusHistoryLogController extends Controller
{
    public function __construct(
        private readonly BonusHistoryLogService $bonusHistoryLogService,
    ) {
    }

    public function list(BonusHistoryLogRequest $request): AnonymousResourceCollection
    {
        /** @var BonusHistoryLogDTO $dto */
        $dto = BonusHistoryLogDTO::fromArray(
            array_merge($request->validated(), [
                'pagination' => [
                    'page' => $request->pagination['page'] ?? 1,
                    'limit' => $request->pagination['limit'] ?? 10,
                ]
            ])
        );
        $logs = $this->bonusHistoryLogService->list($dto);
        return BonusHistoryLogResource::collection($logs);
    }
}
