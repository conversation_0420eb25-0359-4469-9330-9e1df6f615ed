<?php

namespace App\Http\Controllers;

use App\Http\Requests\DepositSelectedBonusRequest;
use App\Services\BonusApplyService;
use App\Services\Dto\DepositSelectedBonusDTO;
use Exception;
use Illuminate\Http\JsonResponse;

class DepositController extends Controller
{
    public function __construct(
        private readonly BonusApplyService $bonusApplyService
    ) {
    }

    /**
     * @throws Exception
     */
    public function depositSelectedBonus(DepositSelectedBonusRequest $request): JsonResponse
    {
        $dto = DepositSelectedBonusDTO::fromArray($request->validated());

        $response = $this->bonusApplyService->depositSelectedBonus($dto);

        if ($response) {
            return $this->success(['success']);
        }

        return $this->error(['error']);
    }
}
