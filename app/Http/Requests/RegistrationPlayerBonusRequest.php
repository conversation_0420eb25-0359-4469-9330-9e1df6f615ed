<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class RegistrationPlayerBonusRequest
 *
 * @property int $bonus_id
 * @property int $player_id
 * @property string $player_uuid
 * @property bool $is_from_link
 */
class RegistrationPlayerBonusRequest extends BaseRequest
{
    /**
     * @return array<string, string>
     */
    public function rules(): array
    {
        return [
            'bonus_id' => 'required|numeric|exists:bonuses,id',
            'player_id' => 'required|numeric|min:1',
            'player_uuid' => 'required|string|uuid',
            'is_from_link' => 'required|boolean',
        ];
    }
}
