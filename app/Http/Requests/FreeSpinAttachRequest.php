<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class FreeSpinAttachRequest
 *
 * @property int $bonus_id
 * @property int $player_id
 */
class FreeSpinAttachRequest extends BaseRequest
{
    /**
     * @return array<string, string>
     */
    public function rules(): array
    {
        return [
            'bonus_id' => 'required|numeric',
            'player_id' => 'required|numeric',
        ];
    }
}
