<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Rules\CanChangeBannerEnabledStatus;

/**
 * Class BannerChangeStatusRequest
 *
 * @property int $id
 * @property bool $enabled
 */
class BannerChangeStatusRequest extends BaseRequest
{
    /**
     * @return array<string, array<int, string|CanChangeBannerEnabledStatus>>
     */
    public function rules(): array
    {
        /** @var int|string $id */
        $id = $this->route('id');
        $bannerId = (int)$id;

        $this->merge([
            'id' => $bannerId,
        ]);

        return [
            'id' => ['required', 'integer', 'exists:banners'],
            'enabled' => ['required', 'boolean', new CanChangeBannerEnabledStatus($bannerId)],
        ];
    }
}
