<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Traits\PaginationRulesRequestTrait;

/**
 * Class FreeSpinHistoryLogRequest
 *
 * @property int $id
 */
class FreeSpinHistoryLogRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    public function authorize(): bool
    {
        $this->merge([
            'id' => $this->route('id'),
        ]);

        return true;
    }

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'id' => ['required', 'numeric'],
            ...$this->paginationRules(),
        ];
    }
}
