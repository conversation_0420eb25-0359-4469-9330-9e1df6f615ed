<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class BetbyBonusCreateRequest
 *
 * @property int $template_id
 * @property int $amount
 * @property string $win_type
 * @property string $currency
 */
class BetbyBonusCreateRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        $this->merge([
            'template_id' => $this->route('id'),
        ]);

        return [
            'author_email' => ['nullable', 'string'],
            'template_id' => ['required', 'int', 'min:1'],
            'amount' => ['required', 'int', 'min:1'],
            'win_type' => ['required', 'string', 'in:real,bonus'],
            'currency' => ['nullable', 'string', 'between:3,3'],
        ];
    }
}
