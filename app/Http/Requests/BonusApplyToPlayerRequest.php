<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class BonusApplyToPlayerRequest
 *
 * @property string $player_uuid
 * @property int $bonus_id
 * @property null|int $amount
 */
class BonusApplyToPlayerRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'player_uuid' => ['required', 'uuid'],
            'bonus_id' => ['required', 'integer', 'exists:bonuses,id'],
            'amount' => ['nullable', 'integer'],
        ];
    }
}
