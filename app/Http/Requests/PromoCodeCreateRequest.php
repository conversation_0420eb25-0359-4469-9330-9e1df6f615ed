<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Rules\TimestampValidationCheck;
use Illuminate\Database\Query\Builder;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Unique;

/**
 * Class PromoCodeCreateRequest
 *
 * @property int $id
 * @property string $name
 * @property string $code
 * @property null|int $stream_id
 * @property int $bonus_id
 * @property null|string $description
 * @property null|bool $active
 * @property null|int $limit
 * @property null|int $start_at
 * @property null|int $end_at
 * @property string[] $condition
 * @property null|bool $is_alanbase
 */
class PromoCodeCreateRequest extends BaseRequest
{
    /**
     * @return array<string, array<TimestampValidationCheck|Unique|string>>
     */
    public function rules(): array
    {
        $id = $this->route('id');
        $this->merge([
            'id' => $id,
        ]);

        $rules = [
            'id' => ['nullable', 'integer', 'min:1', 'exists:promocodes,id'],
            'name' => ['required', 'string', 'max:255'],
            'code' => ['required', 'string', 'max:255'],
            'stream_id' => ['nullable', 'integer', 'min:1'],
            'bonus_id' => ['required', 'integer', 'min:1', 'exists:bonuses,id'],
            'description' => ['nullable', 'string', 'max:255'],
            'active' => ['nullable', 'boolean'],
            'limit' => ['nullable', 'integer', 'gte:0'],
            'start_at' => [
                'nullable',
                'integer',
                'date_format:U',
                new TimestampValidationCheck(),
            ],
            'end_at' => [
                'nullable',
                'integer',
                'date_format:U',
                new TimestampValidationCheck(),
            ],
            'condition' => ['required', 'array'],
            'condition.*' => ['string'],
            'is_alanbase' => ['nullable', 'boolean'],
        ];

        if ($this->method() === 'PUT') {
            $rules['code'][] = Rule::unique('promocodes', 'code')->where(
                fn(Builder $query) => $query->where('client_id', config('app.special_client_id'))
                    ->where('id', '!=', $id)
            );
        }

        if ($this->method() === 'POST') {
            $rules['code'][] = Rule::unique('promocodes', 'code')->where(
                static fn(Builder $query) => $query->where('client_id', config('app.special_client_id'))
            );
        }

        return $rules;
    }
}
