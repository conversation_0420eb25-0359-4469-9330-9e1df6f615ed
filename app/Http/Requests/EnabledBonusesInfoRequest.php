<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class EnabledBonusesInfoRequest
 *
 * @property null|string $currency
 * @property null|int $player_id
 * @property null $welcome_bonus
 */
class EnabledBonusesInfoRequest extends BaseRequest
{
    public function authorize(): bool
    {
        $playerId = $this->get('playerId');
        if ($playerId) {
            $this->merge([
                'player_id' => $playerId,
            ]);
        }

        return true;
    }

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'currency' => ['nullable', 'string', 'between:3,3'],
            'player_id' => ['nullable', 'numeric', 'min:1'],
            'welcome_bonus' => ['nullable'],
        ];
    }
}
