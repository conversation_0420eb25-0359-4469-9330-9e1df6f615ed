<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class GetFreeSpinBoundsRequest
 *
 * @property int[] $ids
 * @property null|int $player_id
 * @property null|int $slot_id
 * @property null|string $currency
 * @property null|bool $is_active
 */
class GetFreeSpinBoundsRequest extends BaseRequest
{
    public function authorize(): bool
    {
        if ($this->get('bounds')) {
            if (gettype($this->get('bounds')) === 'string') {
                $this->merge(['ids' => explode(',', $this->get('bounds'))]);
            }
        }

        return true;
    }

    /**
     * @return array<string, string>
     */
    public function rules(): array
    {
        return [
            'player_id' => 'nullable|numeric|min:1',
            'slot_id' => 'nullable|numeric|min:1',
            'currency' => 'nullable|string',
            'is_active' => 'nullable|boolean',
            'ids' => 'nullable|array',
            'ids.*' => 'nullable|numeric|exists:free_spin_bounds,id',
        ];
    }
}
