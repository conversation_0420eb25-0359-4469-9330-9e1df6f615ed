<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class SmarticoIssueFreeSpinRequest
 *
 * @property string $user_id
 * @property string $smartico_freespin_id
 * @property int $freespin_id
 * @property null|int $count
 */
class SmarticoIssueFreeSpinRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'user_id' => ['required', 'bail', 'uuid', 'string'],
            'smartico_freespin_id' => ['required', 'string'],
            'freespin_id' => ['required', 'integer'],
            'count' => ['nullable', 'integer', 'min:1'],
        ];
    }
}
