<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class BannerChangeStatusManyRequest
 *
 * @property int[] $banners
 * @property bool $enabled
 */
class BannerChangeStatusManyRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'banners' => ['required', 'array', 'min:1'],
            'banners.*' => ['required', 'integer', 'distinct', 'exists:banners,id'],
            'enabled' => ['required', 'boolean'],
        ];
    }
}
