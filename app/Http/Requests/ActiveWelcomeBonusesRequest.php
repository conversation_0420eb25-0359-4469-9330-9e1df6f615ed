<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Traits\PaginationRulesRequestTrait;

/**
 * Class ActiveWelcomeBonusesRequest
 *
 * @property null|string $currency
 * @property null $welcome_bonus
 */
class ActiveWelcomeBonusesRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'currency' => ['nullable', 'between:3,3'],
            'welcome_bonus' => ['nullable'],
            ...$this->paginationRules(),
        ];
    }
}
