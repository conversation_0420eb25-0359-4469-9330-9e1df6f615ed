<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Rules\CheckPromocodeCurrency;
use App\Rules\CheckPromocodeIsNotWelcome;
use App\Rules\PromoCodeValidation;
use App\Services\PromoCodeService;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * Class PromoCodeCheckRequest
 *
 * @property string $code
 * @property string $currency
 */
class PromoCodeCheckRequest extends BaseRequest
{
    protected $stopOnFirstFailure = true;

    /**
     * @return array<string, array<ValidationRule|string>>
     */
    public function rules(PromoCodeService $promoCodeService): array
    {
        $code = $this->string('code')->value();
        $promoCode = $promoCodeService->getPromoCode($code);

        return [
            'code' => [
                'bail',
                'required',
                'string',
                'max:255',
                'exists:promocodes,code',
                new CheckPromocodeIsNotWelcome($promoCode),
                new PromoCodeValidation($promoCode),
                new CheckPromocodeCurrency($promoCode, $this->currency),
            ],
            'currency' => [
                'bail',
                'nullable',
                'string',
                'regex:/^[A-Z]{3}$/',
            ],
        ];
    }
}
