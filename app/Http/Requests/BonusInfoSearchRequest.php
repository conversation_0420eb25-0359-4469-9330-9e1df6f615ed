<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Traits\PaginationRulesRequestTrait;

/**
 * Class BonusInfoSearchRequest
 *
 * @property null|string $currency
 * @property null|bool $active
 * @property null|bool $casino
 * @property null|string $name
 * @property null|string $button
 * @property null|int $id
 * @property null|int $bonus_id
 * @property null|string $type
 * @property null|int $visible_from
 * @property null|int $visible_to
 */
class BonusInfoSearchRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'currency' => ['nullable', 'string', 'between:3,3'],
            'active' => ['nullable', 'boolean'],
            'casino' => ['nullable', 'boolean'],
            'name' => ['nullable', 'string'],
            'button' => ['nullable', 'string'],
            'bonus_id' => ['nullable', 'numeric', 'exists:bonuses,id'],
            'id' => ['nullable', 'numeric', 'exists:bonus_info'],
            'type' => ['nullable', 'string'],
            'visible_from' => ['nullable', 'date_format:U'],
            'visible_to' => ['nullable', 'date_format:U'],

            ...$this->paginationRules(),
        ];
    }
}
