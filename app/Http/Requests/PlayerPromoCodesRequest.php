<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class PlayerPromoCodesRequest
 *
 * @property int $player_id
 * @property null|int $bonus_id
 * @property null|bool $is_alanbase
 * @property null|bool $is_active
 */
class PlayerPromoCodesRequest extends BaseRequest
{
    public function authorize(): bool
    {
        /** @var int|string $playerId */
        $playerId = $this->route('player_id');

        $this->merge(['player_id' => (int)$playerId]);

        return true;
    }

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'player_id' => ['required', 'integer', 'min:1'],
            'bonus_id' => ['nullable', 'integer', 'min:1', 'exists:bonuses,id'],
            'is_alanbase' => ['nullable', 'boolean'],
            'is_active' => ['nullable', 'boolean'],
        ];
    }
}
