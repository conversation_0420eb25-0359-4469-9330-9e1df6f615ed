<?php

namespace App\Http\Requests;

class DepositSelectedBonusRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'amount' => ['required', 'integer'],
            'deposits_count' => ['required', 'integer'],
            'player_id' => ['required', 'int', 'min:1'],
            'player_uuid' => ['required', 'string', 'uuid'],
            'player_currency' => ['required', 'string'],
        ];
    }
}
