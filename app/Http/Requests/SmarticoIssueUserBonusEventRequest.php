<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class SmarticoIssueUserBonusEventRequest
 *
 * @property int $bonus_id
 * @property string $user_id
 * @property int $smartico_bonus_id
 * @property null|int $amount
 */
class SmarticoIssueUserBonusEventRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'bonus_id' => ['required', 'bail'],
            'user_id' => ['required', 'bail', 'uuid', 'string'],
            'smartico_bonus_id' => ['required', 'integer'],
            'amount' => ['nullable', 'integer', 'gt:0'],
        ];
    }
}
