<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class BonusApplyRequest
 *
 * @property string $player_uuid
 * @property int $bonus_id
 * @property null|int $amount
 */
class BonusApplyRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        $this->merge([
            'player_uuid' => $this->attributes->get('player_uuid'),
        ]);

        return [
            'player_uuid' => ['required', 'uuid'],
            'bonus_id' => ['required', 'integer', 'exists:bonuses,id'],
            'amount' => ['nullable', 'integer'],
        ];
    }
}
