<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Traits\PaginationRulesRequestTrait;

/**
 * Class EnabledBonusesInfoForAdminRequest
 *
 * @property null|int $id
 * @property null|string $currency
 * @property null|bool $casino
 * @property null|int $bonus_id
 * @property null|string $visible_from
 * @property null|string $visible_to
 * @property null|int $player_id
 */
class EnabledBonusesInfoForAdminRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        if ($this->route('player_id')) {
            /** @var int|string $playerId */
            $playerId = $this->route('player_id');

            $this->merge(['player_id' => (int)$playerId]);
        }

        return [
            'id' => ['nullable', 'numeric', 'exists:bonus_info,id'],
            'currency' => ['nullable', 'string', 'between:3,3'],
            'casino' => ['nullable', 'boolean'],
            'bonus_id' => ['nullable', 'numeric', 'exists:bonuses,id'],
            'visible_from' => ['nullable', 'date_format:Y-m-d H:i:s'],
            'visible_to' => ['nullable', 'date_format:Y-m-d H:i:s'],
            'player_id' => ['nullable', 'numeric', 'min:1'],

            ...$this->paginationRules(),
        ];
    }
}
