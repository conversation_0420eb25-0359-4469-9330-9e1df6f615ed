<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class FreeSpinCancelBoundRequest
 *
 * @property int $id
 */
class FreeSpinCancelBoundRequest extends BaseRequest
{
    public function authorize(): bool
    {
        $this->merge([
            'id' => (int)$this->route('id'),
        ]);

        return true;
    }

    /**
     * @return array<string, string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|numeric|exists:free_spin_bounds',
        ];
    }
}
