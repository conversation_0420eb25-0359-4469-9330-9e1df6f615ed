<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Rules\CheckPlayerPromocodeUnavailability;
use App\Rules\CheckPromocodeIsWelcome;
use App\Rules\MatchPromoCodeBonusAndPlayerCurrency;
use App\Rules\PromoCodeValidation;
use App\Services\PlayerService;
use App\Services\PromoCodeService;
use Exception;

/**
 * Class PromoCodeApplyToPlayerRequest
 *
 * @property string $player_uuid
 * @property string $code
 */
class PromoCodeApplyToPlayerRequest extends BaseRequest
{
    /**
     * @return array<string, array<PromoCodeValidation|CheckPromocodeIsWelcome|MatchPromoCodeBonusAndPlayerCurrency|CheckPlayerPromocodeUnavailability|string>>
     * @throws Exception
     */
    public function rules(PlayerService $playerService, PromoCodeService $promoCodeService): array
    {
        $playerUUID = $this->player_uuid;

        if (!$playerUUID) {
            throw new Exception('Player UUID is required.', 422);
        }

        $player = $playerService->getPlayerByUUID($playerUUID);
        $code = $this->string('code')->value();
        $promoCode = $promoCodeService->getPromoCode($code);
        $promoCodeIds = $promoCodeService->getPromoCodeIdsByPlayerId($player->id);

        return [
            'player_uuid' => ['bail', 'required', 'string', 'uuid'],
            'code' => [
                'bail',
                'required',
                'string',
                'max:255',
                'exists:promocodes,code',
                new PromoCodeValidation($promoCode),
                new CheckPromocodeIsWelcome($promoCode),
                new MatchPromoCodeBonusAndPlayerCurrency($player, $promoCode),
                new CheckPlayerPromocodeUnavailability($promoCode, $promoCodeIds),
            ],
        ];
    }
}
