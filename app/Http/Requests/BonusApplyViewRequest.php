<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Enums\BonusTypeEnum;
use App\Traits\PaginationRulesRequestTrait;
use Illuminate\Validation\Rule;

/**
 * Class BonusApplyViewRequest
 *
 * @property int $bonus_id
 * @property string $bonus_type
 * @property null|string $status
 * @property null|string $start_at
 * @property null|string $end_at
 */
class BonusApplyViewRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        $this->merge([
            'bonus_id' => $this->route('bonusId'),
            'bonus_type' => $this->route('bonusType') ?? $this->input('bonus_type'),
        ]);

        return [
            'bonus_id' => ['required', 'integer'],
            'bonus_type' => [
                'required',
                'string',
                Rule::in([
                    BonusTypeEnum::ONETIME->value,
                    BonusTypeEnum::FREEBET->value,
                ]),
            ],
            'status' => ['nullable', 'string'],
            'start_at' => ['nullable', 'date'],
            'end_at' => ['nullable', 'date'],
            ...$this->paginationRules(),
        ];
    }
}
