<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Class ApplyBonusSmarticoOnetimeRequest
 *
 * @property int $bonus_id
 * @property int $player_id
 * @property int $amount
 */
class ApplyBonusSmarticoOnetimeRequest extends BaseRequest
{
    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'bonus_id' => [
                'required',
                'integer',
                'min:1',
                'exists:bonuses,id',
            ],
            'player_id' => ['required', 'integer', 'min:1'],
            'amount' => ['integer', 'gt:0'],
        ];
    }
}
