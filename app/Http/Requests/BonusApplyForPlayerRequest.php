<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Enums\BonusTypeEnum;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\Rules\In;

/**
 * Class BonusApplyForPlayerRequest
 *
 * @property int $bonus_id
 * @property string $bonus_type
 * @property int $player_id
 * @property null|int $amount
 */
class BonusApplyForPlayerRequest extends BaseRequest
{
    /**
     * @return array<string, array<Exists|In|string>>
     */
    public function rules(): array
    {
        /**
         * @var string $bonusType
         */
        $bonusType = $this->route('bonusType') ?? $this->input('bonus_type');
        $this->merge([
            'bonus_type' => $bonusType,
        ]);

        $rules = [
            'bonus_id' => [
                'required',
                'integer',
                'min:1',
                Rule::exists('bonuses', 'id')->where('type', mb_strtolower($bonusType)),
            ],
            'bonus_type' => [
                'required',
                'string',
                Rule::in([
                    BonusTypeEnum::ONETIME->value,
                    BonusTypeEnum::FREE_BET->value,
                    BonusTypeEnum::FREE_MONEY->value,
                ]),
            ],
            'player_id' => ['required', 'integer', 'min:1'],
        ];

        if ($bonusType === BonusTypeEnum::ONETIME->value) {
            $rules['amount'] = ['required', 'integer', 'min:0'];
        }

        return $rules;
    }
}
