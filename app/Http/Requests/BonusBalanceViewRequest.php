<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Enums\BonusTypeEnum;
use App\Traits\PaginationRulesRequestTrait;
use Illuminate\Validation\Rule;

/**
 * Class BonusBalanceViewRequest
 *
 * @property null|int $client_id
 * @property int $player_id
 * @property int $bonus_id
 * @property bool $is_not_waiting
 * @property bool $external
 * @property int $bonus_external_id
 * @property int $external_id
 * @property bool $active
 * @property bool $casino
 * @property bool $bets
 * @property string $type
 */
class BonusBalanceViewRequest extends BaseRequest
{
    use PaginationRulesRequestTrait;

    /**
     * @return array<string, string[]>
     */
    public function rules(): array
    {
        return [
            'client_id' => ['nullable', 'integer', 'min:1'],
            'player_id' => ['integer', 'min:1'],
            'bonus_id' => ['integer', 'min:1', 'exists:bonuses,id'],
            'is_not_waiting' => ['bool'],
            'external' => ['bool'],
            'bonus_external_id' => ['required', 'integer', 'min:1'],
            'external_id' => ['integer', 'min:1'],
            'active' => ['bool'],
            'casino' => ['bool'],
            'bets' => ['bool'],
            'type' => Rule::in([
                BonusTypeEnum::WAGER->value,
                BonusTypeEnum::FREE_MONEY->value,
                BonusTypeEnum::FREE_BET->value,
                BonusTypeEnum::FREEBET->value,
                BonusTypeEnum::FREESPIN_BONUS->value,
                BonusTypeEnum::FREE_SPINS_FOR_DEPOSIT->value,
            ]),
            ...$this->paginationRules(),
        ];
    }
}
