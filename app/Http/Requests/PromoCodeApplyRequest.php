<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Rules\CheckPlayerPromocodeUnavailability;
use App\Rules\CheckPromocodeIsWelcome;
use App\Rules\MatchPromoCodeBonusAndPlayerCurrency;
use App\Rules\PromoCodeValidation;
use App\Services\PlayerService;
use App\Services\PromoCodeService;

/**
 * Class PromoCodeApplyRequest
 *
 * @property string $player_uuid
 * @property string $code
 */
class PromoCodeApplyRequest extends BaseRequest
{
    /**
     * @return array<string, array<PromoCodeValidation|CheckPromocodeIsWelcome|MatchPromoCodeBonusAndPlayerCurrency|CheckPlayerPromocodeUnavailability|string>>
     */
    public function rules(PlayerService $playerService, PromoCodeService $promoCodeService): array
    {
        $playerUUID = $this->attributes->get('player_uuid');

        $player = $playerService->getPlayerByUUID($playerUUID);
        $code = $this->string('code')->value();
        $promoCode = $promoCodeService->getPromoCode($code);
        $promoCodeIds = $promoCodeService->getPromoCodeIdsByPlayerId($player->id);

        $this->merge(['player_uuid' => $playerUUID]);

        return [
            'player_uuid' => ['bail', 'required', 'string', 'uuid'],
            'code' => [
                'bail',
                'required',
                'string',
                'max:255',
                'exists:promocodes,code',
                new PromoCodeValidation($promoCode),
                new CheckPromocodeIsWelcome($promoCode),
                new MatchPromoCodeBonusAndPlayerCurrency($player, $promoCode),
                new CheckPlayerPromocodeUnavailability($promoCode, $promoCodeIds),
            ],
        ];
    }
}
