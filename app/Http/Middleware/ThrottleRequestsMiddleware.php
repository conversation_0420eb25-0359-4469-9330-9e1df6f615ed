<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Services\RealIpService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Routing\Middleware\ThrottleRequests;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ThrottleRequestsMiddleware extends ThrottleRequests
{
    public function handle($request, Closure $next, $maxAttempts = 60, $decayMinutes = 1, $prefix = ''): Response
    {
        $this->determineIp($request);

        return parent::handle(
            $request,
            $next,
            $maxAttempts,
            $decayMinutes,
            $prefix
        );
    }

    /**
     * @param Request $request
     * @return void
     */
    private function determineIp(Request $request): void
    {
        /** @var RealIpService $realIpService */
        $realIpService = app(RealIpService::class);

        $clientIp = $realIpService->getIpAddress();

        if (!App::environment('production')) {
            Log::info('Client ip: ' . $clientIp);
        }

        $request->server->set('REMOTE_ADDR', $clientIp);
        $request->attributes->set('client_ip', $clientIp);
    }
}
