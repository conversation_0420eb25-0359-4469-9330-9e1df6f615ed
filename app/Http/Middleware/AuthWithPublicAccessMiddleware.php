<?php

namespace App\Http\Middleware;

use App\Exceptions\Exception;
use App\Services\ClusterService;
use App\Services\PlayerService;
use Closure;
use Illuminate\Http\Request;

class AuthWithPublicAccessMiddleware
{
    public function __construct(
        private readonly PlayerService $playerService,
        private readonly ClusterService $clusterService
    ) {
    }

    /**
     * Handle an incoming request.
     *
     * @throws Exception
     */
    public function handle(Request $request, Closure $next, string ...$withExtraFields): mixed
    {
        if (empty($request->header('authorization'))) {
            return $next($request);
        }

        $bearerToken = $request->bearerToken();

        if ($bearerToken) {
            $playerData = $this->playerService->getPlayerDataByToken($bearerToken, $withExtraFields);
            $this->clusterService->setCurrentConnection($playerData->cc ?? null);

            if ($playerData) {
                $request->attributes->add([
                    'playerId' => $playerData->id,
                    'playerUUID' => $playerData->uuid,
                    'player_uuid' => $playerData->uuid,
                    ...$playerData->extraData
                ]);

                return $next($request);
            }
        }

        throw new Exception('Unauthorized, please pass a valid core bearer token');
    }
}
