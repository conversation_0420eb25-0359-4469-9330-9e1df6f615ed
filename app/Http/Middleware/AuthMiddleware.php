<?php

namespace App\Http\Middleware;

use App\Exceptions\Exception;
use App\Services\ClusterService;
use App\Services\PlayerService;
use Closure;
use Illuminate\Http\Request;
use JsonException;

class AuthMiddleware
{
    public function __construct(
        private readonly PlayerService $playerService,
        private readonly ClusterService $clusterService
    ) {
    }

    /**
     * Handle an incoming request.
     *
     * @throws Exception
     * @throws JsonException
     */
    public function handle(Request $request, Closure $next, string ...$withExtraFields): mixed
    {
        if (empty($request->header('authorization'))) {
            throw new Exception('Unauthorized, please pass a valid core bearer token', 401);
        }

        $bearerToken = $request->bearerToken();

        if ($bearerToken) {
            $playerData = $this->playerService->getPlayerDataByToken($bearerToken, $withExtraFields);
            $this->clusterService->setCurrentConnection($playerData->cc ?? null);

            if ($playerData) {
                $request->attributes->add([
                    'playerId' => $playerData->id,
                    'player_uuid' => $playerData->uuid,
                    ...$playerData->extraData
                ]);

                return $next($request);
            }
        }

        throw new Exception('Unauthorized, please pass a valid core bearer token', 401);
    }
}
