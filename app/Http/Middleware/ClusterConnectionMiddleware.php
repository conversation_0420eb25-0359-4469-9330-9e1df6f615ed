<?php

namespace App\Http\Middleware;

use App\Services\ClusterService;
use Closure;
use Illuminate\Http\Request;

class ClusterConnectionMiddleware
{
    public function __construct(
        private readonly ClusterService $clusterService
    ) {
    }

    public function handle(Request $request, Closure $next): mixed
    {
        if (!empty($request->header('x-cluster-connection'))) {
            $connection = $request->header('x-cluster-connection');
        }

        if (!empty($request->input('brand_id'))) {
            $connection = $request->input('brand_id');
        }

        $this->clusterService->setCurrentConnection($connection ?? null);

        return $next($request);
    }
}
