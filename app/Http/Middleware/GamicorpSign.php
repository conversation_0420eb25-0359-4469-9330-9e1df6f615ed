<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use JsonException;
use Symfony\Component\HttpFoundation\Response;

class GamicorpSign
{
    /**
     * @throws JsonException
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (app()->environment(['local', 'dev', 'staging'])) {
            return $next($request);
        }

        $data = $request->post();
        $dataJson = json_encode($data, JSON_THROW_ON_ERROR);
        $sign = sha1(base64_encode($dataJson).config('auth.gamicorp_merchant_key'));

        if ($sign !== $request->header('X-SIGNATURE')) {
            return response()->json(['error' => 'Invalid signature'], 401);
        }

        return $next($request);
    }
}
