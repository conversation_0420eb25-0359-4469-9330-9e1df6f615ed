<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Exceptions\SmarticoException;
use Closure;
use Illuminate\Http\Request;

class SmarticoAuthMiddleware
{
    public function handle(Request $request, Closure $next): mixed
    {
        $requestToken = $request->header('signature');

        if (empty($requestToken)) {
            throw new SmarticoException('Unauthorized, please pass a valid token', 401);
        }

        $entityId = $request->get('bonus_id') ?? $request->get('freespin_id');

        $validatedToken = $this->tokenValid(
            $requestToken,
            $request->get('user_id'),
            $entityId
        );

        if ($validatedToken) {
            return $next($request);
        }

        throw new SmarticoException('Unauthorized, please pass a valid token', 401);
    }

    private function tokenValid(string $requestToken, string $userId, string $entityId): bool
    {
        $token = md5($userId . ':' . $entityId . ':' . config('auth.smartico_private_key'));

        return $requestToken === $token;
    }
}
