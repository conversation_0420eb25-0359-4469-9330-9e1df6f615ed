<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Traits\ExcludeHeadersListTrait;
use Closure;
use Illuminate\Http\Request;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;

class LogRequestMiddleware
{
    use ExcludeHeadersListTrait;

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     *
     * @return mixed
     */
    private LoggerInterface $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function handle(Request $request, Closure $next): mixed
    {
        $response = $next($request);

        $this->logger->debug(
            $this->makeLogMessage($request, $response),
            $this->makeLogContext($request, $response)
        );

        return $response;
    }

    protected function makeLogMessage(Request $request, Response $response): string
    {
        return "Request | Response [{$response->getStatusCode()}] | {$request->getMethod()}: {$request->getPathInfo()}";
    }

    /**
     * @return array<string, string|array<string, string|int|string[]|mixed>>
     */
    protected function makeLogContext(Request $request, Response $response): array
    {
        $responseContent = $response->getContent();

        if ($request->getMethod() === 'GET') {
            $responseContent = mb_substr($responseContent, 0, 500) . '....';
        }

        if ($request->headers->get('authorization')) {
            $request->headers->set('Bearer', 'Logged in user');
        }

        return [
            'url' => $request->getPathInfo(),
            'method' => $request->getMethod(),
            'body' => collect($request->request->all())->except(['password', 'repeat_password', 'client_secret', 'old_password'])->all(),
            'response' => $this->maskPrivateData($responseContent, ['access_token', 'refresh_token', 'redirect_url', 'betby_jwt']),
            'headers' => collect($request->headers->all())->except($this->getExcludeHeadersList())->all(),
            'query' => $request->query->all(),
        ];
    }

    /**
     * @param string[] $keys
     */
    protected function maskPrivateData(string $responseContent, array $keys): string
    {
        foreach ($keys as $key) {
            $responseContent = preg_replace("/\"$key\":\"([^\"\\\\]|\\\\.)+\"/", "\"$key\":\"*****\"", $responseContent, 1);
        }

        return $responseContent;
    }
}
