<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Spatie\ResponseCache\Exceptions\CouldNotUnserialize;
use Spatie\ResponseCache\Middlewares\CacheResponse;
use Spatie\ResponseCache\Replacers\Replacer;
use Symfony\Component\HttpFoundation\Response;
use Closure;

class CacheResponseMiddleware extends CacheResponse
{
    public function handle(Request $request, Closure $next, mixed ...$args): Response
    {
        $isRefreshCache = $request->header('REFRESH-CACHE') === config('cache.refresh_cache_secret');

        $lifetimeInSeconds = $this->getLifetime($args);
        $tags = $this->getTags($args);

        if ($this->responseCache->enabled($request)) {
            if (!$isRefreshCache && $this->responseCache->hasBeenCached($request, $tags)) {
                try {
                    $response = $this->responseCache->getCachedResponseFor($request, $tags);
                    $this->getReplacers()->each(static function (Replacer $replacer) use ($response): void {
                        $replacer->replaceInCachedResponse($response);
                    });

                    return $response;
                } catch (CouldNotUnserialize $e) {
                    Log::info($e->getMessage() . ' | CacheResponseMiddleware error', [
                        'request' => $request->all(),
                        'path' => $request->path(),
                        'tags' => $tags,
                    ]);
                }
            }
        }

        $response = $next($request);

        if (
            $this->responseCache->enabled($request)
            && $this->responseCache->shouldCache($request, $response)
        ) {
            $this->makeReplacementsAndCacheResponse($request, $response, $lifetimeInSeconds, $tags);
        }

        return $response;
    }
}
