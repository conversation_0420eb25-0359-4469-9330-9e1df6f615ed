<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

/**
 * Class TraceMiddleware
 *
 * This middleware class is responsible for adding a trace ID to the request and response headers.
 * It generates a unique trace ID if not provided in the request headers and sets it in the request and response.
 */
class TraceMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        $traceIdHeader = Config::get('app.trace_id_header', 'X-Trace-Id');
        $traceId = $request->header($traceIdHeader, Str::uuid()->toString());

        Log::withContext(['trace_id' => $traceId]);
        $request->headers->set($traceIdHeader, $traceId);

        /** @var Response $response */
        $response = $next($request);
        $response->headers->set($traceIdHeader, $traceId);

        return $response;
    }
}
