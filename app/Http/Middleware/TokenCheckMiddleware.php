<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Exceptions\Exception;
use App\Exceptions\SmarticoException;
use Closure;
use Illuminate\Http\Request;

class TokenCheckMiddleware
{
    /**
     * @throws Exception
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $requestToken = $request->input('token');

        if (empty($requestToken)) {
            throw new SmarticoException('Unauthorized, please pass a valid token', 401);
        }

        if ($this->tokenValid($requestToken)) {
            return $next($request);
        }

        throw new SmarticoException('Unauthorized, please pass a valid token', 401);
    }

    private function tokenValid(string $requestToken): bool
    {
        return $requestToken === config('auth.bonus_info_endpoint_token');
    }
}
