<?php

declare(strict_types=1);

namespace App\Traits;

trait PaginationRulesRequestTrait
{
    /**
     * @return string[]
     */
    protected function paginationRules(): array
    {
        return [
            'pagination' => 'array',
            'pagination.limit' => 'integer|min:1',
            'pagination.page' => 'integer|min:1',
            'asc' => 'nullable|string',
            'desc' => 'nullable|string',
        ];
    }
}
