<?php

namespace App\Traits;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use SplFileObject;

trait FileHelper
{
    public function getSeparator(UploadedFile $uploadedFile): ?string
    {
        foreach ([';', ','] as $separator) {
            $result = static::getHeader($separator, $uploadedFile);
            if (count($result) > 1) {
                $successSeparator = $separator;
                break;
            }
        }

        return $successSeparator ?? null;
    }

    /**
     * @param string $separator
     * @param UploadedFile $uploadedFile
     * @return array<string>|false
     */
    public function getHeader(string $separator, UploadedFile $uploadedFile): array|false
    {
        $file = new SplFileObject($uploadedFile->getRealPath());
        $file->setCsvControl();
        $file->setFlags(SplFileObject::READ_CSV);

        return $file->fgetcsv($separator);
    }

    public function getPathName(UploadedFile $uploadedFile, string $extension = 'csv'): string
    {
        $fileName = md5((string)time()) . '.' . $extension;
        $pathName = $uploadedFile->storeAs('public', $fileName);

        return Storage::path($pathName);
    }
}
