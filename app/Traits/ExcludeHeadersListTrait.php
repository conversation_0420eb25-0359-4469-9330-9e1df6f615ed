<?php

declare(strict_types=1);

namespace App\Traits;

trait ExcludeHeadersListTrait
{
    /**
     * @return string[]
     */
    protected function getExcludeHeadersList(): array
    {
        return [
            'access-token',
            'authorization',
            'Authorization',
            'content-length',
            'content-type',
            'accept',
            'accept-encoding',
            'accept-language',
            'accept-geo',
            'origin',
            'verify',
            'user-agent',
            'cf-visitor',
            'cf-ray',
            'cf-connecting-ip',
            'cf-ipcountry',
            'cdn-loop',
            'sec-fetch-site',
            'sec-fetch-mode',
            'sec-fetch-dest',
            'sec-ch-ua-platform',
            'sec-ch-ua-mobile',
            'sec-ch-ua',
            'x-forwarded-server',
            'x-forwarded-proto',
            'x-forwarded-port',
            'x-forwarded-host',
            'x-forwarded-for',
            'x-signature',
            'X-Signature',
            'signature',
        ];
    }
}
