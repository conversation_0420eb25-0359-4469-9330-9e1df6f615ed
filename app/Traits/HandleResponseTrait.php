<?php

declare(strict_types=1);

namespace App\Traits;

use App\Dto\ResponseOutputDto;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\Client\Response;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

trait HandleResponseTrait
{
    /**
     * @param null|Response $response
     * @return ResponseOutputDto
     */
    public function handleSuccessResponse(?Response $response): ResponseOutputDto
    {
        $dto = new ResponseOutputDto();
        $dto->responseCode = $response?->status() ?? 0;
        $dto->rawContent = $response?->body();
        $dto->content = $dto->rawContent ? json_decode($dto->rawContent, true) : null;

        return $dto;
    }

    /**
     * @param null|RequestException $exception
     * @return ResponseOutputDto
     */
    public function handleErrorResponse(?RequestException $exception): ResponseOutputDto
    {
        $dto = new ResponseOutputDto();
        $dto->isSuccess = false;
        $dto->responseCode = $exception?->response->status() ?? SymfonyResponse::HTTP_INTERNAL_SERVER_ERROR;
        $dto->errorCode = $exception?->response->body()
            ? json_decode($exception->response->body(), true)['error_code'] ?? null : null;

        return $dto;
    }
}
