<?php

declare(strict_types=1);

namespace App\Traits;

trait MoneyWithCurrencyTrait
{
    /**
     * @return null|string[]
     */
    public function geMoneyWithCurrency(null|float|int|string $amount, ?string $currency): ?array
    {
        if (!$amount || !$currency) {
            return null;
        }

        return [
            'amount' => (string)$amount,
            'currency' => $currency,
        ];
    }
}
