<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\BonusStatusEnum;
use App\Exceptions\SmarticoException;
use App\Jobs\FreeBetStartApplyJob;
use App\Models\Bonus;
use App\Models\MassFreeBetBonusInfo;
use App\Repositories\BonusApplyFreebetRepository;
use App\Services\Dto\BonusApplyForPlayerDTO;
use App\Services\Dto\BonusApplySearchDTO;
use App\Services\Dto\MassBonusApplyDTO;
use App\Services\Helpers\CsvFileReader;
use App\Traits\FileHelper;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Throwable;

class FreeBetBonusService
{
    use FileHelper;

    public function __construct(
        private readonly PlayerService $playerService,
        private readonly BonusApplyFreebetRepository $bonusApplyFreebetRepository,
        private readonly BonusService $bonusService,
        private readonly BonusApplyService $bonusApplyService,
    ) {
    }

    /**
     * @return array<string, bool|string>
     */
    public function applyBonusForPlayer(BonusApplyForPlayerDTO $dto): array
    {
        /** @var Bonus $bonus */
        $bonus = $this->bonusService->getBonusById($dto->bonusId);
        $amount = $dto->amount ?? (count($bonus->bonuses) > 0 ? $bonus->bonuses[0] / 100 : 0);

        $bonusInfo = $this->createBonusInfo([
            'bonusId' => $dto->bonusId,
            'playerId' => $dto->playerId,
            'amount' => $amount,
            'currency' => $bonus->currency,
            'smarticoBonusId' => $dto->smarticoBonusId,
        ]);

        return $this->apply($bonusInfo->id);
    }

    /**
     * @return array<string, bool|string>
     */
    public function apply(int $bonusInfoId): array
    {
        $freeBetBonusInfo = $this->bonusApplyFreebetRepository->getFreeBetBonusDetail($bonusInfoId);

        if ($freeBetBonusInfo->amount) {
            $player = $freeBetBonusInfo->player_id
                ? $this->playerService->getPlayerByID($freeBetBonusInfo->player_id)
                : $this->playerService->getPlayerByUUID($freeBetBonusInfo->external_player_id);

            $freeBetBonusInfo->player_id = $player->id;
            $freeBetBonusInfo->external_player_id = $player->uuid;
            $freeBetBonusInfo->save();

            try {
                $bonus = $this->bonusService->getBonusById($freeBetBonusInfo->bonus_id);

                $this->bonusApplyService->applyOnetimeForPlayer($bonus, $player, $freeBetBonusInfo->amount);
            } catch (Throwable $e) {
                Log::error('Bonus not possible to issue', [
                    'error' => $e->getMessage(),
                    'bonus_id' => $freeBetBonusInfo->id,
                    'player_id' => $player->id,
                    'amount' => $freeBetBonusInfo->amount,
                    'bonus_smartico_id' => $freeBetBonusInfo->smartico_bonus_id,
                ]);

                if ($e instanceof ModelNotFoundException) {
                    throw new SmarticoException('Bonus Not Found', 400);
                }

                throw new SmarticoException('Bonus not possible to issue', 400);
            }

            $message = 'Bonus accepted for player';
            $freeBetBonusInfo = $this->saveBonusInfoStatus($freeBetBonusInfo, $message, BonusStatusEnum::SUCCESS_STATUS);
        } else {
            $message = 'There was no money';
            $freeBetBonusInfo = $this->saveBonusInfoStatus($freeBetBonusInfo, $message, BonusStatusEnum::ERROR_STATUS);
        }

        return [
            'status' => $freeBetBonusInfo->status === BonusStatusEnum::SUCCESS_STATUS->value,
            'message' => $freeBetBonusInfo->text,
        ];
    }

    /**
     * @param array<string, int|string> $data
     */
    public function createBonusInfo(array $data): MassFreeBetBonusInfo
    {
        if (isset($data['smarticoBonusId']) && $data['smarticoBonusId']) {
            $isBonusAlreadyTransferredToPlayer = $this->bonusApplyFreebetRepository->isBonusAlreadyTransferredToPlayer(
                $data['smarticoBonusId']
            );

            if ($isBonusAlreadyTransferredToPlayer) {
                throw new SmarticoException('Bonus Already Transferred to player', 422);
            }
        }

        return $this->saveMassFreebetBonusInfo($data);
    }

    public function saveBonusInfoStatus(MassFreeBetBonusInfo $bonusInfo, string $message, BonusStatusEnum $status): MassFreeBetBonusInfo
    {
        $bonusInfo->text = $message;
        $bonusInfo->status = $status->value;
        $bonusInfo->save();

        if (empty($message)) {
            Log::error('Free Money bonus not applied');
        } else {
            Log::error($message);
        }

        return $bonusInfo;
    }

    public function list(BonusApplySearchDTO $dto): LengthAwarePaginator
    {
        return $this->bonusApplyFreebetRepository->list($dto);
    }

    public function startApplyJob(MassBonusApplyDTO $dto): void
    {
        $key = 'mass_bonus_apply_freebet_' . md5((string)time());
        $fileDataList = CsvFileReader::readFile($dto->file->getPathname(), $this->getSeparator($dto->file));

        Cache::set($key, $fileDataList, 3600);

        dispatch(new FreeBetStartApplyJob($dto->bonusId, $key));
    }

    /**
     * @param array<string, int|string> $data
     */
    private function saveMassFreebetBonusInfo(array $data): MassFreeBetBonusInfo
    {
        $freeBetBonusInfo = new MassFreeBetBonusInfo();
        $freeBetBonusInfo->bonus_id = $data['bonusId'];
        $freeBetBonusInfo->template_id = !empty($data['templateId']) ? $data['templateId'] : null;
        $freeBetBonusInfo->external_player_id = !empty($data['externalPlayerId']) ? $data['externalPlayerId'] : null;
        $freeBetBonusInfo->player_id = !empty($data['playerId']) ? $data['playerId'] : null;
        $freeBetBonusInfo->smartico_bonus_id = !empty($data['smarticoBonusId']) ? $data['smarticoBonusId'] : null;
        $freeBetBonusInfo->currency = $data['currency'];
        $freeBetBonusInfo->amount = !empty($data['amount']) ? $data['amount'] : null;
        $freeBetBonusInfo->save();

        return $freeBetBonusInfo;
    }
}
