<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\BonusStatusEnum;
use App\Exceptions\SmarticoException;
use App\Models\Bonus;
use App\Models\BonusTriggerSession;
use App\Services\Dto\PlayerDto;
use Carbon\Carbon;

class DepositBonusService
{
    public function __construct(
        private readonly BonusHistoryService $bonusHistoryService,
        private readonly BonusService $bonusService,
        private readonly BonusPlayerService $bonusPlayerService,
        private readonly BonusTriggerSessionService $bonusTriggerSessionService
    ) {
    }

    /**
     * @return array<string, bool|string>
     */
    public function applyBonusForPlayer(Bonus $bonus, PlayerDto $player, int $smarticoBonusId): array
    {
        if ($this->bonusHistoryService->existsSuccessfulTransfer($smarticoBonusId)) {
            throw new SmarticoException('Bonus Already Transferred to player', 422);
        }

        $bonusInfo = $this->bonusHistoryService->createBonusInfo($bonus->id, $player, $smarticoBonusId);

        $bonusInfoCard = $this->bonusService->getSmarticoBonusInfoByBonusId($bonus->id);

        if (empty($bonusInfoCard)) {
            $message = 'Bonus Card is not Found in the system';
            $this->bonusHistoryService->saveBonusInfoStatus($bonusInfo, $message, BonusStatusEnum::ERROR_STATUS);

            throw new SmarticoException($message, 400);
        }

        /** @var BonusTriggerSession $triggerSession */
        $triggerSession = $this->bonusTriggerSessionService->getBonusTriggerSessionByBonusId($bonusInfo['bonus_id']);

        if (Carbon::parse($triggerSession->end_at) < Carbon::now()) {
            $message = 'Bonus Card is Expired in the system';
            $this->bonusHistoryService->saveBonusInfoStatus($bonusInfo, $message, BonusStatusEnum::ERROR_STATUS);

            throw new SmarticoException($message, 400);
        }

        $this->bonusPlayerService->saveBonus($bonusInfo['bonus_id'], $bonusInfo['player_id'], $bonusInfoCard->id);

        $message = 'Bonus accepted for player';
        $this->bonusHistoryService->saveBonusInfoStatus($bonusInfo, $message, BonusStatusEnum::SUCCESS_STATUS);

        return [
            'status' => true,
            'message' => 'Bonus accepted for player',
        ];
    }
}
