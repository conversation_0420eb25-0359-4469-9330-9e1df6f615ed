<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\BonusTypeEnum;
use App\Enums\FreeSpinBoundStatusEnum;
use App\Enums\SupplierEnum;
use App\Exceptions\SmarticoException;
use App\Models\FreeSpin;
use App\Repositories\SmarticoFreeSpinRepository;
use App\Services\Dto\BonusApplyForPlayerDTO;
use App\Services\Dto\SmarticoIssueBonusToPlayerDTO;
use App\Services\Dto\SmarticoIssueFreeSpinToPlayerDTO;
use Carbon\Carbon;
use Exception;
use RuntimeException;

class SmarticoService
{
    public function __construct(
        private readonly FreeBetBonusService $freeBetBonusService,
        private readonly OneTimeBonusService $oneTimeBonusService,
        private readonly DepositBonusService $depositBonusService,
        private readonly PlayerService $playerService,
        private readonly BonusService $bonusService,
        private readonly FreeSpinService $freeSpinService,
        private readonly GamicorpFreeSpinBoundService $gamicorpFreeSpinBoundService,
        private readonly FreeSpinBoundService $freeSpinBoundService,
        private readonly SmarticoFreeSpinRepository $smarticoFreeSpinRepository,
    ) {
    }

    /**
     * @throws Exception
     * @return array<string, bool|string>
     */
    public function issueBonus(SmarticoIssueBonusToPlayerDTO $dto): array
    {
        $bonus = $this->bonusService->getBonusForSmarticoById($dto->bonusId);
        $player = $this->playerService->getFirstPlayerByUUID($dto->userId);

        if (null === $bonus) {
            throw new SmarticoException('Bonus Not Found', 400);
        }

        if (empty($player)) {
            throw new SmarticoException('Player Not Found', 400);
        }

        if ($player->blocked) {
            throw new SmarticoException('Player is blocked', 400);
        }

        if ($player->currency !== $bonus->currency) {
            throw new SmarticoException('Player and Bonus Currencies doesnt match', 400);
        }

        $bonusApplyForPlayerDTO = new BonusApplyForPlayerDTO([
            'bonus_id' => $dto->bonusId,
            'player_id' => $player->id,
            'amount' => $dto->amount,
            'smartico_bonus_id' => $dto->smarticoBonusId,
        ]);

        return match ($bonus->type) {
            BonusTypeEnum::ONETIME->value
                => $this->oneTimeBonusService->applyBonusForPlayer($bonusApplyForPlayerDTO),
            BonusTypeEnum::FREE_MONEY->value, BonusTypeEnum::FREE_BET->value
                => $this->freeBetBonusService->applyBonusForPlayer($bonusApplyForPlayerDTO),
            BonusTypeEnum::DEPOSIT->value
                => $this->depositBonusService->applyBonusForPlayer($bonus, $player, $dto->smarticoBonusId),

            default => throw new SmarticoException('Bonus type is not allowed', 400),
        };
    }

    /**
     * @return array<string, bool|string>
     */
    public function issueFreeSpin(SmarticoIssueFreeSpinToPlayerDTO $dto): array
    {

        try {
            $player = $this->playerService->getFirstPlayerByUUID($dto->userId);
        } catch (RuntimeException) {
            throw new SmarticoException('Internal error', 400);
        }

        if (empty($player)) {
            throw new SmarticoException('Player Not Found', 400);
        }

        if ($player->blocked) {
            throw new SmarticoException('Player Blocked', 400);
        }

        $freespin = $this->freeSpinService->getFistFreeSpinFromCacheById($dto->freespinId);

        if (empty($freespin)) {
            throw new SmarticoException('Freespin Not Found', 400);
        }

        if ($player->currency !== $freespin->currency) {
            throw new SmarticoException('Player and Freespin currencies dont match', 400);
        }

        if ($freespin->supplier !== SupplierEnum::GAMICORP_INTEGRATION->value) {
            throw new SmarticoException('Freespin issue is prohibited for this supplier', 400);
        }

        if (Carbon::parse($freespin->expired_at) < now()) {
            throw new SmarticoException('Freespin is expired', 400);
        }

        if ($this->smarticoFreeSpinRepository->isFreeSpinAlreadyTransferredToPlayer($dto->smarticoFreespinId)) {
            throw new SmarticoException('Freespin already transferred to player', 400);
        }

        if ($this->isPlayerHasActiveFreeSpinIssuedBefore($freespin, $player->id)) {
            throw new SmarticoException('Player already has this Freespin', 400);
        }

        $freeSpinCount = ($dto->count !== null) && ($dto->count < $freespin->count) ? $dto->count : $freespin->count;

        //        DB::connection('mysql.bonus')->beginTransaction();
        //        try {

        $gamicorpResponseDTO = $this->gamicorpFreeSpinBoundService->createFreeSpinBound(
            $freespin,
            $player,
            $freeSpinCount
        );

        if ($gamicorpResponseDTO->statusCode !== 200) {
            $this->freeSpinBoundService->update([
                'message' => $gamicorpResponseDTO->errorMessage,
                'status' => FreeSpinBoundStatusEnum::INACTIVE->value,
            ], [$gamicorpResponseDTO->freespinBoundId]);

            $status = false;
            $message = 'error';
        } else {

            $this->freeSpinBoundService->update([
                'is_active' => true,
                'status' => FreeSpinBoundStatusEnum::ACTIVE->value,
                'message' => $gamicorpResponseDTO->status,
            ], [$gamicorpResponseDTO->freespinBoundId]);

            $this->smarticoFreeSpinRepository->fillSmarticoFreeSpinData($dto);

            $status = true;
            $message = 'Freespin created successfully';
        }

        //            DB::commit();

        return $this->response($status, $message);
        //        } catch (Exception $e) {
        //            DB::rollBack();
        //
        //            return $this->response();
        //        }
    }

    private function isPlayerHasActiveFreeSpinIssuedBefore(FreeSpin $freeSpin, int $playerId): bool
    {
        return $freeSpin->bounds()->where('player_id', $playerId)
            ->where('status', FreeSpinBoundStatusEnum::ACTIVE->value)
            ->exists();
    }

    /**
     * @return array<string, bool|string>
     */
    private function response(bool $status = false, string $message = 'error'): array
    {
        return [
            'status' => $status,
            'message' => $message,
        ];
    }
}
