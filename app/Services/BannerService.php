<?php

namespace App\Services;

use App\Models\Banner;
use App\Repositories\BannerRepository;
use App\Services\Dto\ActivationSelectedBannersOutputDTO;
use App\Services\Dto\BannerCreateDTO;
use App\Services\Dto\BannerEnabledDTO;
use App\Services\Dto\SearchBannersDTO;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;

/**
 * Class BannerService
 */
class BannerService
{
    public function __construct(
        private readonly BannerRepository $bannerRepository
    ) {
    }

    public function create(BannerCreateDTO $bannerCreateDto): Banner
    {
        $banner = $this->fillBannerData($bannerCreateDto->getData(false));

        return $this->bannerRepository->saveBanner($banner);
    }

    public function update(BannerCreateDTO $bannerCreateDto, int $id): Banner
    {
        $banner = $this->fillBannerData($bannerCreateDto->getData(false), $id);

        return $this->bannerRepository->saveBanner($banner);
    }

    /**
     * @param int[] $ids
     * @param array<string, null|string|int|bool> $data
     */
    public function updateMany(array $ids, array $data): int
    {
        return $this->bannerRepository->updateBanners($ids, $data);
    }

    public function list(SearchBannersDTO $searchBannersDTO): LengthAwarePaginator
    {
        return $this->bannerRepository->getWithPagination($searchBannersDTO);
    }

    public function changeStatus(int $id, bool $enabled): bool
    {
        /** @var Banner $banner */
        $banner = $this->bannerRepository->getBannerById($id);
        $banner->enabled = $enabled;

        return $this->bannerRepository->save($banner);
    }

    /**
     * @param int[] $ids
     */
    public function changeStatusMany(array $ids, bool $enabled): ActivationSelectedBannersOutputDTO
    {
        if ($enabled) {
            $processedIds = $this->activateSelectedBanners($ids);
        } else {
            $processedIds = $this->deactivateSelectedBanners($ids);
        }

        $status = count($processedIds['non_processed_ids']) > 0 && count($processedIds['processed_ids']) > 0
            ? 'success partially' : (count($processedIds['processed_ids']) > 0 ? 'success' : 'not success');

        /** @var ActivationSelectedBannersOutputDTO */
        return ActivationSelectedBannersOutputDTO::fromArray(array_merge($processedIds, [
            'status' => $status,
        ]));
    }

    /**
     * @param int[] $ids
     * @return array<string, array<array<int, string>>>
     */
    public function activateSelectedBanners(array $ids): array
    {
        $activeBanners = $this->bannerRepository->getBannersForChangingEnabled($ids);
        $activeBannerIds = $activeBanners->pluck('id')->toArray();
        $this->updateMany($activeBannerIds, ['enabled' => true]);

        $noneProcessedIds = array_diff($ids, $activeBannerIds);
        $noneProcessedNames = [];
        if (count($noneProcessedIds) > 0) {
            $noneProcessedNames = $this->bannerRepository->getBannersByIds($noneProcessedIds)
                ->pluck('name', 'id')->toArray();
        }

        return [
            'processed_ids' => $activeBanners->pluck('name', 'id')->toArray(),
            'non_processed_ids' => $noneProcessedNames,
        ];
    }

    /**
     * @param int[] $ids
     * @return array<string, array<array<int, string>>>
     */
    public function deactivateSelectedBanners(array $ids): array
    {
        $this->updateMany($ids, ['enabled' => false]);

        return [
            'processed_ids' => $this->bannerRepository->getBannersByIds($ids)
                ->pluck('name', 'id')->toArray(),
            'non_processed_ids' => [],
        ];
    }

    public function delete(int $id): ?bool
    {
        $banner = $this->bannerRepository->getBannerById($id);

        return $this->bannerRepository->remove($banner);
    }

    public function enabledBanners(BannerEnabledDTO $dto): LengthAwarePaginator
    {
        return Cache::remember(
            'banners_enabled_' . md5(serialize($dto)),
            60,
            fn() => $this->bannerRepository->getEnabledBanners($dto)
        );
    }

    public function getBannerById(int $id): ?Banner
    {
        return $this->bannerRepository->getBannerById($id);
    }

    /**
     * @param array<string, null|string|int|bool> $data
     */
    private function fillBannerData(array $data, ?int $id = null): Banner
    {
        $banner = $id ? $this->bannerRepository->getBannerById($id) : new Banner();
        $banner->fill($data);

        return $banner;
    }
}
