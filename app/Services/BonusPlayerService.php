<?php

namespace App\Services;

use App\Enums\BonusPlayerStatusEnum;
use App\Exceptions\Exception;
use App\Models\Bonus;
use App\Models\BonusPlayer;
use App\Models\BonusPlayerCard;
use App\Repositories\BonusPlayerRepository;
use App\Services\Dto\PlayerDto;
use Illuminate\Contracts\Queue\EntityNotFoundException;
use Illuminate\Database\Eloquent\ModelNotFoundException;

/**
 * Class BonusPlayerService
 */
class BonusPlayerService
{
    public function __construct(
        private readonly BonusPlayerRepository $bonusPlayerRepository,
        private readonly BonusPlayerCardService $bonusPlayerCardService,
        private readonly BonusInfoService $bonusInfoService,
        private readonly PlayerService $playerService,
    ) {
    }

    public function saveBonus(int $bonusId, int $playerId, int $bonusInfoId): BonusPlayerCard
    {
        $bonusCard = new BonusPlayerCard();
        $bonusCard->bonus_id = $bonusId;
        $bonusCard->player_id = $playerId;
        $bonusCard->bonus_info_id = $bonusInfoId;
        $bonusCard->save();

        return $bonusCard;
    }

    public function getBonusPivotPlayerData(Bonus $bonus, int $playerId): ?BonusPlayer
    {
        $currentTriggerSession = $bonus->getCurrentTriggerSession();
        $bonusPlayer = $this->bonusPlayerRepository->getBonusPlayerQueryWithSession($playerId, $bonus->id, $currentTriggerSession);

        if ($this->bonusInfoService->isBonusForSmartico($bonus->id)) {
            $bonusPlayer->whereNotIn('status', [BonusPlayerStatusEnum::UNSELECTED, BonusPlayerStatusEnum::ACTIVATED]);
        }

        $bonusPlayerFirst = $bonusPlayer->first();

        return $this->makePivotExpiredIfBonusExpired($bonusPlayerFirst, $bonus);
    }

    public function selectActiveBonus(PlayerDto $player, Bonus $bonus): void
    {
        $selectedBonusPlayerData = $this->bonusPlayerRepository->getSelectedBonusPlayerData($player->id);

        $this->unselectSelectedBonus($selectedBonusPlayerData);

        $unselectedBonusPlayerData = $this->bonusPlayerRepository->getUnselectedBonusPlayerData($player->id, $bonus->id);

        if ($unselectedBonusPlayerData) {
            $unselectedBonusPlayerData->status = BonusPlayerStatusEnum::WAITING_FOR_DEPOSIT->value;
            $unselectedBonusPlayerData->save();
        } else {
            $pivotModel = new BonusPlayer();
            $pivotModel->bonus_id = $bonus->id;
            $pivotModel->player_id = $player->id;
            $pivotModel->trigger_session_id = optional($bonus->getCurrentTriggerSession())->id;
            $pivotModel->status = BonusPlayerStatusEnum::WAITING_FOR_DEPOSIT->value;
            $pivotModel->deposits_count = $bonus->is_welcome_4_steps ? 0 : null;
            $pivotModel->save();
        }

        $this->clearPlayerCache($player->uuid);
    }

    public function detachActiveBonus(int $playerId): void
    {
        $selectedBonusPlayerData = $this->bonusPlayerRepository->getSelectedBonusPlayerData($playerId);
        $this->unselectSelectedBonus($selectedBonusPlayerData);
    }

    public function isBonusUsed(int $playerId, int $bonusId, ?int $triggerSessionId = null): bool
    {
        $isForSmartico = $this->bonusInfoService->isBonusForSmartico($bonusId);

        if ($isForSmartico && !$this->bonusPlayerCardService->hasPlayerNotUsedBonusPlayerCard($playerId, $bonusId)) {
            return true;
        }

        if (!$isForSmartico) {
            return $this->bonusPlayerRepository->isNorUnselectedBonusPlayerWithSession($playerId, $bonusId, $triggerSessionId);
        }

        return $this->bonusPlayerRepository->isNorUnselectedAndActivatedBonusPlayer($playerId, $bonusId, $triggerSessionId);
    }

    public function getSelectedBonusByPlayerUuid(string $playerUuid): Bonus
    {
        $player = $this->playerService->getPlayerByUUID($playerUuid);
        $selectedBonusPlayer = $this->bonusPlayerRepository->getSelectedBonusPlayerData($player->id);

        if (!$selectedBonusPlayer) {
            throw new ModelNotFoundException(sprintf('Bonus for player with uuid %s not found', $playerUuid));
        }

        $this->makePivotExpiredIfBonusExpired($selectedBonusPlayer, $selectedBonusPlayer->bonus);

        return $selectedBonusPlayer->isStatus(BonusPlayerStatusEnum::EXPIRED)
            ? throw new ModelNotFoundException(sprintf('Bonus for player with uuid %s is expired', $playerUuid))
            : $selectedBonusPlayer->bonus;
    }

    private function makePivotExpiredIfBonusExpired(?BonusPlayer $bonusPlayer, Bonus $bonus): ?BonusPlayer
    {
        if ($bonusPlayer && $bonusPlayer->status === BonusPlayerStatusEnum::WAITING_FOR_DEPOSIT->value) {
            if ($bonus->is_deposit || $bonus->is_free_spin_for_deposit) {
                $activeTriggerSession = $bonus->getCurrentTriggerSession();

                $isBonusExpired = $bonusPlayer->trigger_session_id !== optional($activeTriggerSession)->id;
            } else {
                $isBonusExpired = !$bonus->isValidTimeBonus();
            }

            if ($isBonusExpired) {
                $bonusPlayer->status = BonusPlayerStatusEnum::EXPIRED->value;
                $bonusPlayer->save();
            }

            $player = $this->playerService->getPlayerById($bonusPlayer->player_id);
            $this->clearPlayerCache($player->uuid);
        }

        return $bonusPlayer;
    }

    private function unselectSelectedBonus(?BonusPlayer $selectedBonusPlayer): void
    {
        if ($selectedBonusPlayer) {
            if ($selectedBonusPlayer->deposits_count > 0) {
                $selectedBonusPlayer->status = BonusPlayerStatusEnum::UNSELECTED->value;
                $selectedBonusPlayer->save();
            } else {
                $selectedBonusPlayer->delete();
            }

            $player = $this->playerService->getPlayerById($selectedBonusPlayer->player_id);
            $this->clearPlayerCache($player->uuid);
        }
    }

    public function getSelectedBonus(int $playerId): ?Bonus
    {
        $selectedBonusPlayerData = $this->bonusPlayerRepository->getSelectedBonusPlayerData($playerId);

        if (!$selectedBonusPlayerData) {
            return null;
        }

        try {
            /** @var BonusService $bonusService */
            $bonusService = app(BonusService::class);
            $bonus = $bonusService->getBonusById($selectedBonusPlayerData->bonus_id);
        } catch (EntityNotFoundException) {
            $bonus = null;
        }

        if ($bonus) {
            $selectedBonusPlayerData = $this->makePivotExpiredIfBonusExpired($selectedBonusPlayerData, $bonus);

            if ($selectedBonusPlayerData->status === BonusPlayerStatusEnum::EXPIRED->value) {
                $bonus = null;
            }
        }

        return $bonus;
    }

    /**
     * @throws Exception
     */
    public function activateSelectedBonus(int $playerId, int $bonusId): void
    {
        $selectedBonusPlayerData = $this->bonusPlayerRepository->getSelectedBonusPlayerData($playerId);

        if (!$selectedBonusPlayerData || $selectedBonusPlayerData->bonus_id !== $bonusId) {
            throw new Exception('Selected bonus is differs from activated bonus', 500);
        }

        if ($selectedBonusPlayerData->bonus->is_welcome_4_steps) {
            ++$selectedBonusPlayerData->deposits_count;

            if ($selectedBonusPlayerData->deposits_count === 4) {
                $selectedBonusPlayerData->status = BonusPlayerStatusEnum::ACTIVATED->value;
            }
        } else {
            $selectedBonusPlayerData->status = BonusPlayerStatusEnum::ACTIVATED->value;
        }

        $this->bonusPlayerRepository->save($selectedBonusPlayerData);

        $player = $this->playerService->getPlayerById($playerId);
        $this->clearPlayerCache($player->uuid);

        $this->bonusPlayerRepository->updateUnselectedBonusPlayer($playerId, ['status' => BonusPlayerStatusEnum::EXPIRED]);
        $this->bonusPlayerCardService->updateUnusedBonusPlayerCard($playerId, $bonusId, ['is_used' => true]);
    }

    public function getSelectedBonusPlayerData(int $playerId): ?BonusPlayer
    {
        return $this->bonusPlayerRepository->getSelectedBonusPlayerData($playerId);
    }

    /**
     * Remove this method and related code after 04.05.2025
     *  if there will not appear bugs related to disabled ClearPlayerCacheJob
     */
    public function clearPlayerCache(string $playerUuid): void
    {
        // dispatch(new ClearPlayerCacheJob($playerUuid));
    }
}
