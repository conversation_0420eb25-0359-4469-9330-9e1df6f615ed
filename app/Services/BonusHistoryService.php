<?php

namespace App\Services;

use App\Enums\BonusStatusEnum;
use App\Models\DepositBonusInfo;
use App\Services\Dto\PlayerDto;
use Illuminate\Support\Facades\Log;

/**
 * Class BonusService
 */
class BonusHistoryService
{
    public function createBonusInfo(int $bonusId, PlayerDto $player, int $smarticoBonusId, string $type = 'deposit'): DepositBonusInfo
    {
        $depositBonusInfo = new DepositBonusInfo();
        $depositBonusInfo->player_id = $player->id;
        $depositBonusInfo->email = $player->email;
        $depositBonusInfo->bonus_id = $bonusId;
        $depositBonusInfo->smartico_bonus_id = (string)$smarticoBonusId;
        $depositBonusInfo->save();

        return $depositBonusInfo;
    }

    public function saveBonusInfoStatus(DepositBonusInfo $bonusInfo, string $message, BonusStatusEnum $status): void
    {
        $bonusInfo->text = $message;
        $bonusInfo->status = $status->value;
        $bonusInfo->save();

        if (empty($message)) {
            Log::error('Bonus Card is not applied');
        } else {
            Log::error($message);
        }
    }

    public function existsSuccessfulTransfer(int $smarticoBonusId): bool
    {
        return DepositBonusInfo::query()
            ->where('smartico_bonus_id', (string)$smarticoBonusId)
            ->where('status', 'success')
            ->exists();
    }
}
