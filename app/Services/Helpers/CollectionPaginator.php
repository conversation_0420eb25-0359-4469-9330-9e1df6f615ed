<?php

namespace App\Services\Helpers;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;

class CollectionPaginator
{
    /**
     * @param ResourceCollection<int, Model> $collection
     *
     * @return array<string, string|array<string, int|string>>
     */
    public static function paginate(ResourceCollection $collection, int $page = 1, int $limit = 15): array
    {
        $offset = ($page - 1) * $limit;

        $items = $collection->collection->slice($offset, $limit)->values();

        $paginator = new LengthAwarePaginator(
            $items,
            $collection->count(),
            $limit,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return [
            'data' => $paginator->items(),
            'meta' => [
                'current_page' => $paginator->currentPage(),
                'from' => $paginator->firstItem(),
                'last_page' => $paginator->lastPage(),
                'per_page' => $paginator->perPage(),
                'to' => $paginator->lastItem(),
                'total' => $paginator->total(),
            ],
            'links' => [
                'first' => $paginator->url(1),
                'last' => $paginator->url($paginator->lastPage()),
                'prev' => $paginator->previousPageUrl(),
                'next' => $paginator->nextPageUrl(),
            ],
        ];
    }
}
