<?php

namespace App\Services\Helpers;

use SplFileObject;

class CsvFileReader
{
    /**
     * @return array<array<string>>
     */
    public static function readFile(string $path, string $separator, ?callable $callable = null): array
    {
        $file = new SplFileObject($path);
        $file->setCsvControl($separator);
        $file->setFlags(SplFileObject::READ_CSV);
        $header = [];
        $dataArray = [];

        foreach ($file as $row) {
            if (empty($header)) {
                $header = $row;

                continue;
            }
            if (empty(array_filter($row))) {
                continue;
            }
            $item = array_combine($header, $row);
            $dataArray[] = $item;

            if ($callable !== null) {
                $callable($item);
            }
        }

        return $dataArray;
    }
}
