<?php

declare(strict_types=1);

namespace App\Services;

use App\Clients\AppClient;
use App\Models\UserBalance;
use App\Repositories\PlayerRepository;
use App\Services\Dto\AuthPlayerDto;
use App\Services\Dto\BalanceInfoDTO;
use App\Services\Dto\PlayerDto;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;

class PlayerService
{
    private const PLAYERS_UUID_CACHE_PREFIX = 'cache:players_uuid:';
    private const PLAYERS_ID_CACHE_PREFIX = 'cache:players_id:';
    private const PLAYERS_TOKEN_CACHE_PREFIX = 'cache:players_token:';
    private const PLAYERS_HAS_DEPOSITS_CACHE_PREFIX = 'cache:players_has_deposits:';

    public function __construct(
        private readonly PlayerRepository $playerRepository,
        private readonly CacheService $cacheService,
        private readonly AppClient $appClient,
    ) {
    }

    /**
     * @param array<string> $playerExtraData
     */
    public function getPlayerDataByToken(string $token, array $playerExtraData = []): ?AuthPlayerDto
    {
        $playerData = Cache::remember(
            self::PLAYERS_TOKEN_CACHE_PREFIX . md5($token) . implode(':', $playerExtraData),
            Carbon::now()->addDay(),
            fn() => $this->appClient->getUserInfoByToken($token, $playerExtraData)
        );

        if (empty($playerData)) {
            return null;
        }

        return new AuthPlayerDto($playerData);
    }

    public function getPlayerByUUID(string $uuid): PlayerDto
    {
        $players = $this->getPlayersByUUIDs([$uuid]);

        if ($players->isEmpty()) {
            throw new ModelNotFoundException(sprintf('Player with uuid %s not found', $uuid));
        }

        return $players->first();
    }

    public function getFirstPlayerByUUID(string $uuid): ?PlayerDto
    {
        $players = $this->getPlayersByUUIDs([$uuid]);

        return !$players->isEmpty() ? $players->first() : null;
    }

    /**
     * @param string[] $uuids
     *
     * @return Collection<int, PlayerDto>
     */
    public function getPlayersByUUIDs(array $uuids): Collection
    {
        $players = $this->cacheService->getCachedItemsAndFetchMissing(
            self::PLAYERS_UUID_CACHE_PREFIX,
            'uuid',
            $uuids,
            fn(string ...$uuids): array => $this->appClient->getPlayersByUuids(...$uuids),
            Carbon::now()->addWeek()
        );

        return Collection::make(array_filter($players))->mapInto(PlayerDto::class);
    }

    /**
     * @param int[] $ids
     *
     * @return Collection<int, PlayerDto>
     */
    public function getPlayersByIds(array $ids): Collection
    {
        $players = $this->cacheService->getCachedItemsAndFetchMissing(
            self::PLAYERS_ID_CACHE_PREFIX,
            'id',
            $ids,
            fn(int ...$ids): array => $this->appClient->getPlayersByIds(...$ids),
            Carbon::now()->addWeek()
        );

        return Collection::make(array_filter($players))->mapInto(PlayerDto::class);
    }

    public function getPlayerByID(int $id): PlayerDto
    {
        $players = $this->getPlayersByIds([$id]);

        if ($players->isEmpty()) {
            throw new ModelNotFoundException(sprintf('Player with id %s not found', $id));
        }

        return $players->first();
    }

    public function getBalanceByPlayerID(int $id): UserBalance
    {
        return $this->playerRepository->getBalanceByPlayerID($id);
    }

    public function isPlayerBlocked(int $id): bool
    {
        $players = $this->getPlayersByIds([$id]);

        return (bool)$players->first()?->blocked;
    }

    public function hasPlayerDeposits(int $id): bool
    {
        $hasDepositsCache = Cache::get(self::PLAYERS_HAS_DEPOSITS_CACHE_PREFIX . $id);
        if ($hasDepositsCache !== null) {
            return $hasDepositsCache;
        }

        $hasDeposits = null;
        if (request()->attributes->get('playerId') === $id) {
            $hasDeposits = request()->attributes->get('has_deposits');
        }

        $hasDeposits = $hasDeposits === true || $this->appClient->hasPlayerDeposits($id);

        Cache::put(
            self::PLAYERS_HAS_DEPOSITS_CACHE_PREFIX . $id,
            $hasDeposits,
            $hasDeposits ? Carbon::now()->addWeek() : 10,
        );

        return $hasDeposits;
    }

    public function getBalanceIdByPlayerUuid(string $uuid): int
    {
        return (int)Cache::remember(
            self::PLAYERS_UUID_CACHE_PREFIX . "player_balance_id_$uuid",
            Carbon::now()->addMinutes(30),
            fn() => BalanceInfoDTO::fromArray($this->appClient->getBalanceInfoByPlayerUuid($uuid))->id
        );
    }

    public function getBalanceByPlayerUuid(string $uuid): int
    {
        return (int)Cache::remember(
            self::PLAYERS_UUID_CACHE_PREFIX . "player_balance_$uuid",
            Carbon::now()->addSeconds(10),
            fn() => BalanceInfoDTO::fromArray($this->appClient->getBalanceInfoByPlayerUuid($uuid))->balance
        );
    }
}
