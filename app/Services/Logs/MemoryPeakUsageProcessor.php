<?php

declare(strict_types=1);

namespace App\Services\Logs;

use Illuminate\Support\Facades\App;
use Monolog\LogRecord;
use Monolog\Processor\ProcessorInterface;

class MemoryPeakUsageProcessor implements ProcessorInterface
{
    public function __invoke(LogRecord $record): LogRecord
    {
        if (App::environment('production')) {
            return $record;
        }

        $record->extra['memory_peak_usage'] = $this->getMemoryUsageInMb();

        return $record;
    }

    private function getMemoryUsageInMb(): float
    {
        return round(memory_get_peak_usage(true) / 1024 / 1024, 4);
    }
}
