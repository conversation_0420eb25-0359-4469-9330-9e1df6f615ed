<?php

namespace App\Services;

use Illuminate\Http\Request;
use Spatie\ResponseCache\Hasher\DefaultHasher;

class CustomPersonalCacheHasher extends DefaultHasher
{
    private const excludedParams = [
        '_ip_address',
    ];

    public function getHashFor(Request $request): string
    {
        $requestUri = $request->getRequestUri();

        foreach (self::excludedParams as $param) {
            if (preg_match("/([?|&]?)($param=[^&]*)([&]?)/", $requestUri, $matches)) {
                $match = $matches[0];

                if (($matches[1] === '?' || $matches[1] === '&') && $matches[3] === '&') {
                    $match = substr($match, 1);
                }

                $requestUri = str_replace($match, '', $requestUri);
            }
        }

        return 'responsecache-'.md5(
                "{$request->getHost()}-$requestUri-{$request->getMethod()}/".$this->cacheProfile->useCacheNameSuffix($request)
            );
    }
}
