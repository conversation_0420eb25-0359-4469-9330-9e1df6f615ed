<?php

namespace App\Services;

use App\Models\DataSubscriptions;
use App\Repositories\DataSubscriptionRepository;

/**
 * Class DataSubscriptionService
 */
class DataSubscriptionService
{
    public function __construct(
        private readonly DataSubscriptionRepository $dataSubscriptionRepository
    ) {
    }

    public function getByProviderName(string $name): ?DataSubscriptions
    {
        /** @var DataSubscriptions */
        return $this->dataSubscriptionRepository->getByProviderName($name);
    }
}
