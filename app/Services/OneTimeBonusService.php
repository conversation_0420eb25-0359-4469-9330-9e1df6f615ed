<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\BonusStatusEnum;
use App\Exceptions\InvalidArgumentException;
use App\Exceptions\SmarticoException;
use App\Jobs\OneTimeBonusStartApplyJob;
use App\Models\IssuedBonus;
use App\Models\MassOnetimeBonusInfo;
use App\Models\Bonus;
use App\Repositories\BonusApplyOnetimeRepository;
use App\Services\Dto\ApplyBonusSmarticoOnetimeDTO;
use App\Services\Dto\BonusApplyForPlayerDTO;
use App\Services\Dto\BonusApplySearchDTO;
use App\Services\Dto\MassBonusApplyDTO;
use App\Services\Helpers\CsvFileReader;
use App\Traits\FileHelper;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Throwable;

class OneTimeBonusService
{
    use FileHelper;

    public function __construct(
        private readonly PlayerService $playerService,
        private readonly BonusApplyOnetimeRepository $bonusApplyOnetimeRepository,
        private readonly BonusService $bonusService,
        private readonly BonusApplyService $bonusApplyService,
    ) {
    }

    /**
     * @return array<string, bool|string>
     *
     * @throws Exception
     */
    public function applyBonusForPlayer(BonusApplyForPlayerDTO $dto): array
    {
        /** @var Bonus $bonus */
        $bonus = $this->bonusService->getBonusById($dto->bonusId);
        $bonusInfo = $this->createBonusInfo([
            'bonusId' => $dto->bonusId,
            'playerId' => $dto->playerId,
            'amount' => $dto->amount ?? $bonus->bonuses[0],
            'smarticoBonusId' => $dto->smarticoBonusId,
        ]);

        return $this->apply($bonusInfo->id);
    }

    /**
     * @return array<string, bool|string>
     *
     * @throws Exception
     */
    public function apply(int $bonusInfoId): array
    {
        $bonusInfo = $this->bonusApplyOnetimeRepository->getOneTimeBonusDetail($bonusInfoId);

        if (!$bonusInfo->player_id) {
            throw new Exception('The bonus cannot be credited. Player ID is not correct', 422);
        }

        $player = $this->playerService->getPlayerByID($bonusInfo->player_id);
        $bonusInfo->email = $player->email;
        $bonusInfo->player_id = $player->id;
        $bonusInfo->save();

        if ($bonusInfo->amount) {
            try {
                $bonus = $this->bonusService->getBonusById($bonusInfo->bonus_id);

                $this->bonusApplyService->applyOnetimeForPlayer($bonus, $player, $bonusInfo->amount);
            } catch (Throwable $e) {
                Log::error('Bonus not possible to issue', [
                    'error' => $e->getMessage(),
                    'bonus_id' => $bonusInfo->id,
                    'player_id' => $player->id,
                    'amount' => $bonusInfo->amount,
                    'smartico_bonus_id' => $bonusInfo->smartico_bonus_id,
                ]);

                if ($e instanceof ModelNotFoundException) {
                    throw new SmarticoException('Bonus Not Found', 400);
                }

                throw new SmarticoException('Bonus not possible to issue', 400);
            }

            $message = 'Bonus accepted for player';
            $bonusInfo = $this->saveBonusInfoStatus($bonusInfo, $message, BonusStatusEnum::SUCCESS_STATUS);
        } else {
            $message = 'There was no money';
            $bonusInfo = $this->saveBonusInfoStatus($bonusInfo, $message, BonusStatusEnum::ERROR_STATUS);
        }

        return [
            'status' => $bonusInfo->status === BonusStatusEnum::SUCCESS_STATUS->value,
            'message' => $bonusInfo->text,
        ];
    }

    /**
     * @param array<string, int|string> $data
     */
    public function createBonusInfo(array $data): ?MassOnetimeBonusInfo
    {
        if (isset($data['smarticoBonusId']) && $data['smarticoBonusId']) {
            $isBonusAlreadyTransferredToPlayer = $this->bonusApplyOnetimeRepository->isBonusAlreadyTransferredToPlayer(
                $data['smarticoBonusId']
            );

            if ($isBonusAlreadyTransferredToPlayer) {
                throw new SmarticoException('Bonus Already Transferred to player', 422);
            }
        }

        return $this->saveMassOnetimeBonusInfo($data);
    }

    public function saveBonusInfoStatus(MassOnetimeBonusInfo $bonusInfo, string $message, BonusStatusEnum $status): MassOnetimeBonusInfo
    {
        $bonusInfo->status = $status->value;
        $bonusInfo->text = $message;
        $bonusInfo->save();

        if (empty($message)) {
            Log::error('OneTime bonus not applied');
        } else {
            Log::error($message);
        }

        return $bonusInfo;
    }

    public function list(BonusApplySearchDTO $dto): LengthAwarePaginator
    {
        return $this->bonusApplyOnetimeRepository->list($dto);
    }

    public function startApplyJob(MassBonusApplyDTO $dto): void
    {
        $key = 'mass_bonus_apply_' . md5((string)time());
        $fileDataList = CsvFileReader::readFile($dto->file->getPathname(), $this->getSeparator($dto->file));

        Cache::set($key, $fileDataList, 3600);

        dispatch(new OneTimeBonusStartApplyJob($dto->bonusId, $key));
    }

    /**
     * @param array<string, int|string> $data
     */
    private function saveMassOnetimeBonusInfo(array $data): MassOnetimeBonusInfo
    {
        $massOnetimeBonusInfo = new MassOnetimeBonusInfo();
        $massOnetimeBonusInfo->bonus_id = $data['bonusId'];
        $massOnetimeBonusInfo->smartico_bonus_id = !empty($data['smarticoBonusId']) ? $data['smarticoBonusId'] : null;
        $massOnetimeBonusInfo->player_id = !empty($data['playerId']) ? $data['playerId'] : null;
        $massOnetimeBonusInfo->email = !empty($data['email']) ? $data['email'] : null;
        $massOnetimeBonusInfo->amount = !empty($data['amount']) ? $data['amount'] : null;
        $massOnetimeBonusInfo->save();

        return $massOnetimeBonusInfo;
    }

    /**
     * @throws InvalidArgumentException
     */
    public function applySmarticoBonusOneTime(ApplyBonusSmarticoOnetimeDTO $dto): IssuedBonus
    {
        $bonus = $this->bonusService->getBonusById($dto->bonusId);
        $player = $this->playerService->getPlayerByID($dto->playerId);

        return $this->bonusApplyService->applyOnetimeForPlayer($bonus, $player, $dto->amount);
    }
}
