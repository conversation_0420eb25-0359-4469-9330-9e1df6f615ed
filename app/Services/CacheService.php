<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use RuntimeException;
use Throwable;

class CacheService
{
    /**
     * Retrieves cached items by keys and fetches missing items if not found in cache.
     *
     * @param string $cacheKeyField the field representing the cache key
     * @param string[]|int[] $keys An array of values by which we search for items
     * @param callable $fetchMissingItems a callback function to fetch missing items
     *
     * @return array the combined array of cached items and fetched missing items
     *
     * @phpstan-ignore-next-line
     */
    public function getCachedItemsAndFetchMissing(
        string $cachePrefix,
        string $cacheKeyField,
        array $keys,
        callable $fetchMissingItems,
        mixed $cacheDuration = 60 * 60 * 24
    ): array {
        $cachedItems = $this->getCachedItemsByKeys($cachePrefix, $keys, $cacheKeyField);
        $cachedKeys = array_keys($cachedItems);

        $missingKeys = array_diff($keys, $cachedKeys);
        $missingItems = $this->receiveAndCacheItems(
            $cachePrefix,
            $missingKeys,
            $cacheKeyField,
            $fetchMissingItems,
            $cacheDuration
        );

        return $cachedItems + $missingItems;
    }

    /**
     * @param int[]|string[] $keys
     *
     * @return array<int>
     */
    private function getCachedItemsByKeys(string $cachePrefix, array $keys, string $cacheKeyField): array
    {
        $cacheKeys = array_map(fn($key) => $this->getCacheKey($cachePrefix, $key), $keys);
        $items = Cache::many($cacheKeys);

        return Arr::keyBy(array_filter($items), $cacheKeyField);
    }

    private function getCacheKey(string $cachePrefix, float|int|string $key): string
    {
        return $cachePrefix . $key;
    }

    /**
     * @param int[]|string[] $keys
     *
     * @return array<int>
     */
    private function receiveAndCacheItems(
        string $cachePrefix,
        array $keys,
        string $cacheKeyField,
        callable $fetchMissingItems,
        mixed $cacheDuration
    ): array {
        if (empty($keys)) {
            return [];
        }

        try {
            $items = $fetchMissingItems(...$keys);
        } catch (Throwable $e) {
            Log::error($e->getMessage(), ['exception' => $e->getMessage(), 'keys' => $keys]);

            throw new RuntimeException(
                sprintf('Failed to get items with %s in: %s', $cacheKeyField, implode(', ', $keys))
            );
        }

        $items = Arr::keyBy($items, fn($item) => $this->getCacheKey($cachePrefix, $item[$cacheKeyField]));

        if (empty($items)) {
            return [];
        }

        Cache::putMany($items, $cacheDuration);

        return Arr::keyBy($items, $cacheKeyField);
    }
}
