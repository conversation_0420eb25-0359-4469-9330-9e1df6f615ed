<?php

declare(strict_types=1);

namespace App\Services;

use App\Clients\AppClient;
use App\Services\Dto\SlotProviderDto;
use Illuminate\Support\Carbon;

class SlotProviderService
{
    private const SLOTS_PROVIDERS_CACHE_PREFIX = 'cache:slot-providers:';

    public function __construct(
        private readonly AppClient $appClient,
        private readonly CacheService $cacheService,
    ) {
    }

    /**
     * @param array<int> $ids
     *
     * @return SlotProviderDto[]
     */
    public function getSlotProvidersByIds(array $ids): array
    {
        $slotProviders = $this->cacheService->getCachedItemsAndFetchMissing(
            self::SLOTS_PROVIDERS_CACHE_PREFIX,
            'id',
            $ids,
            fn(int ...$ids): array => $this->appClient->getSlotProvidersByIds(...$ids),
            Carbon::now()->addWeek()
        );

        return array_map(
            static fn(array $slotProvider): SlotProviderDto => SlotProviderDto::fromArray($slotProvider),
            array_filter($slotProviders)
        );
    }
}
