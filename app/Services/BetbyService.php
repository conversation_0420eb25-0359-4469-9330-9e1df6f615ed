<?php

namespace App\Services;

use App\Exceptions\ApiException;
use App\Models\DataSubscriptions;
use App\Models\IssuedBonus;
use App\Repositories\BetbyRepository;
use App\Services\Dto\BetbySearchDTO;
use GuzzleHttp\Promise\PromiseInterface;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * Class BonusService
 */
class BetbyService
{
    public function __construct(
        private readonly DataSubscriptionService $dataSubscriptionService,
        private readonly BetbyRepository $betbyRepository,
    ) {
    }

    /**
     * @return null|array<string, array<int, array<string, null|array<string, null|bool|int|string>|bool|int|string>>>
     *
     * @throws Exception
     */
    public function getTemplates(BetbySearchDTO $dto): ?array
    {
        $response = $this->doRequest('GET', '/api/v1/bonus/templates');

        $content = $response->json();

        if (empty($content) || $response->failed()) {
            Log::info('Betby /api/v1/bonus/templates bad response.', [
                'status' => $response->status(),
                'response' => $response->body(),
            ]);

            return null;
        }

        return $this->betbyRepository->getFilteredData($content['items'], $dto);
    }

    /**
     * @return array<array<string, null|int|string|bool>>
     *
     * @throws Exception
     */
    public function sendBetbyRequest(IssuedBonus $issuedBonus, string $playerUuid): array
    {
        $response = $this->doRequest('POST', '/api/v1/bonus/mass_give_bonus', [
            'templateId' => $issuedBonus->bonus_external_id,
            'playersData' => array_map(static fn(string $uuid) => [
                'external_player_id' => $uuid,
                'currency' => $issuedBonus->currency,
                'amount' => $issuedBonus->balance,
                'force_activated' => false,
            ], [$playerUuid]),
        ]);

        if ($response->failed()) {
            throw new ApiException('Failed to send request to Betby', [
                'response' => $response,
            ]);
        }

        if (!isset($response['result'])) {
            throw new ApiException('Wrong betby response');
        }

        return $response['result']['items'];
    }

    /**
     * @param array<string, null|list<array<string, bool|int|string>>|string> $body
     *
     * @throws Exception
     */
    private function doRequest(string $method, string $url, array $body = []): PromiseInterface|Response
    {
        /** @var DataSubscriptions $subscription */
        $subscription = $this->dataSubscriptionService->getByProviderName('betby');

        return Http::baseUrl(config('app.betby_host'))
            ->withHeaders([
                'Content-Type' => 'application/json',
                'accessToken' => $subscription->access_token,
            ])
            ->withBody(json_encode(array_merge(['subscriberId' => (int)$subscription->sub_id], $body)))
            ->send($method, $url);
    }
}
