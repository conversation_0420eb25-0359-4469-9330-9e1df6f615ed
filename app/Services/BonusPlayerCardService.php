<?php

namespace App\Services;

use App\Repositories\BonusPlayerCardRepository;

class BonusPlayerCardService
{
    public function __construct(
        private readonly BonusPlayerCardRepository $bonusPlayerCardRepository,
        private readonly BonusInfoService $bonusInfoService,
    ) {
    }

    public function hasPlayerNotUsedBonusPlayerCard(int $playerId, int $bonusId): bool
    {
        return $this->bonusPlayerCardRepository->hasPlayerNotUsedBonusPlayerCard($playerId, $bonusId);
    }

    public function hasValidBonusCard(int $playerId, int $bonusId): bool
    {
        $bonusInfo = $this->bonusInfoService->isBonusForSmartico($bonusId);
        if (!$bonusInfo) {
            return true;
        }

        $bonusPlayerCard = $this->bonusPlayerCardRepository->hasBonusPlayerCard($playerId, $bonusId);

        return ! (!$bonusPlayerCard);
    }

    /**
     * @param array<string, int|string|bool> $data
     */
    public function updateUnusedBonusPlayerCard(int $playerId, int $bonusId, array $data): int
    {
        return $this->bonusPlayerCardRepository->notUsedBonusPlayerCardQuery($playerId, $bonusId)->update($data);
    }
}
