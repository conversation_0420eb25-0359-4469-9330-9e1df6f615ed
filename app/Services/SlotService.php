<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\DataSubscriptions;
use App\Repositories\SlotRepository;
use App\Services\Dto\SlotDto;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class SlotService
{
    public function __construct(
        private readonly SlotRepository $slotsRepository,
    ) {
    }

    public function getSlotById(int $slotId): SlotDto
    {
        $slots = $this->getSlotListByIds([$slotId]);

        if (!isset($slots[$slotId])) {
            throw new ModelNotFoundException(sprintf('Slot with id %s not found', $slotId));
        }

        return $slots[$slotId];
    }

    /**
     * @param array<int> $slotIds
     *
     * @return array<int, SlotDto>
     */
    public function getSlotListByIds(array $slotIds): array
    {
        if (empty($slotIds)) {
            return [];
        }

        $slots = DB::table('slots')->whereIn('slots.id', $slotIds)
            ->select(['slots.id', 'slots.name', 'slots.external_id', 'slots.image', 'slots.friendly_url', 'slots.provider', 'slots_providers.name as provider_name'])
            ->leftJoin('slots_providers', static function ($join): void {
                $join->on('slots.external_provider_id', '=', 'slots_providers.id');
            })
            ->get()->map(static fn($slot) => [
                'id' => $slot->id,
                'name' => $slot->name,
                'slot_provider' => $slot->provider,
                'provider' => $slot->provider_name,
                'external_id' => $slot->external_id,
                'image' => $slot->image,
                'link_name' => $slot->friendly_url,
            ])->toArray();

        $slots = array_map(static fn(array $slot): SlotDto => SlotDto::fromArray($slot), array_filter($slots));

        return Arr::keyBy($slots, 'id');
    }

    public function getSubscriptionByProvider(string $provider): DataSubscriptions
    {
        return $this->slotsRepository->getSubscriptionByProvider($provider);
    }

    public function getSubscriptionBySlotId(int $slotId): DataSubscriptions
    {
        $slot = $this->getSlotById($slotId);

        return $this->slotsRepository->getSubscriptionByProvider($slot->slot_provider);
    }
}
