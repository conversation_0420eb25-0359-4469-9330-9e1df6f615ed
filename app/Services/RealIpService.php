<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Http\Request;

class RealIpService
{
    protected const IP_HEADERS = [
        'HTTP_CLIENT_IP',
        'HTTP_X_FORWARDED_FOR',
        'HTTP_X_FORWARDED',
        'HTTP_X_CLUSTER_CLIENT_IP',
        'HTTP_FORWARDED_FOR',
        'HTTP_FORWARDED',
        'REMOTE_ADDR',
    ];

    public function getIpAddress(): ?string
    {
        /** @var Request $request */
        $request = request();
        foreach (self::IP_HEADERS as $key) {
            if (!$request->server->has($key)) {
                continue;
            }

            foreach (explode(',', $request->server->get($key)) as $ip) {
                $ip = trim($ip);
                if ($this->isValidIp($ip)) {
                    return $ip;
                }
            }
        }

        return null;
    }

    private function isValidIp(string $ip): bool
    {
        return filter_var(
            $ip,
            FILTER_VALIDATE_IP,
            FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE
        ) !== false;
    }
}
