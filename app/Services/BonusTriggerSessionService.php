<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\BonusTriggerSession;
use App\Repositories\BonusTriggerSessionRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class BonusTriggerSessionService
{
    public function __construct(
        private readonly BonusTriggerSessionRepository $bonusTriggerSessionRepository
    ) {
    }

    /**
     * @return Collection<int, BonusTriggerSession>
     */
    public function sessionsByBonusId(int $bonusId, ?string $orderBy = null): Collection
    {
        return $this->bonusTriggerSessionRepository->getSessionsByBonusId($bonusId, $orderBy, 'asc');
    }

    public function createByBonusIdAndDates(int $bonusId, null|int|string $startAt = null, null|int|string $endAt = null): BonusTriggerSession
    {
        $session = new BonusTriggerSession();
        $session->bonus_id = $bonusId;

        if ($startAt) {
            $session->start_at = Carbon::createFromTimestamp($startAt);
        }
        if ($endAt) {
            $session->end_at = Carbon::createFromTimestamp($endAt);
        }

        $this->bonusTriggerSessionRepository->save($session);

        return $session;
    }

    /**
     * @param int[] $ids
     */
    public function remove(array $ids): int
    {
        return BonusTriggerSession::query()->whereIn('id', $ids)->delete();
    }

    /**
     * @param Collection<int, BonusTriggerSession> $sessions
     */
    public function getTriggerSessionsByDates(Collection $sessions, int|string $startAt, int|string $endAt): ?BonusTriggerSession
    {
        /** @var null|BonusTriggerSession */
        return $sessions
            ->where('start_at', Carbon::createFromTimestamp($startAt))
            ->where('end_at', Carbon::createFromTimestamp($endAt))
            ->first();
    }

    public function getBonusTriggerSessionByBonusId(int $bonusId, string $orderBy = 'id', string $direction = 'desc'): ?BonusTriggerSession
    {
        /** @var null|BonusTriggerSession */
        return $this->bonusTriggerSessionRepository
            ->getSessionsByBonusId($bonusId, $orderBy, $direction)
            ->first();
    }
}
