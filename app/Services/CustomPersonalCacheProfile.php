<?php

namespace App\Services;

use Illuminate\Http\Request;
use Spatie\ResponseCache\CacheProfiles\CacheAllSuccessfulGetRequests;

final class CustomPersonalCacheProfile extends CacheAllSuccessfulGetRequests
{
    public function shouldCacheRequest(Request $request): bool
    {
        if ($request->ajax() && $request->isMethod('get')) {
            return true;
        }
        return parent::shouldCacheRequest($request);
    }

    public function useCacheNameSuffix(Request $request): string
    {
        return "";
    }
}
