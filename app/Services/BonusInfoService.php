<?php

namespace App\Services;

use App\Enums\BonusPlayerStatusEnum;
use App\Enums\BonusTypeEnum;
use App\Models\BonusInfo;
use App\Models\BonusPlayer;
use App\Models\BonusPlayerCard;
use App\Repositories\BonusInfoRepository;
use App\Services\Dto\ActiveBonusesInfoDTO;
use App\Services\Dto\BonusInfoCreateDTO;
use App\Services\Dto\BonusInfoSearchDTO;
use App\Services\Dto\BonusInfoUpdateDTO;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\Cache;

/**
 * Class BonusInfoService
 */
class BonusInfoService
{
    private const IS_BONUS_FOR_SMARTICO_CACHE_PREFIX = 'is_bonus_for_smartico:';

    public function __construct(
        private readonly BonusInfoRepository $bonusInfoRepository,
        private readonly PlayerService $playerService,
    ) {
    }

    public function list(BonusInfoSearchDTO $dto): LengthAwarePaginator
    {
        return $this->bonusInfoRepository->searchBonusesInfo($dto);
    }

    public function getBonusInfoById(int $id): ?BonusInfo
    {
        return $this->bonusInfoRepository->getBonusInfoById($id);
    }

    /**
     * @return Collection<int, BonusInfo>
     */
    public function getActiveBonusesInfoData(): Collection
    {
        return $this->bonusInfoRepository->getActiveBonusesInfoData();
    }

    /**
     * @return Collection<int, BonusInfo>
     */
    public function getActiveBonusesInfoCards(ActiveBonusesInfoDTO $dto): Collection
    {
        if ($dto->welcomeBonus && !is_numeric($dto->welcomeBonus)) {
            $dto->welcomeBonus = null;
        } elseif ($dto->welcomeBonus && is_numeric($dto->welcomeBonus)) {
            /** @var BonusService $bonusService */
            $bonusService = app(BonusService::class);
            $bonus = $bonusService->getActiveWelcomeBonus((int)$dto->welcomeBonus, $dto->currency);
            if (!$bonus) {
                $dto->welcomeBonus = null;
            }
        }

        return $this->bonusInfoRepository->getEnabledBonusesInfo($dto);
    }

    /**
     * @return Collection<int, BonusInfo>
     */
    public function getActiveBonuses(ActiveBonusesInfoDTO $dto, ?string $playerUUID = null): Collection
    {
        $bonusesInfo = $this->bonusInfoRepository->getActiveBonuses($dto, $playerUUID);

        if (!$dto->playerId) {
            return $bonusesInfo;
        }

        $playerHasDeposit = $this->playerService->hasPlayerDeposits($dto->playerId);

        $bonusesByIdKey = $bonusesInfo->pluck('bonus.id')->filter()->unique()->toArray();
        /** @var SupportCollection<string, BonusPlayer> $bonusPlayers */
        $bonusPlayers = BonusPlayer::query()->getQuery()
            ->select(['bonus_id', 'trigger_session_id', 'deposits_count', 'status'])
            ->where('player_id', $dto->playerId)
            ->whereIn('bonus_id', $bonusesByIdKey)
            ->orderBy('id')
            ->get()->keyBy(
                static fn($bonusPlayer) => sprintf('%s-%s', $bonusPlayer->bonus_id, $bonusPlayer->trigger_session_id)
            );

        return $bonusesInfo->filter(function (BonusInfo $bonusInfo) use ($dto, $bonusPlayers, $playerHasDeposit) {
            $playerData = $this->getPlayerData($bonusInfo, $bonusPlayers, $dto->playerId, $playerHasDeposit);

            if (
                !in_array(
                    $playerData['status'],
                    [BonusPlayerStatusEnum::ACTIVATED->value, BonusPlayerStatusEnum::EXPIRED->value],
                    true
                )
            ) {
                $bonusInfo->setAttribute('player_data', $playerData);

                return true;
            }

            return false;
        });
    }

    public function create(BonusInfoCreateDTO $dto): BonusInfo
    {
        $bonusInfo = new BonusInfo();
        $bonusInfo->fill($dto->toArray(false));
        $this->bonusInfoRepository->save($bonusInfo);

        return $bonusInfo;
    }

    public function update(BonusInfoUpdateDTO $dto): BonusInfo
    {
        /** @var BonusInfo $bonusInfo */
        $bonusInfo = $this->getBonusInfoById($dto->id);
        $bonusInfo->fill($dto->toArray(false));
        $this->bonusInfoRepository->save($bonusInfo);

        return $bonusInfo;
    }

    public function isBonusForSmartico(int $bonusId): bool
    {
        return Cache::remember(
            self::IS_BONUS_FOR_SMARTICO_CACHE_PREFIX . $bonusId,
            Carbon::now()->addMinutes(15),
            fn(): bool => $this->bonusInfoRepository->isBonusForSmartico($bonusId)
        );
    }

    /**
     * @param SupportCollection<string, BonusPlayer> $bonusPlayers
     * @return array<string, null|int|string>
     */
    protected function getPlayerData(BonusInfo $bonusInfo, SupportCollection $bonusPlayers, ?int $playerId, bool $playerHasDeposit = false): array
    {
        $bonus = $bonusInfo->bonus;
        $currentTriggerSession = $bonus?->getCurrentTriggerSession();

        /** @var null|BonusPlayer $bonusPlayer */
        $bonusPlayer = $bonusPlayers->get(sprintf('%s-%s', $bonus?->id, $currentTriggerSession?->id));

        return [
            'deposits_count' => $bonusPlayer?->deposits_count,
            'status' => $this->getBonusPlayerStatus($bonusInfo, $playerId, $bonusPlayer?->status, $playerHasDeposit),
        ];
    }

    protected function getBonusPlayerStatus(
        BonusInfo $bonusInfo,
        ?int $playerId = null,
        ?string $status = null,
        bool $isPlayerHasDeposits = false,
    ): ?string {
        $bonus = $bonusInfo->bonus;
        if (!$bonus || !$playerId) {
            return $bonusInfo->general_visible_from <= Carbon::now()
                ? null
                : BonusPlayerStatusEnum::COMING_SOON->value;
        }

        if ($status && $status !== BonusPlayerStatusEnum::EXPIRED->value && $bonusInfo->is_for_smartico) {
            if ($status === BonusPlayerStatusEnum::WAITING_FOR_DEPOSIT->value) {
                return $status;
            }

            $existCards = BonusPlayerCard::query()->where('bonus_info_id', $bonusInfo->id)
                ->where('player_id', $playerId)
                ->where('is_used', false)->exists();

            if ($existCards) {
                return null;
            }
        }

        if (!$status && $bonusInfo->general_visible_from > Carbon::now()) {
            $status = BonusPlayerStatusEnum::COMING_SOON->value;
        } elseif (!$status && $bonusInfo->general_visible_to <= Carbon::now()) {
            $status = BonusPlayerStatusEnum::EXPIRED->value;
        } elseif (
            !$status
            && $isPlayerHasDeposits
            && in_array($bonus->type, [BonusTypeEnum::WELCOME_4_STEPS->value, BonusTypeEnum::WELCOME->value], true)
        ) {
            $status = BonusPlayerStatusEnum::EXPIRED->value;
        } elseif ($status === BonusPlayerStatusEnum::UNSELECTED->value) {
            $status = null;
        }

        return $status;
    }
}
