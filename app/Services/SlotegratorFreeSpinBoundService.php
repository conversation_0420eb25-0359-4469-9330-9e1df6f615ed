<?php

namespace App\Services;

use App\Enums\BonusTypeEnum;
use App\Events\FreeSpinCreateEvent;
use App\Models\FreeSpin;
use Carbon\Carbon;
use Exception;

class SlotegratorFreeSpinBoundService
{
    public function __construct(
        private readonly BonusService $bonusService
    ) {}

    /**
     * @throws Exception
     */
    public function attach(FreeSpin $freeSpin, int $playerId): bool
    {
        $bonus = $this->bonusService->getBonusById($freeSpin->bonus_id);
        if (
            in_array($bonus->type, [BonusTypeEnum::FREE_SPINS_FOR_DEPOSIT->value, BonusTypeEnum::DEPOSIT->value])
        ) {
            $bonusActiveSession = $this->bonusService->getActiveBonusTriggerSessionByBonusId($freeSpin->bonus_id);

            if (! $bonusActiveSession) {
                throw new Exception('No Active Trigger Session In This Interval');
            }
            $startAt = Carbon::now();
            $expiredAt = Carbon::parse($bonusActiveSession->end_at);

            event(new FreeSpinCreateEvent($freeSpin, [$playerId], $startAt, $expiredAt));

            return true;
        }

        return  false;
    }
}
