<?php

namespace App\Services;

use App\Clients\AppClient;
use App\Enums\BonusBalanceStatusEnum;
use App\Enums\BonusTypeEnum;
use App\Exceptions\ApiException;
use App\Models\Bonus;
use App\Models\IssuedBonus;
use App\Repositories\BonusBalanceRepository;
use App\Services\Dto\BonusBalanceDTO;
use App\Services\Dto\IssueBonusBalanceDTO;
use App\Services\Dto\PlayerDto;
use Exception;

/**
 * Class BonusBalanceService
 */
class BonusBalanceService
{
    public function __construct(
        private readonly BonusBalanceRepository $bonusBalanceRepository,
        private readonly BetbyService $betbyService,
        private readonly BonusPlayerService $bonusPlayerService,
        private readonly AppClient $appClient,
    ) {
    }

    /**
     * @param array<string, int|string|bool> $searchParams
     * @param array<string, string> $data
     */
    public function updateBonusBalance(array $searchParams, array $data): int
    {
        return $this->bonusBalanceRepository->updateBonusBalance($searchParams, $data);
    }

    /**
     * @throws ApiException
     * @throws Exception
     */
    public function createBalanceFromBonus(Bonus $bonus, PlayerDto $player, int $initAmount, int $relatedDepositsCount = 0): IssuedBonus
    {
        $maxBonus = $bonus->bonuses[$relatedDepositsCount];
        $maxTransfer = $bonus->max_transfers[$relatedDepositsCount];
        $bonusFactor = $bonus->deposit_factors[$relatedDepositsCount] ?? null;

        if ($bonusFactor !== null) {
            $bonusAmount = min($initAmount * $bonusFactor, $maxBonus);
        } elseif (in_array($bonus->type, [BonusTypeEnum::FREE_MONEY->value, BonusTypeEnum::FREE_BET->value], true)) {
            $bonusAmount = min($initAmount, $maxBonus);
        } else {
            $bonusAmount = $maxBonus;
        }

        $issuedBonus = $this->prepareIssuedBonusModel($bonus, $player, $bonusAmount, $maxTransfer);
        $this->bonusBalanceRepository->save($issuedBonus);

        $dto = $this->getIssueBonusBalanceDto($issuedBonus);
        $dto->playerUuid = $player->uuid;

        $response = $this->appClient->issueBonus($dto);

        if (!isset($response['id'])) {
            throw new ApiException('Wrong bonus balance response.');
        }

        /** @var BonusBalanceDTO $dto */
        $dto = BonusBalanceDTO::fromArray([
            'id' => $response['id'],
            'player_id' => $issuedBonus->player_id,
            'bonus_id' => $issuedBonus->bonus_id,
        ]);
        /** @var PromoCodeService $promoCodeService */
        $promoCodeService = app(PromoCodeService::class);
        $promoCodeService->attachBonusBalance($dto);

        $this->bonusPlayerService->clearPlayerCache($player->uuid);

        return $issuedBonus;
    }

    /**
     * @throws ApiException
     * @throws Exception
     */
    public function createBalanceFromFreeSpin(int $bonusId, int $playerId, int $initAmount, int $relatedDepositsCount = 0): IssuedBonus
    {
        /** @var PlayerService $playerService */
        $playerService = app(PlayerService::class);
        $player = $playerService->getPlayerById($playerId);

        /** @var BonusService $bonusService */
        $bonusService = app(BonusService::class);
        $bonus = $bonusService->getBonusById($bonusId);

        $maxTransfer = $bonus->max_transfers[$relatedDepositsCount];

        $issuedBonus = $this->prepareIssuedBonusModel($bonus, $player, $initAmount, $maxTransfer);
        $this->bonusBalanceRepository->save($issuedBonus);

        $dto = $this->getIssueBonusBalanceDto($issuedBonus);
        $dto->playerUuid = $player->uuid;

        if ($initAmount !== 0) {
            $response = $this->appClient->issueBonus($dto);

            if (!isset($response['id'])) {
                throw new ApiException('Wrong bonus balance response.');
            }
        }

        return $issuedBonus;
    }

    public function getIssueBonusBalanceDto(IssuedBonus $issuedBonus): IssueBonusBalanceDTO
    {
        return IssueBonusBalanceDTO::fromArray([
            'id' => $issuedBonus->id,
            'bonus_id' => $issuedBonus->bonus_id,
            'player_id' => $issuedBonus->player_id,
            'type' => $issuedBonus->type,
            'balance' => $issuedBonus->balance,
            'bonus_external_id' => $issuedBonus->bonus_external_id,
            'status' => $issuedBonus->status,
            'orig_bonus' => $issuedBonus->orig_bonus,
            'orig_wager' => $issuedBonus->orig_wager,
            'wager' => $issuedBonus->wager,
            'min_factor' => $issuedBonus->min_factor,
            'min_bet' => $issuedBonus->min_bet,
            'in_game' => $issuedBonus->in_game,
            'currency' => $issuedBonus->currency,
            'active' => $issuedBonus->active,
            'casino' => $issuedBonus->casino,
            'bets' => $issuedBonus->bets,
            'transfer' => $issuedBonus->transfer,
            'expire_at' => $issuedBonus->expire_at,
        ]);
    }

    /**
     * @throws Exception
     */
    public function prepareIssuedBonusModel(Bonus $bonus, PlayerDto $player, int $bonusAmount, int $maxTransfer): IssuedBonus
    {
        $issuedBonus = new IssuedBonus();
        $issuedBonus->bonus_id = $bonus->id;
        $issuedBonus->player_id = $player->id;
        $issuedBonus->type = $bonus->type;
        $issuedBonus->balance = $bonusAmount;
        $issuedBonus->bonus_external_id = $bonus->data['external_id'] ?? null;
        $issuedBonus->status = BonusBalanceStatusEnum::PENDING->value;
        $issuedBonus->orig_bonus = $bonusAmount;
        $issuedBonus->orig_wager = (int)($bonusAmount * $bonus->wager);
        $issuedBonus->wager = $issuedBonus->orig_wager;
        $issuedBonus->min_factor = $bonus->min_factor;
        $issuedBonus->min_bet = $bonus->min_bet;
        $issuedBonus->in_game = 0;
        $issuedBonus->currency = $bonus->currency;
        $issuedBonus->active = true;
        $issuedBonus->casino = $bonus->casino;
        $issuedBonus->bets = $bonus->bets;
        $issuedBonus->transfer = ($bonus->is_external || $bonusAmount === 0 || $bonus->type === BonusTypeEnum::FREESPIN_BONUS->value)
            ? $maxTransfer : min($bonusAmount, $maxTransfer);

        if ($bonus->duration) {
            $issuedBonus->expire_at
                = in_array($bonus->type, [BonusTypeEnum::FREE_MONEY->value, BonusTypeEnum::FREE_BET->value], true)
                ? $bonus->active_til : now()->addSeconds(+$bonus->duration);
        }

        if ($bonus->is_external) {
            $betbyBonus = $this->betbyService->sendBetbyRequest($issuedBonus, $player->uuid);
            $issuedBonus->bonus_external_id = reset($betbyBonus)['id'];
        }

        return $issuedBonus;
    }
}
