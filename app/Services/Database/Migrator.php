<?php

declare(strict_types=1);

namespace App\Services\Database;

use Illuminate\Database\Migrations\Migrator as BaseMigrator;

class Migrator extends BaseMigrator
{
    /**
     * Set the database connection name for the migrator.
     *
     * @param  string|null $name
     */
    public function usingConnection($name, callable $callback)
    {
        $previousConnection = $this->resolver->getDefaultConnection();

        $this->setConnection($name ?? config('database.migrations_connection'));

        return tap($callback(), function () use ($previousConnection) {
            $this->setConnection($previousConnection);
        });
    }
}
