<?php

namespace App\Services;

use App\Enums\BonusPlayerStatusEnum;
use App\Exceptions\InvalidArgumentException;
use App\Models\Bonus;
use App\Services\Dto\DepositSelectedBonusDTO;
use App\Services\Dto\PlayerDto;
use App\Models\IssuedBonus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;
use Throwable;

class BonusApplyService
{
    public function __construct(
        private readonly BonusPlayerService $bonusPlayerService,
        private readonly DepositService $depositService,
        private readonly BonusPlayerCardService $bonusPlayerCardService,
        private readonly BonusBalanceService $bonusBalanceService,
    ) {
    }

    /**
     * @throws InvalidArgumentException
     */
    public function applyWelcomeBonusForPlayer(Bonus $bonus, PlayerDto $player): void
    {
        $bonusPlayer = $this->bonusPlayerService->getBonusPivotPlayerData($bonus, $player->id);

        if (!$bonusPlayer || $bonusPlayer->status !== BonusPlayerStatusEnum::UNSELECTED->value) {
            $depositCount = $this->depositService->getDepositCount($player->id, $bonus->currency);

            Log::debug('Counter players deposits', [
                'previous_deposits' => $depositCount,
            ]);

            if ($depositCount > 0) {
                throw new InvalidArgumentException('player', $player->uuid, [
                    'previous_deposits' => $depositCount,
                    'player' => $player->uuid ?? $player->id,
                    'bonus_id' => $bonus->id,
                ]);
            }
        }

        if (
            $bonus->currency !== $player->currency
            || !$bonus->active
            || !$bonus->isValidTimeBonus()
            || $this->bonusPlayerService->isBonusUsed($player->id, $bonus->id)
        ) {
            throw new InvalidArgumentException('bonusId', $bonus->id, [
                'bonus_id' => $bonus->id,
                'bonus_type' => $bonus->type,
                'data' => $bonus->data,
                'bonus_currency' => $bonus->currency,
                'player_currency' => $player->currency,
                'is_active' => $bonus->active,
                'valid' => $bonus->isValidTimeBonus(),
            ]);
        }

        $this->bonusPlayerService->selectActiveBonus($player, $bonus);
    }

    /**
     * @throws InvalidArgumentException
     */
    public function applyDepositBonusForPlayer(Bonus $bonus, PlayerDto $player): void
    {
        if (
            $bonus->currency !== $player->currency
            || !$bonus->active
            || !$bonus->getCurrentTriggerSession()
            || $this->bonusPlayerService->isBonusUsed($player->id, $bonus->id, optional($bonus->getCurrentTriggerSession())->id)
        ) {
            throw new InvalidArgumentException('bonusId', $bonus->id, [
                'bonus_id' => $bonus->id,
                'bonus_type' => $bonus->type,
                'data' => $bonus->data,
                'bonus_currency' => $bonus->currency,
                'player_currency' => $player->currency,
                'valid' => $bonus->isValidTimeBonus(),
                'has_active_trigger_session' => $bonus->getCurrentTriggerSession() !== null,
            ]);
        }

        $this->bonusPlayerService->selectActiveBonus($player, $bonus);
    }

    /**
     * @throws InvalidArgumentException
     */
    public function applyFreeSpinForDepositBonusForPlayer(Bonus $bonus, PlayerDto $player): void
    {
        if (
            $bonus->currency !== $player->currency
            || !$bonus->active
            || !$bonus->getCurrentTriggerSession()
            || !$this->bonusPlayerCardService->hasValidBonusCard($player->id, $bonus->id)
            || $this->bonusPlayerService->isBonusUsed($player->id, $bonus->id)
        ) {
            throw new InvalidArgumentException('bonusId', $bonus->id, [
                'bonus_id' => $bonus->id,
                'bonus_type' => $bonus->type,
                'data' => $bonus->data,
                'bonus_currency' => $bonus->currency,
                'player_currency' => $player->currency,
                'is_active' => $bonus->active,
                'valid' => $bonus->isValidTimeBonus(),
                'has_active_trigger_session' => $bonus->getCurrentTriggerSession() !== null,
                'has_bonus_card' => $this->bonusPlayerCardService->hasValidBonusCard($player->id, $bonus->id),
            ]);
        }

        $this->bonusPlayerService->selectActiveBonus($player, $bonus);
    }

    /**
     * @throws InvalidArgumentException|Exception
     */
    public function applyOnetimeForPlayer(Bonus $bonus, PlayerDto $player, ?int $amount = null): IssuedBonus
    {
        if (
            !$bonus->is_onetime_group
            || !$bonus->active
            || !$bonus->isValidTimeBonus()
            || $bonus->currency !== $player->currency
        ) {
            throw new InvalidArgumentException('bonusId', $bonus->id, [
                'bonus_id' => $bonus->id,
                'is_onetime' => $bonus->is_onetime_group,
                'is_active' => $bonus->active,
                'is_valid_time' => $bonus->isValidTimeBonus(),
                'currency_match' => $bonus->currency === $player->currency,
            ]);
        }

        $transferAmount = $amount ? $amount * 100 : $bonus->bonuses[0];

        return $this->bonusBalanceService->createBalanceFromBonus($bonus, $player, $transferAmount);
    }

    /**
     * @throws InvalidArgumentException|Exception
     */
    public function applyNoDepForPlayer(Bonus $bonus, PlayerDto $player): void
    {
        if (
            !$bonus->is_no_dep
            || !$bonus->active
            || !$bonus->isValidTimeBonus()
            || $bonus->currency !== $player->currency
        ) {
            throw new InvalidArgumentException('bonusId', $bonus->id, [
                'bonus_id' => $bonus->id,
                'is_no_dep' => $bonus->is_no_dep,
                'is_active' => $bonus->active,
                'is_valid_time' => $bonus->isValidTimeBonus(),
                'currency_match' => $bonus->currency === $player->currency,
            ]);
        }

        $this->bonusBalanceService->createBalanceFromBonus($bonus, $player, $bonus->bonuses[0]);
    }

    /**
     * @throws Exception
     */
    public function depositSelectedBonus(DepositSelectedBonusDTO $dto): bool
    {
        $bonus = $this->bonusPlayerService->getSelectedBonus((int)$dto->playerId);

        if (!$this->isValidBonus($bonus, $dto)) {
            return false;
        }

        $depositPosition = $this->getDepositPosition($bonus, $dto);

        if ($this->isBonusLimitExceed($bonus, $dto, $depositPosition)) {
            return false;
        }

        $this->processActivateSelectedBonus($bonus, $dto, $depositPosition);

        return true;
    }

    protected function isValidBonus(?Bonus $bonus, DepositSelectedBonusDTO $dto): bool
    {
        if ($bonus === null) {
            return false;
        }

        if (!$bonus->active || $bonus->currency !== $dto->playerCurrency) {
            Log::info('Bonus not valid', [
                'bonus_id' => $bonus->id,
                'player_id' => $dto->playerId,
            ]);

            return false;
        }

        if ($bonus->min_deposit && $bonus->min_deposit > $dto->amount) {
            Log::warning('Min deposit reqs were not met', [
                'bonus_id' => $bonus->id,
                'player_id' => $dto->playerId,
            ]);

            return false;
        }

        return true;
    }

    protected function getDepositPosition(Bonus $bonus, DepositSelectedBonusDTO $dto): int
    {
        if (!$bonus->is_welcome_group) {
            return 0;
        }

        return $bonus->is_welcome_4_steps
            ? $this->bonusPlayerService->getSelectedBonusPlayerData((int)$dto->playerId)->deposits_count
            : $dto->depositsCount;
    }

    protected function isBonusLimitExceed(Bonus $bonus, DepositSelectedBonusDTO $dto, int $depositPosition): bool
    {
        if (!isset($bonus->deposit_factors[$depositPosition])) {
            Log::warning('Bonus limit exceeded', [
                'bonus_id' => $bonus->id,
                'player_id' => $dto->playerId,
            ]);

            return true;
        }

        return false;
    }

    /**
     * @throws Exception
     */
    protected function processActivateSelectedBonus(Bonus $bonus, DepositSelectedBonusDTO $dto, int $depositPosition): void
    {
        /** @var FreeSpinBoundService $freeSpinBoundService */
        $freeSpinBoundService = app(FreeSpinBoundService::class);

        DB::beginTransaction();

        $playerId = (int)$dto->playerId;

        try {
            $isAttachedFreeSpinToPlayer = false;

            if ($bonus->is_free_spin_for_deposit) {
                $isAttachedFreeSpinToPlayer = $freeSpinBoundService->attachFreeSpinToPlayer($bonus->id, $playerId);
                $this->bonusPlayerService->activateSelectedBonus($playerId, $bonus->id);
            } else {
                /** @var PlayerService $playerService */
                $playerService = app(PlayerService::class);
                $player = $playerService->getPlayerByID($playerId);
                $this->bonusPlayerService->activateSelectedBonus($playerId, $bonus->id);
                $this->bonusBalanceService->createBalanceFromBonus($bonus, $player, $dto->amount, $depositPosition);
            }


            DB::commit();
        } catch (Throwable $e) {
            if ($bonus->is_free_spin_for_deposit && $isAttachedFreeSpinToPlayer) {
                $freeSpinBoundService->cancelFreeSpinBound($bonus->id, $playerId);
            }

            DB::rollBack();

            throw new Exception($e->getMessage(), $e->getCode());
        }
    }
}
