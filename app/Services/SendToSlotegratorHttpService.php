<?php

declare(strict_types=1);

namespace App\Services;

use Exception;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

/**
 * Class SendToSlotegratorHttpService
 */
class SendToSlotegratorHttpService
{
    /**
     * @param array<string, int|list<array<string, string>>> $data
     * @throws Exception
     */
    public function makePostRequestToSlotEgrator(string $uri, string $accessToken, array $data): Response
    {
        $response = Http::withHeaders([
            'AccessToken' => $accessToken,
            'Accept' => 'application/json',
        ])->timeout(15)->baseUrl(config('app.slotegrator_host'))
            ->withBody(json_encode($data))
            ->post($uri);

        if ($response->failed()) {
            throw new Exception('Server error , please try later !');
        }

        return $response;
    }
}
