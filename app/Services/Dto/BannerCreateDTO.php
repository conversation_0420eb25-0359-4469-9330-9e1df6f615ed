<?php

declare(strict_types=1);

namespace App\Services\Dto;

use App\Dto\BaseDTO;
use Carbon\Carbon;

class BannerCreateDTO extends BaseDTO
{
    public int $client_id = 2;
    public string $name;
    public ?string $language;
    public ?string $country = null;

    /**
     * @uses $mobileApkInstall
     */
    public bool $mobileApkInstall;
    public ?string $platform;

    /**
     * @uses $isForSignedIn
     */
    public string $isForSignedIn;
    public ?int $weight = 0;
    public ?string $location = null;
    /**
     * @var int[]
     */
    public array $disposition = [];
    public ?string $image = null;
    public string $type;
    public bool $enabled;

    /**
     * @uses $smarticoSegmentId
     */
    public ?string $smarticoSegmentId;
    public ?int $startAt = null;
    /**
     * @uses $endAt
     */
    public ?int $endAt = null;

    /**
     * @return array<string, null|array<null|float|int|string>|bool|Carbon|float|int|string>
     */
    public function getData(bool $isCamelCase = true): array
    {
        $data = $this->toArray($isCamelCase);
        if ($isCamelCase) {
            if ($data['startAt']) {
                $data['startAt'] = Carbon::createFromTimestamp($data['startAt']);
            }
            if ($data['endAt']) {
                $data['endAt'] = Carbon::createFromTimestamp($data['endAt']);
            }
        } else {
            if ($data['start_at']) {
                $data['start_at'] = Carbon::createFromTimestamp($data['start_at']);
            }
            if ($data['end_at']) {
                $data['end_at'] = Carbon::createFromTimestamp($data['end_at']);
            }
        }
        return $data;
    }
}
