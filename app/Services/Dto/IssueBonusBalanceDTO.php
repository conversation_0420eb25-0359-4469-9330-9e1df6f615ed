<?php

declare(strict_types=1);

namespace App\Services\Dto;

use App\Dto\BaseDTO;
use DateTimeInterface;

class IssueBonusBalanceDTO extends BaseDTO
{
    public int $id;
    public int $bonusId;
    public int $playerId;
    public ?string $playerUuid;
    public string $type;
    public float $balance;
    public ?string $bonusExternalId;
    public string $status;
    public float $origBonus;
    public int $origWager;
    public int $wager;
    public ?float $minFactor;
    public float $minBet;
    public int $inGame;
    public string $currency;
    public bool $active;
    public bool $casino;
    public bool $bets;
    public int $transfer;
    public ?DateTimeInterface $expireAt;
}
