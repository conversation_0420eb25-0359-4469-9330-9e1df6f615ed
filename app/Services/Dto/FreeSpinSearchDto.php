<?php

declare(strict_types=1);

namespace App\Services\Dto;

class FreeSpinSearchDto extends PaginationDto
{
    public ?int $option = null;

    public ?int $id = null;

    public ?string $type = null;

    public ?string $name = null;

    public ?string $title = null;

    public ?int $playerIdWith = null;

    public ?string $currency = null;

    public ?string $supplier = null;

    public ?string $aggregator = null;

    public ?int $providerId = null;

    public ?int $slotId = null;

    public ?int $bonusId = null;

    public ?int $updatedByAuthorId = null;

    public ?int $authorId = null;

    public ?int $countFrom = null;

    public ?int $countTo = null;

    public ?int $betFrom = null;

    public ?int $betTo = null;

    public ?string $startAtFrom = null;

    public ?string $startAtTo = null;

    public ?string $expiredAtFrom = null;

    public ?string $expiredAtTo = null;

    public ?string $createdAtFrom = null;

    public ?string $createdAtTo = null;

    public ?string $updatedAtFrom = null;

    public ?string $updatedAtTo = null;
}
