<?php

declare(strict_types=1);

namespace App\Services\Dto;

use App\Dto\BaseDTO;
use Illuminate\Support\Str;

class FreeSpinCrudDto extends BaseDTO
{
    public ?int $id = null;

    public string $supplier;

    public ?string $aggregator;

    public int $slotsId;

    public ?string $slotExternalId = null;

    public int $providerId;

    public string $name;

    public string $title;

    public int $count;

    public ?int $bet;

    public string $currency;

    public ?string $betId;

    public ?int $authorId;
    public ?string $authorEmail;

    public ?float $denomination;

    public ?string $startAt = null;

    public ?string $expiredAt = null;

    /**
     * @var null|array<array<string, string>>
     */
    public ?array $players = null;

    public bool $bonusBalance;

    public ?float $wager;

    public ?int $maxTransfer;

    public ?int $minBet;

    public ?int $duration;
    public ?int $freeSpinDuration;

    public string $status;

    public ?bool $isActive;

    public ?int $updatedByAuthorId;

    public ?string $inoutGameName;

    public ?int $minesCount;

    public ?string $difficulty;

    public ?int $bonusGenreId;

    /**
     * @var null|array<int>
     */
    public ?array $bonusSlotProviders;

    /**
     * @var null|array<int>
     */
    public ?array $bonusSlots;

    public bool $autoCancelWithdrawal;

    /**
     * @return array<string, null|array<string, string>|float|int|string>
     */
    public function getFreeSpinData(bool $withId = false): array
    {
        $data = [
            'supplier' => $this->supplier,
            'aggregator' => $this->aggregator,
            'slot_id' => $this->slotsId,
            'slot_external_id' => $this->slotExternalId,
            'provider_id' => $this->providerId,
            'name' => $this->name,
            'title' => $this->title,
            'count' => $this->count,
            'bet' => $this->bet,
            'bet_id' => $this->betId,
            'denomination' => $this->denomination,
            'currency' => $this->currency,
            'start_at' => $this->startAt,
            'expired_at' => $this->expiredAt,
            'duration' => $this->freeSpinDuration,
            'author_email' => $this->authorEmail,
            'auto_cancel_withdrawal' => $this->autoCancelWithdrawal,
        ];
        if ($withId) {
            $data['id'] = $this->id;
        }
        if ($this->isActive === null) {
            $data['is_active'] = 1;
        }

        if ($this->authorId) {
            $data['author_id'] = $this->authorId;
        }

        if ($this->updatedByAuthorId) {
            $data['updated_by_author_id'] = $this->updatedByAuthorId;
        }

        if ($this->inoutGameName) {
            $data['options']['inout_game_name'] = $this->inoutGameName;
            if ($this->minesCount) {
                $data['options']['mines_count'] = (string)$this->minesCount;
            }
            if ($this->difficulty) {
                $data['options']['difficulty'] = Str::upper($this->difficulty);
            }
        } else {
            $data['options'] = null;
        }

        return $data;
    }
}
