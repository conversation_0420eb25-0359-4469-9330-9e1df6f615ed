<?php

namespace App\Services\Dto;

use App\Dto\BaseDTO;

/**
 * @property string $name
 * @property string $code
 * @property null|int $streamId
 * @property int $bonusId
 * @property null|string $description
 * @property null|bool $active
 * @property null|int $limit
 * @property null|string $startAt
 * @property null|string $endAt
 * @property null|array<string, string> $condition
 * @property null|bool $isAlanbase
 */
class PromoCodeDTO extends BaseDTO
{
    public ?int $id;

    public string $name;

    public string $code;

    public ?int $streamId;

    public ?int $bonusId;

    public ?int $playerId;

    public ?string $description;

    public ?bool $active;

    public ?int $limit;

    public ?int $startAt;

    public ?int $endAt;

    /**
     * @var null|array<string, string>
     */
    public ?array $condition;

    public ?bool $isAlanbase;
}
