<?php

declare(strict_types=1);

namespace App\Services\Dto;

use App\Dto\BaseDTO;
use Carbon\Carbon;

class GamicorpFreeSpinCreateDto extends BaseDTO
{
    public string $token;
    /**
     * @uses $providerId
     */
    public int $providerId;
    public string $freebetId;
    /**
     * @uses $userLabel
     */
    public string $userLabel;
    /**
     * @var string[]
     *
     * @uses $gameUuids
     */
    public array $gameUuids;
    /**
     * @uses $freeRoundsCount
     */
    public int $freeRoundsCount;
    /**
     * @uses $betAmount
     */
    public ?string $betAmount;
    /**
     * @uses $betLevel
     */
    public ?int $betLevel;
    public string $currency;
    /**
     * @uses $expireDate
     */
    public string $expireDate;
    /**
     * @uses $expirationDuration
     */
    public ?int $expirationDuration = null;
    /**
     * @var array<string, null|string|Carbon>
     */
    public array $user;
    /**
     * @var null|array<string, string>
     */
    public ?array $options;
}
