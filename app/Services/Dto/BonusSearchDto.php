<?php

declare(strict_types=1);

namespace App\Services\Dto;

/**
 * Class BonusSearchDto
 *
 * @property int $id
 * @property string $name
 * @property string $currency
 * @property bool $casino
 * @property bool $bets
 * @property string $type
 * @property string $slot_provider_id
 * @property string $slot_id
 * @property bool $active
 * @property bool $isOrganic
 * @property null|string $genreId
 * @property null|string $productType
 */
class BonusSearchDto extends PaginationDto
{
    public ?string $currency;

    public ?bool $casino;

    public ?bool $bets;

    public ?bool $active;

    public ?string $type;

    public ?string $name;

    public ?int $id;

    public ?string $slot_provider_id;

    public ?string $slot_id;

    public ?bool $isOrganic = false;

    public ?int $externalId;

    public ?string $genreId;

    public ?string $productType;
}
