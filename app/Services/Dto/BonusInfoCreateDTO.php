<?php

namespace App\Services\Dto;

use App\Dto\BaseDTO;
use Carbon\Carbon;

class BonusInfoCreateDTO extends BaseDTO
{
    public string $name;
    /**
     * @uses $clientId
     */
    public ?int $clientId = 2;
    public string $currency;
    /**
     * @uses $visibleFrom
     */
    public string $visibleFrom;
    /**
     * @uses $visibleTo
     */
    public string $visibleTo;
    public bool $casino;
    /**
     * @uses $isWelcome
     */
    public bool $isWelcome;
    public ?bool $active;
    public ?string $image;
    /**
     * @var null|array<string, string>
     */
    public ?array $description;
    /**
     * @uses $descriptionInfo
     *
     * @var null|array<string, string>
     */
    public ?array $descriptionInfo;
    /**
     * @var null|array<string, string>
     */
    public ?array $condition;
    /**
     * @uses $conditionTitle
     *
     * @var null|array<string, string>
     */
    public ?array $conditionTitle;
    /**
     * @uses $bonusName
     *
     * @var null|array<string, string>
     */
    public ?array $bonusName;
    /**
     * @uses $sortOrder
     */
    public int $sortOrder;
    /**
     * @uses $proceedLink
     */
    public ?string $proceedLink;
    public ?int $bonusId;
    /**
     * @var null|array<string, string>
     */
    public ?array $colors;
    /**
     * @uses $isForSmartico
     */
    public ?bool $isForSmartico;
    /**
     * @uses $showVisibleDate
     */
    public ?bool $showVisibleDate;
    /**
     * @uses $customType
     */
    public ?string $customType;

    /**
     * @return array<string, null|string|int|bool|Carbon>
     */
    public function toArray(bool $isCamelCase = true): array
    {
        $data = parent::toArray($isCamelCase);

        if ($data['active'] === null) {
            unset($data['active']);
        }

        if ($isCamelCase) {
            if (!$data['clientId']) {
                $data['clientId'] = 2;
            }
            if ($data['isForSmartico'] === null) {
                unset($data['isForSmartico']);
            }
            $data['visibleFrom'] = Carbon::createFromTimestamp($data['visibleFrom']);
            $data['visibleTo'] = Carbon::createFromTimestamp($data['visibleTo']);

            return $data;
        }

        if (!$data['client_id']) {
            $data['client_id'] = 2;
        }

        if ($data['is_for_smartico'] === null) {
            unset($data['is_for_smartico']);
        }

        $data['visible_from'] = Carbon::createFromTimestamp($data['visible_from']);
        $data['visible_to'] = Carbon::createFromTimestamp($data['visible_to']);

        return $data;
    }
}
