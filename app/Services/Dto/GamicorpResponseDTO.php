<?php

namespace App\Services\Dto;

use App\Dto\BaseDTO;

/**
 * @property string $status
 * @property string $error
 * @property array<string, string> $meta
 * @property string $errorMessage
 * @property int $statusCode
 * @property null|int $freespinBoundId
 */
class GamicorpResponseDTO extends BaseDTO
{
    public ?int $freespinBoundId = null;
    public string $status = 'success';
    public string $error = '';
    /**
     * @var array<string, string>
     */
    public array $meta = [];
    public string $errorMessage = '';
    public int $statusCode = 200;
}
