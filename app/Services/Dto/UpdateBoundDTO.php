<?php

declare(strict_types=1);

namespace App\Services\Dto;

class UpdateBoundDTO extends PaginationDto
{
    public int $id;
    public ?int $countLeft;
    public ?int $totalWin;
    public ?bool $isActive;
    public ?bool $isArchived;

    /**
     * @return array<string, int|bool>
     */
    public function getDataForUpdate(): array
    {
        $data = [];

        if ($this->countLeft !== null) {
            $data['count_left'] = $this->countLeft;
        }
        if ($this->totalWin !== null) {
            $data['total_win'] = $this->totalWin;
        }
        if ($this->isActive !== null) {
            $data['is_active'] = $this->isActive;
        }
        if ($this->isArchived !== null) {
            $data['is_archived'] = $this->isArchived;
        }

        return $data;
    }
}
