<?php

declare(strict_types=1);

namespace App\Services\Dto;

use App\Dto\BaseDTO;

class GamicorpFreeSpinBoundCreateDto extends BaseDto
{
    public int $id;
    public string $externalId;
    public int $providerId;
    public ?int $bonusId = null;
    public ?float $denomination = null;
    public int $count;
    public ?int $bet = null;
    public string $currency;
    public ?string $betId = null;
    public ?string $startAt = null;
    public string $expiredAt;
    public ?int $duration = null;
    public int $playerId;
    /**
     * @var null|array<string, string>
     */
    public ?array $options;
}
