<?php

declare(strict_types=1);

namespace App\Services\Dto;

use App\Dto\BaseDTO;
use App\Enums\BonusTypeEnum;
use Carbon\Carbon;

class BonusCrudDto extends BaseDTO
{
    public int $clientId = 2;
    public ?int $id;
    public string $name;

    /**
     * @var null|int[]
     */
    public ?array $maxBonuses = null;

    /**
     * @var null|int[]
     */
    public ?array $maxTransfers = null;

    /**
     * @var null|int[]
     */
    public ?array $depositFactors;

    public ?int $minDeposit;

    public ?int $minBet;

    public string $currency;

    public ?float $wager;

    public bool $active = false;

    public bool $casino = true;

    public bool $bets = true;

    public ?float $minFactor;

    public ?int $duration;

    public ?string $image;

    /**
     * @var null|array<string, string>
     */
    public ?array $description;

    /**
     * @var null|array<string, string>
     */
    public ?array $shortDescription;

    /**
     * @var null|array<string, null|string|int|float>
     */
    public ?array $data;

    public bool $isPromo = false;

    public bool $isExternal = false;

    public ?string $type;

    public int $uses = 0;

    public int $transfers = 0;

    public ?Carbon $activeFrom;

    public ?Carbon $activeTil;

    /**
     * @var null|array<string, string>
     */
    public ?array $condition;

    /**
     * @var null|array<string, string>
     */
    public ?array $bonusName;

    /**
     * @var null|int[]
     */
    public ?array $slotProviders;

    /**
     * @var null|int[]
     */
    public ?array $slots;

    /**
     * @var null|array<array<string, string>>
     */
    public ?array $triggerSessions;

    /**
     * @uses $bonusBalance
     */
    public ?bool $bonusBalance;

    public ?string $authorEmail;

    /**
     * @uses $authorId
     */
    public ?int $authorId;

    public string $status = 'active';

    public ?bool $isActive;

    public ?int $count;

    public ?int $bet;

    /**
     * @uses $betId
     */
    public ?string $betId;

    public ?float $denomination;

    public ?int $maxRealBalance;

    public ?int $weight;
    public bool $isOrganic = true;
    public bool $autoCancelWithdrawal;

    public ?int $genreId;

    public ?int $freeSpinSlotProvider;

    public ?int $freeSpinSlot;

    /**
     * @var null|array<string, string>
     */
    public ?array $colors;

    /**
     * @param array<string, null|string|bool|int|float> $data
     */
    public static function fromArray(array $data): BaseDTO
    {
        $dto = parent::fromArray($data);
        if ($dto->maxTransfers !== null && $dto->maxBonuses === null) {
            $dto->maxBonuses = $dto->maxTransfers;
        }

        if ($data['from'] ?? null) {
            $dto->activeFrom = Carbon::parse($data['from']);
        }
        if ($data['to'] ?? null) {
            $dto->activeTil = Carbon::parse($data['to']);
        }
        if (!($data['deposit_factors'] ?? null) && $data['type'] === BonusTypeEnum::FREE_SPINS_FOR_DEPOSIT->value) {
            $dto->depositFactors = [0];
        }

        return $dto;
    }

    /**
     * @return array<string, null|array<null|float|int|string>|bool|Carbon|float|int|string>
     */
    public function getDataForCreatingBonus(): array
    {
        $data = [
            'client_id' => $this->clientId,
            'author_email' => $this->authorEmail,
            'name' => $this->name,
            'bonus_name' => $this->bonusName,
            'description' => $this->description,
            'short_description' => $this->shortDescription,
            'colors' => $this->colors,
            'condition' => $this->condition,
            'type' => $this->type,
            'bonuses' => $this->maxBonuses,
            'max_transfers' => $this->maxTransfers ?? $this->maxBonuses,
            'transfers' => $this->transfers,
            'deposit_factors' => $this->depositFactors,
            'min_deposit' => $this->minDeposit,
            'currency' => $this->currency,
            'duration' => $this->duration,
            'min_bet' => $this->minBet,
            'min_factor' => $this->minFactor,
            'active' => $this->active,
            'casino' => $this->casino,
            'bets' => $this->bets,
            'active_from' => $this->activeFrom,
            'active_til' => $this->activeTil,
            'is_external' => $this->isExternal,
            'image' => $this->image,
            'weight' => $this->weight,
            'max_real_balance' => $this->maxRealBalance,
            'is_organic' => $this->isOrganic,
            'auto_cancel_withdrawal' => $this->autoCancelWithdrawal,
            'updated_at' => Carbon::now(),
            'genre_id' => $this->genreId,
        ];

        if ($this->wager !== null) {
            $data['wager'] = $this->wager;
        }

        if ($this->data !== null) {
            $data['data'] = $this->data;
        }

        if (!$this->id) {
            $data['is_promo'] = $this->isPromo;
        }

        return $data;
    }
}
