<?php

namespace App\Providers;

use App\Events\BonusCreateOrUpdateEvent;
use App\Events\FreeSpinCreateEvent;
use App\Events\FreeSpinCudEvent;
use App\Events\FreeSpinResendEvent;
use App\Listeners\BonusCreateOrUpdateListener;
use App\Listeners\CreateFreeSpinAndSendToPlayersListener;
use App\Listeners\FreeSpinCudListener;
use App\Listeners\FreeSpinResendToPlayersListener;
use App\Listeners\SqlListener;
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        QueryExecuted::class => [
            SqlListener::class,
        ],
        FreeSpinCreateEvent::class => [
            CreateFreeSpinAndSendToPlayersListener::class,
        ],
        FreeSpinResendEvent::class => [
            FreeSpinResendToPlayersListener::class,
        ],
        BonusCreateOrUpdateEvent::class => [
            BonusCreateOrUpdateListener::class,
        ],
        FreeSpinCudEvent::class => [
            FreeSpinCudListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        Event::listen(QueryExecuted::class, SqlListener::class);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
