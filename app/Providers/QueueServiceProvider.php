<?php

namespace App\Providers;

use App\Queue\RedisConnector;
use Illuminate\Queue\QueueManager;
use Illuminate\Redis\RedisManager;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use JsonException;
use ReflectionClass;
use ReflectionException;
use Throwable;

class QueueServiceProvider extends ServiceProvider
{
    private const JOB_SUCCESS = 'success';
    private const JOB_FAILING = 'failing';

    /**
     * @var array<string, array<string, string|array<string, string>>>
     */
    private static array $queueList = [];

    public function register(): void
    {
        $this->app->extend('queue', function (QueueManager $manager) {
            $redis = $this->app->make(RedisManager::class);

            $manager->addConnector('redis', static fn() => new RedisConnector($redis));

            // logging jobs
            $manager->before(function ($event): void {
                $this->startQueueLogInfo($event);
            });

            $manager->after(function ($event): void {
                $this->finishQueueLogInfo($event, self::JOB_SUCCESS);
            });

            $manager->failing(function ($event): void {
                $this->finishQueueLogInfo($event, self::JOB_FAILING);
            });

            return $manager;
        });
    }

    /**
     * @param mixed $event
     *
     * @throws JsonException|ReflectionException
     */
    protected function startQueueLogInfo($event): void
    {
        $jobReflection = $this->getJobReflection($event);

        $jobName = $jobReflection->getShortName();

        self::$queueList[$jobName]['message'] = "Queue | $jobName | started";

        self::$queueList[$jobName]['context'] = [
            'jobProperties' => $this->getJobProperties($jobReflection),
            'setNewUniqueId' => Str::uuid()->toString(),
        ];
    }

    /**
     * @param mixed $event
     *
     * @throws ReflectionException
     * @throws JsonException
     */
    protected function finishQueueLogInfo($event, string $status): void
    {
        $jobReflection = $this->getJobReflection($event);

        $jobName = $jobReflection->getShortName();

        if ($status === self::JOB_SUCCESS) {
            self::$queueList[$jobName]['message'] .= ' | success completed';
        } elseif ($status === self::JOB_FAILING) {
            self::$queueList[$jobName]['message'] .= " | fail | {$event->exception->getMessage()}";
            self::$queueList[$jobName]['context']['trace'] = $event->exception->getTraceAsString();
        }

        Log::info(self::$queueList[$jobName]['message'], self::$queueList[$jobName]['context']);

        unset(self::$queueList[$jobName]);
    }

    /**
     * @phpstan-ignore-next-line
     */
    protected function getJobReflection($event): ReflectionClass
    {
        $serializedJob = json_decode($event->job->getRawBody(), true, 512, JSON_THROW_ON_ERROR)['data']['command'];
        $job = unserialize($serializedJob, ['allowed_classes' => true]);

        return new ReflectionClass($job);
    }

    /**
     * @phpstan-ignore-next-line
     */
    protected function getJobProperties($jobReflection): array
    {
        $properties = $jobReflection->getProperties();
        $data = [];

        foreach ($properties as $property) {
            $class = $property->getDeclaringClass()->getShortName();
            $propertyName = $property->getName();

            try {
                $data[$class][$propertyName] = $property->getValue($jobReflection);
            } catch (Throwable) {
                $data[$class][$propertyName] = null;
            }
        }

        return $data;
    }
}
