<?php

namespace App\Providers;

use App\Clients\AppClient;
use App\Clients\Requests\RequestBuilder;
use App\Clients\GamicorpClient;
use Deversin\Signature\SignatureInterface;
use GuzzleHttp\Client;
use Guz<PERSON><PERSON>ttp\Handler\CurlHandler;
use GuzzleHttp\HandlerStack;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\ServiceProvider;
use Psr\Http\Message\RequestInterface;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(AppClient::class, function () {
            /** @var RequestBuilder $requestBuilder */
            $requestBuilder = $this->app->make(RequestBuilder::class, [
                'baseUrl' => config('app.main_link_api'),
                'httpVerify' => config('external-services.http_verify'),
            ]);

            return new AppClient($requestBuilder);
        });

        $this->app->bind(GamicorpClient::class, function () {
            /** @var RequestBuilder $requestBuilder */
            $requestBuilder = $this->app->make(RequestBuilder::class, [
                'baseUrl' => config('app.gamicorp_host'),
                'httpVerify' => config('external-services.http_verify'),
            ]);

            return new GamicorpClient($requestBuilder);
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }

    /**
     * @throws BindingResolutionException
     */
    protected function getHttpClientInstance(string $host): Client
    {
        $signatureService = $this->app->make(SignatureInterface::class);

        $stack = new HandlerStack();
        $stack->setHandler(new CurlHandler());

        $stack->push(function ($handler) use ($signatureService) {
            return static function (
                RequestInterface $request,
                array $options
            ) use ($handler, $signatureService) {
                if (! $request->hasHeader(SignatureInterface::SIGNATURE_HEADER)) {
                    // Generating signature
                    $stream = $request->getBody();
                    $body = $stream->getContents();
                    if (empty($body)) {
                        $body = json_encode([], JSON_THROW_ON_ERROR);
                    }
                    $signature = $signatureService->sign($body);

                    $request = $request
                        ->withBody($stream)
                        ->withHeader(SignatureInterface::SIGNATURE_HEADER, $signature);
                }

                return $handler($request, $options);
            };
        });

        return new Client(
            [
                'base_uri' => $host,
                'handler' => $stack,
                'verify' => config('external-services.http_verify'),
            ]
        );
    }
}
