<?php

namespace App\ValueObjects;

use Illuminate\Validation\Rules\Enum;

final class BonusBalanceStatus extends Enum
{
    public const PENDING = 'pending';

    public const DEACTIVATED = 'deactivated';

    public const TRANSFERRED = 'transferred';

    public const BANKRUPT = 'bankrupt';

    public const EXPIRED = 'expired';

    public const WAITING = 'waiting';

    public const CANCELED = 'canceled';
}
