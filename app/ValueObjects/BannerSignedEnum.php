<?php

namespace App\ValueObjects;

interface BannerSignedEnum
{
    public const ALL_USERS = 'all';
    public const AUTHORIZED_USERS = 'authorizedUsers';
    public const UN_AUTHORIZED_USERS = 'unauthorizedUsers';
    public const AUTHORIZED_USERS_WITHOUT_DEPOSIT = 'authorizedUsersWithoutDeposit';
    public const AUTHORIZED_USERS_WITH_DEPOSIT = 'authorizedUsersWithDeposit';

    public const SIGNED_TYPES = [
        self::ALL_USERS,
        self::UN_AUTHORIZED_USERS,
        self::AUTHORIZED_USERS,
        self::AUTHORIZED_USERS_WITHOUT_DEPOSIT,
        self::AUTHORIZED_USERS_WITH_DEPOSIT,
    ];
}
