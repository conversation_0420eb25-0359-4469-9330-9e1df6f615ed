<?php

namespace App\Console;

use App\Console\Commands\DeactivationExpiredBanners;
use App\Console\Commands\DisableExpiredBonusCardsCommand;
use App\Console\Commands\FreeSpinBoundExpirationCheckCommand;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class <PERSON>el extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command(DisableExpiredBonusCardsCommand::class)->everyFiveMinutes()->onOneServer();
        $schedule->command(DeactivationExpiredBanners::class)->everyFiveMinutes()->onOneServer();
        $schedule->command(FreeSpinBoundExpirationCheckCommand::class)->everyFiveMinutes()->onOneServer();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
