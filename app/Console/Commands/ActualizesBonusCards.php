<?php

namespace App\Console\Commands;

use App\Models\BonusPlayerCard;
use Illuminate\Console\Command;

class ActualizesBonusCards extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:actualizes-bonus-cards';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $items = BonusPlayerCard::query()
            ->select('bonus_player_card.*', 'bonus_player.status')
            ->distinct()
            ->leftJoin('bonus_player', static function ($join): void {
                $join->on('bonus_player_card.player_id', '=', 'bonus_player.player_id');
                $join->on('bonus_player_card.bonus_id', '=', 'bonus_player.bonus_id');
            })
            ->where('bonus_player.status', 'activated')
            ->pluck('bonus_player_card.id');

        BonusPlayerCard::query()->whereIn('id', $items)->update(['is_used' => 1]);
    }
}
