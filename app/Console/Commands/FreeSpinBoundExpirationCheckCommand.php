<?php

namespace App\Console\Commands;

use App\Enums\FreeSpinBoundStatusEnum;
use App\Models\FreeSpinBound;
use Illuminate\Console\Command;

class FreeSpinBoundExpirationCheckCommand extends Command
{
    protected $signature = 'app:free-spin-bound-expiration-check';

    protected $description = 'Check if the free spin bound has expired and update status accordingly.';

    public function handle(): void
    {
        FreeSpinBound::query()
            ->where('status', FreeSpinBoundStatusEnum::ACTIVE->value)
            ->where('expire_at', '<', now())
            ->update(['status' => FreeSpinBoundStatusEnum::EXPIRED->value]);
    }
}
