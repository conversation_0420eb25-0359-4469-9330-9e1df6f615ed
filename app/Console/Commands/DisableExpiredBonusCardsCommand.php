<?php

namespace App\Console\Commands;

use App\Enums\BonusTypeEnum;
use App\Models\Bonus;
use App\Models\BonusInfo;
use Illuminate\Console\Command;

class DisableExpiredBonusCardsCommand extends Command
{
    protected $signature = 'app:disable-expired-bonus-cards';

    protected $description = 'Disable expired bonus cards';

    public function handle(): void
    {
        BonusInfo::query()
            ->where('active', true)
            ->where('visible_to', '<', now())
            ->update(['active' => false]);

        BonusInfo::query()
            ->join('bonuses', 'bonus_info.bonus_id', '=', 'bonuses.id')
            ->where('bonuses.active', false)
            ->where('bonus_info.active', true)
            ->update(['bonus_info.active' => false]);

        BonusInfo::query()
            ->join('bonuses', 'bonus_info.bonus_id', '=', 'bonuses.id')
            ->where('bonus_info.active', true)
            ->where('bonuses.active', true)
            ->whereNotNull('bonuses.active_til')
            ->where('bonuses.active_til', '<', now())
            ->update(['bonus_info.active' => false]);

        /** @phpstan-ignore-next-line */
        Bonus::query()
            ->join('bonus_info', 'bonus_info.bonus_id', '=', 'bonuses.id')
            ->where('bonus_info.active', true)
            ->where('bonuses.active', true)
            ->whereNull('bonuses.active_til')
            ->whereIn('bonuses.type', [
                BonusTypeEnum::DEPOSIT->value,
                BonusTypeEnum::FREE_SPINS_FOR_DEPOSIT->value,
            ])
            ->whereDoesntHave('activeTriggerSessions')
            ->update(['bonus_info.active' => false]);
    }
}
