<?php

namespace App\Console\Commands;

use App\Enums\FreeSpinBoundStatusEnum;
use App\Models\FreeSpinBound;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class SolveColumnStatusInFreeSpinBoundsTableCommand extends Command
{
    protected $signature = 'app:solve-column-status-in-free-spin-bounds-table';

    protected $description = 'Solve column status in free spin bounds table';

    public function handle(): void
    {
        $startTime = microtime(true);

        /** @var FreeSpinBound $freeSpinBoundBuilder */
        $freeSpinBoundBuilder = FreeSpinBound::query()->whereNull('status');
        $freeSpinBoundsQuery = $freeSpinBoundBuilder->withTrashed();

        $this->output->progressStart($freeSpinBoundsQuery->count());

        $freeSpinBoundsQuery->chunkById(100, function (Collection $freeSpinBounds): void {
            /** @var FreeSpinBound $freeSpinBound */
            foreach ($freeSpinBounds as $freeSpinBound) {
                if ($freeSpinBound->expire_at > now() && $freeSpinBound->total_win === 0) {
                    $freeSpinBound->status = FreeSpinBoundStatusEnum::ACTIVE->value;
                } elseif ($freeSpinBound->canceled_at !== null) {
                    $freeSpinBound->status = FreeSpinBoundStatusEnum::DEACTIVATED->value;
                } elseif ($freeSpinBound->total_win === 0 && $freeSpinBound->count_left === 0) {
                    $freeSpinBound->status = FreeSpinBoundStatusEnum::LOST->value;
                } elseif ($freeSpinBound->total_win > 0 && $freeSpinBound->count_left === 0) {
                    $freeSpinBound->status = FreeSpinBoundStatusEnum::WIN->value;
                } elseif (
                    ($freeSpinBound->total_win >= 0 || $freeSpinBound->count_left >= 0)
                    && ($freeSpinBound->expire_at < now() || $freeSpinBound->expire_at === null)
                ) {
                    $freeSpinBound->status = FreeSpinBoundStatusEnum::EXPIRED->value;
                } else {
                    $freeSpinBound->status = FreeSpinBoundStatusEnum::INACTIVE->value;
                }

                $freeSpinBound->save();
                $this->output->progressAdvance();
            }
        });

        $this->output->progressFinish();
        $executionTime = microtime(true) - $startTime;
        $this->info('Finished successfully.');
        $this->info('Execution time: ' . number_format($executionTime, 2) . ' seconds');
    }
}
