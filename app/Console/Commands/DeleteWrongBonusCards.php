<?php

namespace App\Console\Commands;

use App\Models\BonusPlayerCard;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DeleteWrongBonusCards extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:delete-wrong-cards';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $timeStart = hrtime(true);
        $total = BonusPlayerCard::query()->count();

        $bonusCards = BonusPlayerCard::query()
            ->select(DB::raw('max(`id`) as id'), 'player_id', 'bonus_id', 'bonus_info_id', DB::raw('count(`id`) as occurrences'))
            ->groupBy(['player_id', 'bonus_id', 'bonus_info_id'])
            ->having('occurrences', '>', 1)
            ->where('is_used', 0)
            ->get();

        $this->info(PHP_EOL . "Total items : " . $total . PHP_EOL);

        $bar = $this->output->createProgressBar(count($bonusCards));

        foreach ($bonusCards as $bonusCard) {

            if ($bonusCard->occurrences > 1) {
                BonusPlayerCard::query()
                    ->where('player_id', $bonusCard->player_id)
                    ->where('bonus_id', $bonusCard->bonus_id)
                    ->where('bonus_info_id', $bonusCard->bonus_info_id)
                    ->where('id', '!=', $bonusCard->id)
                    ->delete();
            }
            $bar->advance();
        }

        $duration = (hrtime(true) - $timeStart) / 1e9;

        $bar->finish();

        $this->info(PHP_EOL . "Execute time - $duration s");
    }
}
