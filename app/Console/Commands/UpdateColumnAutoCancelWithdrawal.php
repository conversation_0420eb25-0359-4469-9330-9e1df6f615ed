<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Bonus;
use App\Models\FreeSpin;
use Illuminate\Console\Command;

class UpdateColumnAutoCancelWithdrawal extends Command
{
    protected $signature = 'app:update-column-auto-cancel-withdrawal';

    protected $description = 'Update column auto_cancel_withdrawal in bonuses and free_spins tables';

    public function handle(): void
    {
        $startTime = microtime(true);

        $bonuses = Bonus::query()
            ->where('is_external', false)
            ->where('auto_cancel_withdrawal', false);

        $freeSpins = FreeSpin::query()
            ->where('auto_cancel_withdrawal', false);

        $this->output->progressStart($bonuses->count() + $freeSpins->count());

        $bonuses->update(['auto_cancel_withdrawal' => true]);
        $freeSpins->update(['auto_cancel_withdrawal' => true]);

        $this->output->progressFinish();
        $executionTime = microtime(true) - $startTime;
        $this->info('All bonuses and freespins updated successfully.');
        $this->info('Execution time: ' . number_format($executionTime, 2) . ' seconds');
    }
}
