<?php

declare(strict_types=1);

namespace App\Gelf;

use Gelf\Publisher;
use Gelf\Transport\IgnoreErrorTransportWrapper;
use Monolog\Handler\GelfHandler;
use Monolog\Logger;

class GelfLoggerFactory extends \Hedii\LaravelGelfLogger\GelfLoggerFactory
{
    /**
     * @param array<string, null|string|int|array<string, null|string|int>> $config
     */
    public function __invoke(array $config): Logger
    {
        $config = $this->parseConfig($config);

        $transport = $this->getTransport(
            $config['transport'],
            $config['host'],
            $config['port'],
            $config['chunk_size'],
            $config['path'],
            $this->enableSsl($config) ? $this->sslOptions($config['ssl_options']) : null
        );

        if ($config['ignore_error']) {
            $transport = new IgnoreErrorTransportWrapper($transport);
        }

        /** @var string $level */
        $level = $this->level($config);
        $handler = new GelfHandler(new Publisher($transport), $level);

        $handler->setFormatter(
            new GelfMessageFormatter(
                $config['system_name'],
                $config['extra_prefix'],
                $config['context_prefix'],
                $config['max_length'],
                $config['unique_id']
            )
        );

        foreach ($this->parseProcessors($config) as $processor) {
            $handler->pushProcessor(new $processor());
        }

        return new Logger($this->parseChannel($config), [$handler]);
    }
}
