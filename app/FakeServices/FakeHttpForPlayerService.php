<?php

declare(strict_types=1);

namespace App\FakeServices;

use Illuminate\Support\Facades\Http;

class FakeHttpForPlayerService extends Http
{
    private bool $isFailed = false;
    private string $url;
    private string $token;

    /**
     * @var array<string, string>
     */
    public array $headers = [];

    final public function __construct(string $token)
    {
        $this->token = $token;
    }

    /** @uses setIsFailed */
    public function setIsFailed(bool $isFailed): void
    {
        $this->isFailed = $isFailed;
    }

    public function failed(): bool
    {
        return $this->isFailed;
    }

    public static function withToken(string $token): static
    {
        return new static($token);
    }

    public function baseUrl(string $url): static
    {
        $this->url = $url;

        return $this;
    }

    public function get(string $url = ''): static
    {
        $this->url .= $url;

        return $this;
    }

    /**
     * @return null|array<string, string>
     */
    public function object(): ?array
    {
        if ($this->failed()) {
            return null;
        }

        return ['url' => $this->url, 'token' => $this->token];
    }

    public function withHeader(string $name, string $value): static
    {
        $this->headers[$name] = $value;

        return $this;
    }
}
