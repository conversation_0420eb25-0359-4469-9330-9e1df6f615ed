{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "deversin/signature": "^1.4", "guzzlehttp/guzzle": "^7.9", "hedii/laravel-gelf-logger": "^9.0", "laravel/framework": "^11.0", "laravel/tinker": "^2.9", "wotz/laravel-swagger-ui": "^1.1", "spatie/laravel-responsecache": "^7.6", "ext-pdo": "*"}, "require-dev": {"fakerphp/faker": "^1.23", "friendsofphp/php-cs-fixer": "^3.75", "laravel/pint": "^1.14", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^11.0.1", "spatie/laravel-ignition": "^2.0"}, "repositories": [{"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/logger.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/white-label/wl-crypto-signature.git"}], "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}