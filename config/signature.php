<?php

return [
    'key_type' => env('SIGNATURE_KEY_TYPE', 'env'), // env or file

    'key' => [
        'env' => [
            'private' => base64_decode(env('SIGNATURE_PRIVATE_KEY', '')),
            'public' => base64_decode(env('SIGNATURE_PUBLIC_KEY', '')),
        ],
        'file' => [
            'private_path' => env('SIGNATURE_PRIVATE_KEY_PATH', storage_path('app/private.key')),
            'public_path' => env('SIGNATURE_PUBLIC_KEY_PATH', storage_path('app/public.key')),
        ],
    ],

];
