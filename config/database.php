<?php

use Illuminate\Support\Str;

return [
    'log_database_query' => (bool)env('LOG_DATABASE_QUERY', false),
    'log_database_query_performance' => (bool)env('LOG_DATABASE_QUERY_PERFORMANCE', false),

    'default' => 'mysql.6Vr',

    'connections' => [
        'sqlite' => [
            'driver' => 'sqlite',
            'url' => env('DATABASE_URL'),
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
        ],
        'mysql' => [
            'admin' => [
                'driver' => 'mysql',
                'url' => env('DATABASE_URL'),
                'host' => env('DB_HOST', '127.0.0.1'),
                'port' => env('DB_PORT', '3306'),
                'database' => env('DB_DATABASE', 'forge'),
                'username' => env('DB_USERNAME', 'forge'),
                'password' => env('DB_PASSWORD', ''),
                'unix_socket' => env('DB_SOCKET', ''),
                'charset' => 'utf8mb4',
                'collation' => 'utf8mb4_unicode_ci',
                'prefix' => '',
                'prefix_indexes' => true,
                'strict' => true,
                'engine' => null,
            ],
            'replica' => [
                'driver' => 'mysql',
                'host' => env('DB_REPLICA_HOST_APP_BACKEND', '127.0.0.1'),
                'port' => env('DB_REPLICA_PORT_APP_BACKEND', 3306),
                'database' => env('DB_REPLICA_DATABASE_APP_BACKEND', 'forge'),
                'username' => env('DB_REPLICA_USERNAME_APP_BACKEND', 'forge'),
                'password' => env('DB_REPLICA_PASSWORD_APP_BACKEND', ''),
                'unix_socket' => env('DB_SOCKET_APP_BACKEND', ''),
                'charset' => env('DB_CHARSET_APP_BACKEND', 'utf8mb4'),
                'collation' => env('DB_COLLATION_APP_BACKEND', 'utf8mb4_unicode_ci'),
                'prefix' => env('DB_PREFIX_APP_BACKEND', ''),
                'strict' => env('DB_STRICT_MODE_APP_BACKEND', true),
                'engine' => env('DB_ENGINE_APP_BACKEND', null),
                'timezone' => env('DB_TIMEZONE_APP_BACKEND', '+00:00'),
            ],
            'bonus' => [
                'driver' => 'mysql',
                'host' => env('DB_HOST_BONUS', '127.0.0.1'),
                'port' => env('DB_PORT_BONUS', 3306),
                'database' => env('DB_DATABASE_BONUS', 'forge'),
                'username' => env('DB_USERNAME_BONUS', 'forge'),
                'password' => env('DB_PASSWORD_BONUS', ''),
                'unix_socket' => env('DB_SOCKET_BONUS', ''),
                'charset' => env('DB_CHARSET_BONUS', 'utf8mb4'),
                'collation' => env('DB_COLLATION_BONUS', 'utf8mb4_unicode_ci'),
                'prefix' => env('DB_PREFIX_BONUS', ''),
                'strict' => env('DB_STRICT_MODE_BONUS', true),
                'engine' => env('DB_ENGINE_BONUS', null),
                'timezone' => env('DB_TIMEZONE_BONUS', '+00:00'),
            ],
            '6Vr' => [
                'driver' => 'mysql',
                'host' => env('DB_HOST_APP_BACKEND', '127.0.0.1'),
                'port' => env('DB_PORT_APP_BACKEND', 3306),
                'database' => env('DB_DATABASE_APP_BACKEND', 'forge'),
                'username' => env('DB_USERNAME_APP_BACKEND', 'forge'),
                'password' => env('DB_PASSWORD_APP_BACKEND', ''),
                'unix_socket' => env('DB_SOCKET_APP_BACKEND', ''),
                'charset' => env('DB_CHARSET_APP_BACKEND', 'utf8mb4'),
                'collation' => env('DB_COLLATION_APP_BACKEND', 'utf8mb4_unicode_ci'),
                'prefix' => env('DB_PREFIX_APP_BACKEND', ''),
                'strict' => env('DB_STRICT_MODE_APP_BACKEND', true),
                'engine' => env('DB_ENGINE_APP_BACKEND', null),
                'timezone' => env('DB_TIMEZONE_APP_BACKEND', '+00:00'),
            ],
            'aCh' => [
                'driver' => 'mysql',
                'host' => env('DB_HOST_APP_BACKEND_2', '127.0.0.1'),
                'port' => env('DB_PORT_APP_BACKEND_2', 3306),
                'database' => env('DB_DATABASE_APP_BACKEND_2', ''),
                'username' => env('DB_USERNAME_APP_BACKEND_2', ''),
                'password' => env('DB_PASSWORD_APP_BACKEND_2', ''),
                'unix_socket' => env('DB_SOCKET_APP_BACKEND', ''),
                'charset' => env('DB_CHARSET_APP_BACKEND', 'utf8mb4'),
                'collation' => env('DB_COLLATION_APP_BACKEND', 'utf8mb4_unicode_ci'),
                'prefix' => env('DB_PREFIX_APP_BACKEND', ''),
                'strict' => env('DB_STRICT_MODE_APP_BACKEND', true),
                'engine' => env('DB_ENGINE_APP_BACKEND', null),
                'timezone' => env('DB_TIMEZONE_APP_BACKEND', '+00:00'),
            ],
            'Ca4' => [
                'driver' => 'mysql',
                'host' => env('DB_HOST_APP_BACKEND_3', '127.0.0.1'),
                'port' => env('DB_PORT_APP_BACKEND_3', 3306),
                'database' => env('DB_DATABASE_APP_BACKEND_3', ''),
                'username' => env('DB_USERNAME_APP_BACKEND_3', ''),
                'password' => env('DB_PASSWORD_APP_BACKEND_3', ''),
                'unix_socket' => env('DB_SOCKET_APP_BACKEND', ''),
                'charset' => env('DB_CHARSET_APP_BACKEND', 'utf8mb4'),
                'collation' => env('DB_COLLATION_APP_BACKEND', 'utf8mb4_unicode_ci'),
                'prefix' => env('DB_PREFIX_APP_BACKEND', ''),
                'strict' => env('DB_STRICT_MODE_APP_BACKEND', true),
                'engine' => env('DB_ENGINE_APP_BACKEND', null),
                'timezone' => env('DB_TIMEZONE_APP_BACKEND', '+00:00'),
            ],
            'vtH' => [
                'driver' => 'mysql',
                'host' => env('DB_HOST_APP_BACKEND_4', '127.0.0.1'),
                'port' => env('DB_PORT_APP_BACKEND_4', 3306),
                'database' => env('DB_DATABASE_APP_BACKEND_4', ''),
                'username' => env('DB_USERNAME_APP_BACKEND_4', ''),
                'password' => env('DB_PASSWORD_APP_BACKEND_4', ''),
                'unix_socket' => env('DB_SOCKET_APP_BACKEND', ''),
                'charset' => env('DB_CHARSET_APP_BACKEND', 'utf8mb4'),
                'collation' => env('DB_COLLATION_APP_BACKEND', 'utf8mb4_unicode_ci'),
                'prefix' => env('DB_PREFIX_APP_BACKEND', ''),
                'strict' => env('DB_STRICT_MODE_APP_BACKEND', true),
                'engine' => env('DB_ENGINE_APP_BACKEND', null),
                'timezone' => env('DB_TIMEZONE_APP_BACKEND', '+00:00'),
            ],
        ],
        'pgsql' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],
        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', 'localhost'),
            'port' => env('DB_PORT', '1433'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
        ],
    ],

    'migrations' => 'migrations',
    'migrations_connection' => 'mysql.bonus',

    'redis' => [
        'client' => env('REDIS_CLIENT', 'phpredis'),
        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_database_'),
        ],
        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'),
        ],
        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
        ],
    ],
];
