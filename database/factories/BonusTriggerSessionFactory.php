<?php

namespace Database\Factories;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BonusTriggerSession>
 */
class BonusTriggerSessionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'bonus_id' => rand(1, 1000000),
            'start_at' => Carbon::now(),
            'end_at' => Carbon::now()->addDays(10),
            'uuid' => Str::uuid()->toString()
        ];
    }
}
