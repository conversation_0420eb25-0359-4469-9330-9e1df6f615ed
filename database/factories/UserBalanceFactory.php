<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\UserBalance;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<UserBalance>
 */
class UserBalanceFactory extends Factory
{
    protected $model = UserBalance::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'player_id' => rand(1, 100000),
            'currency' => 'EUR',
            'last_transaction_id' => null,
            'balance' => rand(100, 100000),
            'debt' => 0,
            'deposit' => 0,
            'wager' => 0,
            'withdraw' => 999999999,
            'in_game' => 0,
            'current_payout_limit' => 0,
            'total_bet' => 0,
            'total_profit' => 0,
            'payout_approved' => 0,
            'payout_wait' => 0,
        ];
    }
}
