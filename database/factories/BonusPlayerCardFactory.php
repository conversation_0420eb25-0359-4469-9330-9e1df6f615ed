<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\BonusPlayerCard;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<BonusPlayerCard>
 */
class BonusPlayerCardFactory extends Factory
{
    protected $model = BonusPlayerCard::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'player_id' => rand(1, 10000),
            'is_used' => false,
            'bonus_id' => rand(1, 10000),
            'bonus_info_id' => rand(1, 10000),
        ];
    }
}
