<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Bonus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Bonus>
 */
class BonusFactory extends Factory
{
    protected $model = Bonus::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = fake()->name();
        return [
            'name' => $name,
            'client_id' => 2,
            'bonus_name' => json_encode([
                'en' => "<p>$name</p>",
            ]),
            'description' => json_encode([
                'en' => "<p>$name</p>",
            ]),
            'condition' => json_encode([
                'en' => "<p>$name</p>",
            ]),
            'type' => fake()->randomElement(
                [
                    'welcome','welcome_4_steps','onetime','no_dep','deposit','freespin_bonus','free_spins_for_deposit',
                    'wager','freebet','free_money','no_risk'
                ]
            ),
            'image' => null,
            'bonuses' => [rand(100, 1000000)],
            'max_transfers' => [rand(100, 1000000)],
            'deposit_factors' => [rand(0, 100)],
            'currency' => fake()->randomElement(['USD', 'EUR', 'INR']),
            'wager' => rand(1, 20),
            'min_deposit' => rand(100, 1000000),
            'min_factor' => null,
            'min_bet' => rand(100, 1000000),
            'active' => true,
            'casino' => rand(0, 1),
            'bets' => rand(0, 1),
            'crash' => rand(0, 1),
            'total_transferred' => 0,
            'uses' => 0,
            'transfers' => 0,
            'total_uses' => 0,
            'is_external' => false,
            'is_promo' => false,
            'active_from' => Carbon::now(),
            'active_til' => Carbon::now()->addMonth(),
            'duration' => rand(1000000, 1000000000),
            'data' => '{}',
            'uuid' => (Str::uuid())->toString(),
        ];
    }
}
