<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\FreeSpin;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Bonus>
 */
class FreeSpinFactory extends Factory
{
    protected $model = FreeSpin::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'slot_id' => rand(1, 100),
            'provider_id' => rand(1, 100),
            'bonus_id' => rand(1, 100),
            'type' => 'manual',
            'name' => fake()->name(),
            'count' => 0,
            'bet' => 0,
            'bet_id' => rand(1, 100),
            'denomination' => '1',
            'currency' => 'USD',
            'status' => 'active',
            'is_active' => true,
            'author_id' => rand(1,100),
            'updated_by_author_id' => rand(1,100),
            'in_process' => false,
            'start_at' => Carbon::now(),
            'expired_at' => Carbon::now()->addMonth(),
            'data' => '{}',
            'uuid' => (Str::uuid())->toString(),
        ];
    }
}
