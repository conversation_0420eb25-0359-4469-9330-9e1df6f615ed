<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\DataSubscriptions;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DataSubscriptions>
 */
class DataSubscriptionsFactory extends Factory
{
    protected $model = DataSubscriptions::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'client_id' => 2,
            'sub_id' => 1,
            'access_token' => md5('test' . rand(1, 1000000)),
            'game_token' => md5('test' . rand(1, 1000000)),
            'provider' => 'betby_test_' . rand(100, *********),
            'provider_company' => 'gamicorp',
            'is_approved' => true,
            'is_slot' => true,
            'uuid' => Str::uuid()->toString(),
        ];
    }
}
