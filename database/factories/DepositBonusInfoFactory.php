<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\DepositBonusInfo;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<DepositBonusInfo>
 */
class DepositBonusInfoFactory extends Factory
{
    protected $model = DepositBonusInfo::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'bonus_id' => rand(1, 100000),
            'smartico_bonus_id' => rand(1, 100000),
            'player_id' => rand(1, 100000),
            'email' => fake()->email(),
            'amount' => rand(1, 100000),
            'status' => 'in_process',
            'text' => implode(' ', fake()->words(10)),
        ];
    }
}
