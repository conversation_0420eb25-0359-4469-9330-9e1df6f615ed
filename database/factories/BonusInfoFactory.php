<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\BonusInfo;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BonusInfo>
 */
class BonusInfoFactory extends Factory
{
    protected $model = BonusInfo::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = fake()->name();
        return [
            'client_id' => 2,
            'name' => $name,
            'currency' => 'USD',
            'active' => true,
            'visible_from' => Carbon::now(),
            'visible_to' => Carbon::now()->addDays(7),
            'casino' => false,
            'is_welcome' => true,
            'is_for_smartico' => true,
            'image' => fake()->url(),
            'bonus_name' => json_encode([
                'en' => "<p>$name</p>",
            ]),
            'description' => json_encode([
                'en' => "<p>$name</p>",
            ]),
            'description_info' => json_encode([
                'en' => "<p>$name</p>",
            ]),
            'condition_title' => json_encode([
                'en' => "<p>$name</p>",
            ]),
            'condition' => json_encode([
                'en' => "<p>$name</p>",
            ]),
            'colors' => json_encode([]),
            'sort_order' => 1,
            'proceed_link' => fake()->url(),
            'bonus_id' => rand(1, 100000000),
            'uuid' => Str::uuid()->toString(),
        ];
    }
}
