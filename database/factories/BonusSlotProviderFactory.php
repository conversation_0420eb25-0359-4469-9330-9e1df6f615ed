<?php

namespace Database\Factories;

use App\Models\BonusSlotProvider;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BonusSlotProvider>
 */
class BonusSlotProviderFactory extends Factory
{
    protected $model = BonusSlotProvider::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'bonus_id' => rand(1, 10000),
            'slot_provider_id' => rand(1, 10000),
            'uuid' => Str::uuid()->toString()
        ];
    }
}
