<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\BonusSlot;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BonusSlot>
 */
class BonusSlotFactory extends Factory
{
    protected $model = BonusSlot::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'bonus_id' => rand(1, 10000),
            'slot_id' => rand(1, 10000),
            'uuid' => Str::uuid()->toString(),
        ];
    }
}
