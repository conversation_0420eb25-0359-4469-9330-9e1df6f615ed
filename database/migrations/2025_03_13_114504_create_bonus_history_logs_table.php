<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mysql.bonus')
            ->create('bonus_history_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('bonus_id')->index();
            $table->string('author_email')->nullable();
            $table->string('type');
            $table->json('data');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('mysql.bonus')->dropIfExists('bonus_history_logs');
    }
};
