<?php

use App\Enums\BonusTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (Schema::hasTable('issued_bonuses')) {
            return;
        }

        Schema::connection('mysql.bonus')->create('issued_bonuses', function (Blueprint $table) {
            $table->id();
            $table->string('log_key')->nullable();
            $table->integer('bonus_id');
            $table->string('type', 40)->default(BonusTypeEnum::WAGER->value);
            $table->integer('player_id');
            $table->bigInteger('balance');
            $table->string('bonus_external_id')->nullable();
            $table->bigInteger('orig_wager');
            $table->bigInteger('orig_bonus');
            $table->string('status', 40);
            $table->bigInteger('wager');
            $table->float('min_factor')->nullable();
            $table->bigInteger('min_bet')->nullable();
            $table->timestamp('expire_at')->nullable();
            $table->bigInteger('in_game');
            $table->bigInteger('transfer');
            $table->string('currency', 3);
            $table->boolean('active');
            $table->boolean('casino');
            $table->boolean('bets');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        if (!Schema::hasTable('issued_bonuses')) {
            return;
        }

        Schema::dropIfExists('issued_bonuses');
    }
};
