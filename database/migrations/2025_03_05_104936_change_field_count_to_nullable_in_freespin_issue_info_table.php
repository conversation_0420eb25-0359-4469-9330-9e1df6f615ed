<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::connection('mysql.bonus')
            ->table('freespin_issue_info', function (Blueprint $table) {
                $table->string('count')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::connection('mysql.bonus')
            ->table('freespin_issue_info', function (Blueprint $table) {
                $table->string('count')->nullable(false)->change();
        });
    }
};
