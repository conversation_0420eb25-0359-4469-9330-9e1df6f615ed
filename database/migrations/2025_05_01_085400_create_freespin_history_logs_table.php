<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mysql.bonus')
            ->create('freespin_history_logs', static function (Blueprint $table): void {
                $table->id();
                $table->unsignedInteger('free_spin_id')->index();
                $table->string('author_email')->nullable();
                $table->string('type');
                $table->json('data');
                $table->timestamps();
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('freespin_history_logs');
    }
};
