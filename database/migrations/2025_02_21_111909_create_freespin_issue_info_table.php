<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::connection('mysql.bonus')
            ->create('freespin_issue_info', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('freespin_id');
                $table->string('smartico_freespin_id');
                $table->string('user_id');
                $table->string('count');
                $table->timestamps();
            });
    }

    public function down(): void
    {
        Schema::connection('mysql.bonus')->dropIfExists('freespin_issue_info');
    }
};
