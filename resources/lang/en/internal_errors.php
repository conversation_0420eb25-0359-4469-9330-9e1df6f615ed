<?php

declare(strict_types=1);

use App\ValueObjects\InternalErrorCode;

return [
    InternalErrorCode::GENERAL_ERROR => 'General error',
    InternalErrorCode::CORE_UNAUTHORIZED => 'Unauthorized, please pass a valid core bearer token',
    InternalErrorCode::CORE_GET_PLAYER_INFO_ERROR => 'Core get player info API error',
    InternalErrorCode::CORE_PARSE_PLAYER_INFO_ERROR => 'Core parse player info error',
    InternalErrorCode::BONUS_TRIGGER_SESSION_DELETE_ERROR => 'You forbidden to delete trigger sessions when bonus is already started',
    InternalErrorCode::BONUS_FOR_DEPOSIT_IS_ALREADY_USED => 'Bonus for deposit is already used',
];
