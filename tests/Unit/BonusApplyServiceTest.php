<?php

namespace Tests\Unit;

use App\Models\Bonus;
use App\Services\BonusApplyService;
use App\Services\BonusBalanceService;
use App\Services\BonusPlayerCardService;
use App\Services\BonusPlayerService;
use App\Services\DepositService;
use App\Services\Dto\DepositSelectedBonusDTO;
use Mockery;
use Tests\TestCase;
use Exception;

class BonusApplyServiceTest extends TestCase
{
    protected BonusApplyService $bonusApplyService;
    protected BonusPlayerService $mockBonusPlayerService;
    protected DepositService $mockDepositService;
    protected BonusPlayerCardService $mockBonusPlayerCardService;
    protected BonusBalanceService $mockBonusBalanceService;
    protected BonusApplyService $mockBonusApplyService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockBonusPlayerService = Mockery::mock(BonusPlayerService::class);
        $this->mockDepositService = Mockery::mock(DepositService::class);
        $this->mockBonusPlayerCardService = Mockery::mock(BonusPlayerCardService::class);
        $this->mockBonusBalanceService = Mockery::mock(BonusBalanceService::class);
        $this->mockBonusApplyService = Mockery::mock(BonusApplyService::class, [
            $this->mockBonusPlayerService,
            $this->mockDepositService,
            $this->mockBonusPlayerCardService,
            $this->mockBonusBalanceService,
        ])->makePartial();

        $this->bonusApplyService = new BonusApplyService(
            $this->mockBonusPlayerService,
            $this->mockDepositService,
            $this->mockBonusPlayerCardService,
            $this->mockBonusBalanceService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @throws Exception
     */
    public function testDepositSelectedBonusReturnsFalseWhenNoBonusFound()
    {
        $dto = Mockery::mock(DepositSelectedBonusDTO::class);
        $dto->playerId = 1;

        $this->mockBonusPlayerService
            ->shouldReceive('getSelectedBonus')
            ->with($dto->playerId)
            ->andReturn(null);

        $this->assertFalse($this->bonusApplyService->depositSelectedBonus($dto));
    }

    /**
     * @throws Exception
     */
    public function testDepositSelectedBonusReturnsFalseWhenBonusIsInvalid()
    {
        $dto = Mockery::mock(DepositSelectedBonusDTO::class);
        $dto->playerId = 1;

        $bonus = new Bonus();

        $this->mockBonusPlayerService
            ->shouldReceive('getSelectedBonus')
            ->with($dto->playerId)
            ->andReturn($bonus);

        $this->mockBonusApplyService
            ->shouldAllowMockingProtectedMethods()
            ->shouldReceive('isValidBonus')
            ->with($bonus, $dto)
            ->andReturn(false);

        $this->assertFalse($this->bonusApplyService->depositSelectedBonus($dto));
    }


    /**
     * @throws Exception
     */
    public function testDepositSelectedBonusReturnsFalseWhenBonusLimitExceeded()
    {
        $dto = new DepositSelectedBonusDTO([
            'player_id' => 1,
            'amount' => 1,
            'deposits_count' => 1,
            'player_uuid' => 'uuid',
            'player_currency' => 'USD',
        ]);

        $bonus = new Bonus();
        $bonus->setAttribute('active', true);
        $bonus->setAttribute('currency', 'USD');

        $this->mockBonusPlayerService
            ->shouldReceive('getSelectedBonus')
            ->with($dto->playerId)
            ->andReturn($bonus);

        $this->mockBonusApplyService
            ->shouldAllowMockingProtectedMethods()
            ->shouldReceive('isValidBonus')
            ->with($bonus, $dto)
            ->andReturn(true);

        $this->mockBonusApplyService
            ->shouldAllowMockingProtectedMethods()
            ->shouldReceive('getDepositPosition')
            ->with($bonus, $dto)
            ->andReturn(1);

        $this->mockBonusApplyService
            ->shouldAllowMockingProtectedMethods()
            ->shouldReceive('isBonusLimitExceed')
            ->with($bonus, $dto, 1)
            ->andReturn(true);

        $this->assertFalse($this->bonusApplyService->depositSelectedBonus($dto));
    }

    /**
     * @throws Exception
     */
    public function testDepositSelectedBonusReturnsTrue()
    {
        $dto = new DepositSelectedBonusDTO([
            'player_id' => 1,
            'amount' => 1,
            'deposits_count' => 1,
            'player_uuid' => 'uuid',
            'player_currency' => 'USD',
        ]);

        $bonus = new Bonus();
        $bonus->setAttribute('active', true);
        $bonus->setAttribute('currency', 'USD');
        $bonus->setAttribute('deposit_factors', [10000]);

        $this->mockBonusPlayerService
            ->shouldReceive('getSelectedBonus')
            ->with($dto->playerId)
            ->andReturn($bonus);

        $this->mockBonusApplyService
            ->shouldAllowMockingProtectedMethods()
            ->shouldReceive('isValidBonus')
            ->with($bonus, $dto)
            ->andReturn(true);

        $this->mockBonusApplyService
            ->shouldAllowMockingProtectedMethods()
            ->shouldReceive('getDepositPosition')
            ->with($bonus, $dto)
            ->andReturn(1);

        $this->mockBonusApplyService
            ->shouldAllowMockingProtectedMethods()
            ->shouldReceive('isBonusLimitExceed')
            ->with($bonus, $dto, 1)
            ->andReturn(false);

        $this->mockBonusApplyService
            ->shouldAllowMockingProtectedMethods()
            ->shouldReceive('processActivateSelectedBonus')
            ->with($bonus, $dto, 1)
            ->once();

        $this->assertTrue($this->mockBonusApplyService->depositSelectedBonus($dto));
    }
}
