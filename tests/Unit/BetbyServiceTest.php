<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Models\DataSubscriptions;
use App\Repositories\BetbyRepository;
use App\Services\BetbyService;
use App\Services\DataSubscriptionService;
use App\Services\Dto\BetbySearchDTO;
use Illuminate\Support\Facades\Http;
use Mockery;
use Tests\TestCase;

class BetbyServiceTest extends TestCase
{
    private DataSubscriptionService $dataSubscriptionService;
    private BetbyRepository $betbyRepository;
    private BetbyService $betbyService;

    public function setUp(): void
    {
        parent::setUp();

        $this->dataSubscriptionService = Mockery::mock(DataSubscriptionService::class);
        $this->betbyRepository = Mockery::mock(BetbyRepository::class);
        $this->betbyService = new BetbyService($this->dataSubscriptionService, $this->betbyRepository);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function test_betby_service_get_templates()
    {
        $subscription = new DataSubscriptions();
        $subscription->id = 1;
        $subscription->sub_id = 1;
        $subscription->access_token = 'test_token';

        $this->dataSubscriptionService->shouldReceive('getByProviderName')
            ->with('betby')->once()
            ->andReturn($subscription);

        $result = [
            'items' => [
                ['id' => 1, 'freebet_data' => ['type' => 'free_money'], 'is_active' => true],
                ['id' => 2, 'freebet_data' => ['type' => 'bet_refund'], 'is_active' => false],
            ]
        ];

        Http::fake([
            config('app.betby_host') . '/api/v1/bonus/templates' => Http::response($result),
        ]);

        $this->betbyRepository->shouldReceive('getFilteredData')
            ->once()
            ->andReturn($result);

        $dto = new BetbySearchDTO();
        $templates = $this->betbyService->getTemplates($dto);

        $this->assertIsArray($templates['items']);
        $this->assertCount(2, $templates['items']);
        $this->assertEquals(1, $templates['items'][0]['id']);
    }

    public function test_betby_service_get_templates_empty()
    {
        $subscription = new DataSubscriptions();
        $subscription->id = 1;
        $subscription->sub_id = 1;
        $subscription->access_token = 'test_token';

        $this->dataSubscriptionService->shouldReceive('getByProviderName')
            ->with('betby')->once()
            ->andReturn($subscription);

        Http::fake([
            config('app.betby_host') . '/api/v1/bonus/templates' => Http::response([]),
        ]);

        $dto = new BetbySearchDTO();
        $templates = $this->betbyService->getTemplates($dto);

        $this->assertNull($templates);
    }
}
