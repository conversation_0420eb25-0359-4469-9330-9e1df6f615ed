<?php

namespace Tests\Unit;

use App\Enums\SupplierEnum;
use App\Services\Dto\PlayerBoundSearchDTO;
use Exception;
use App\Models\FreeSpin;
use App\Models\FreeSpinBound;
use App\Repositories\FreeSpinBoundRepository;
use App\Services\BonusBalanceService;
use App\Services\Dto\FreeSpinBoundSearchDTO;
use App\Services\FreeSpinBoundService;
use App\Services\FreeSpinService;
use App\Services\GamicorpFreeSpinBoundService;
use App\Services\PlayerService;
use App\Services\SendToSlotegratorHttpService;
use App\Services\SlotegratorFreeSpinBoundService;
use App\Services\SlotService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Mockery;
use Tests\TestCase;

class FreeSpinBoundServiceTest extends TestCase
{
    private FreeSpinService $mockFreeSpinService;
    private FreeSpinBoundRepository $mockFreeSpinBoundRepository;
    private FreeSpinBoundService $freeSpinBoundService;
    private SlotService $mockSlotService;
    private GamicorpFreeSpinBoundService $mockGamicorpFreeSpinBoundService;
    private SlotegratorFreeSpinBoundService $mockSlotegratorFreeSpinBoundService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockFreeSpinBoundRepository = Mockery::mock(FreeSpinBoundRepository::class);
        $mockPlayerService = Mockery::mock(PlayerService::class);
        $this->mockSlotService = Mockery::mock(SlotService::class);
        $this->mockGamicorpFreeSpinBoundService = Mockery::mock(GamicorpFreeSpinBoundService::class);
        $mockBonusBalanceService = Mockery::mock(BonusBalanceService::class);
        $mockSendToSlotegratorHttpService = Mockery::mock(SendToSlotegratorHttpService::class);
        $this->mockSlotegratorFreeSpinBoundService = Mockery::mock(SlotegratorFreeSpinBoundService::class);
        $this->mockFreeSpinService = Mockery::mock(FreeSpinService::class);

        $this->freeSpinBoundService = new FreeSpinBoundService(
            $this->mockFreeSpinBoundRepository,
            $mockBonusBalanceService,
            $mockSendToSlotegratorHttpService,
            $this->mockSlotService,
            $this->mockGamicorpFreeSpinBoundService,
            $mockPlayerService,
            $this->mockSlotegratorFreeSpinBoundService
        );
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function testGetPlayerFreeSpinsByActiveStatus(): void
    {
        $expectedActiveFreeSpins = new Collection([new FreeSpinBound()]);
        /** @var PlayerBoundSearchDTO $dto */
        $dto = PlayerBoundSearchDTO::fromArray(['player_id' => 1, 'status' => 'active']);
        $this->mockFreeSpinBoundRepository
            ->shouldReceive('getPlayerActiveFreeSpinBounds')->once()
            ->with($dto)
            ->andReturn($expectedActiveFreeSpins);

        $this->mockSlotService
            ->shouldReceive('getSlotListByIds')
            ->andReturn([]);

        $result = $this->freeSpinBoundService->getPlayerFreeSpinsByStatus($dto);

        $this->assertSame($expectedActiveFreeSpins, $result);
    }

    public function testGetPlayerFreeSpinsByArchivedStatus(): void
    {
        /** @var PlayerBoundSearchDTO $dto */
        $dto = PlayerBoundSearchDTO::fromArray(['player_id' => 1, 'status' => 'history']);
        $expectedArchivedFreeSpins = new Collection(new FreeSpinBound());
        $this->mockFreeSpinBoundRepository
            ->shouldReceive('getPlayerArchivedFreeSpinBounds')->once()
            ->with($dto)
            ->andReturn($expectedArchivedFreeSpins);

        $this->mockSlotService
            ->shouldReceive('getSlotListByIds')
            ->andReturn([]);

        $result = $this->freeSpinBoundService->getPlayerFreeSpinsByStatus($dto);

        $this->assertSame($expectedArchivedFreeSpins, $result);
    }

    public function testUpdate(): void
    {
        $this->mockFreeSpinBoundRepository
            ->shouldReceive('updateFreeSpinBounds')
            ->with([1], [1])
            ->once()
            ->andReturn(1);

        $result = $this->freeSpinBoundService->update([1], [1]);
        $this->assertSame(1, $result);
    }

    public function testGetPlayersByFreeSpinID(): void
    {
        $dto = new FreeSpinBoundSearchDTO([
            'id' => 1,
        ]);

        $mockLengthAwarePaginator = new LengthAwarePaginator([], 0, 10);

        $mockFreeSpinService = Mockery::mock(FreeSpinBoundService::class)
            ->shouldAllowMockingProtectedMethods()
            ->makePartial();

        $mockFreeSpinService->shouldReceive('search')
            ->once()
            ->with($dto)
            ->andReturn($mockLengthAwarePaginator);

        $mockFreeSpinService->shouldReceive('hydrateFreeSpinsBoundsWithPlayers')
            ->once()
            ->with($mockLengthAwarePaginator);

        $result = $mockFreeSpinService->getFreeSpinBoundList($dto);

        $this->assertSame($mockLengthAwarePaginator, $result);
    }

    /**
     * @throws Exception
     */
    public function testAttachFreeSpinToPlayerGamicorp(): void
    {
        $bonusId = 1;
        $playerId = 100;

        $freeSpin = new FreeSpin();
        $freeSpin->setAttribute('supplier', SupplierEnum::GAMICORP_INTEGRATION->value);

        $this->mockFreeSpinService->shouldReceive('getFreeSpinByBonusId')
            ->once()
            ->with($bonusId)
            ->andReturn($freeSpin);

        $this->mock(FreeSpinService::class, function ($mock): void {
            $mock->shouldReceive('getFreeSpinByBonusId')
                ->andReturnUsing(fn($bonusId) => $this->mockFreeSpinService->getFreeSpinByBonusId($bonusId));
        });

        $this->mockGamicorpFreeSpinBoundService->shouldReceive('attach')
            ->once()
            ->with($freeSpin, $playerId)
            ->andReturn(true);

        $result = $this->freeSpinBoundService->attachFreeSpinToPlayer($bonusId, $playerId);

        $this->assertTrue($result);
    }

    /**
     * @throws Exception
     */
    public function testAttachFreeSpinToPlayerSlotegrator(): void
    {
        $bonusId = 2;
        $playerId = 200;

        $freeSpin = new FreeSpin();
        $freeSpin->setAttribute('supplier', SupplierEnum::SLOTEGRATOR->value);

        $this->mockFreeSpinService->shouldReceive('getFreeSpinByBonusId')
            ->once()
            ->with($bonusId)
            ->andReturn($freeSpin);

        $this->mock(FreeSpinService::class, function ($mock): void {
            $mock->shouldReceive('getFreeSpinByBonusId')
                ->andReturnUsing(fn($bonusId) => $this->mockFreeSpinService->getFreeSpinByBonusId($bonusId));
        });

        $this->mockSlotegratorFreeSpinBoundService->shouldReceive('attach')
            ->once()
            ->with($freeSpin, $playerId)
            ->andReturn(true);

        $result = $this->freeSpinBoundService->attachFreeSpinToPlayer($bonusId, $playerId);

        $this->assertTrue($result);
    }

    /**
     * @throws Exception
     */
    public function testAttachFreeSpinToPlayerUnknownSupplier(): void
    {
        $bonusId = 3;
        $playerId = 300;

        $freeSpin = new FreeSpin();
        $freeSpin->setAttribute('supplier', 'Unknown supplier');

        $this->mockFreeSpinService->shouldReceive('getFreeSpinByBonusId')
            ->once()
            ->with($bonusId)
            ->andReturn($freeSpin);

        $this->mock(FreeSpinService::class, function ($mock): void {
            $mock->shouldReceive('getFreeSpinByBonusId')
                ->andReturnUsing(fn($bonusId) => $this->mockFreeSpinService->getFreeSpinByBonusId($bonusId));
        });

        $result = $this->freeSpinBoundService->attachFreeSpinToPlayer($bonusId, $playerId);

        $this->assertFalse($result);
    }

    /**
     * @throws Exception
     */
    public function testCancelFreeSpinBoundGamicorp(): void
    {
        $freeSpinBoundId = 1;
        $playerId = 100;

        $freeSpin = new FreeSpin();
        $freeSpin->setAttribute('supplier', SupplierEnum::GAMICORP_INTEGRATION->value);

        $freeSpinBound = new FreeSpinBound();
        $freeSpinBound->setAttribute('id', $freeSpinBoundId);
        $freeSpinBound->setAttribute('player_id', $playerId);

        $this->mockFreeSpinBoundRepository->shouldReceive('getFreeSpinDetailByBoundId')
            ->once()
            ->with($freeSpinBoundId)
            ->andReturn($freeSpin);

        $this->mockFreeSpinBoundRepository->shouldReceive('getFreeSpinBoundDetailById')
            ->once()
            ->with($freeSpinBoundId)
            ->andReturn($freeSpinBound);

        $this->mockGamicorpFreeSpinBoundService->shouldReceive('cancelFreespinBound')
            ->once()
            ->with($freeSpinBound)
            ->andReturn(true);

        $result = $this->freeSpinBoundService->cancelFreeSpinBound($freeSpinBoundId, $playerId);

        $this->assertTrue($result);
    }

    /**
     * @throws \App\Exceptions\Exception
     */
    public function testCancelFreeSpinBoundThrowsUnauthorizedException(): void
    {
        $freeSpinBoundId = 2;
        $playerId = 100;
        $wrongPlayerId = 200;

        $freeSpin = new FreeSpin();
        $freeSpin->setAttribute('supplier', SupplierEnum::GAMICORP_INTEGRATION->value);

        $freeSpinBound = new FreeSpinBound();
        $freeSpinBound->setAttribute('id', $freeSpinBoundId);
        $freeSpinBound->setAttribute('player_id', $wrongPlayerId);

        $this->mockFreeSpinBoundRepository->shouldReceive('getFreeSpinDetailByBoundId')
            ->once()
            ->with($freeSpinBoundId)
            ->andReturn($freeSpin);

        $this->mockFreeSpinBoundRepository->shouldReceive('getFreeSpinBoundDetailById')
            ->once()
            ->with($freeSpinBoundId)
            ->andReturn($freeSpinBound);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Unauthorized, please pass a valid core bearer token');

        $this->freeSpinBoundService->cancelFreeSpinBound($freeSpinBoundId, $playerId);
    }
}
