<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Models\Banner;
use App\Repositories\BannerRepository;
use App\Services\BannerService;
use App\Services\Dto\ActivationSelectedBannersOutputDTO;
use App\Services\Dto\BannerCreateDTO;
use App\Services\Dto\BannerEnabledDTO;
use App\Services\Dto\SearchBannersDTO;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Foundation\Testing\TestCase;
use Mockery;
use Tests\CreatesApplication;

class BannerServiceTest extends TestCase
{
    use CreatesApplication;

    private BannerService $bannerService;
    private BannerRepository $bannerRepository;

    private array $createData;

    protected function setUp(): void
    {
        parent::setUp();

        $this->bannerRepository = Mockery::mock(BannerRepository::class);
        $this->bannerService = new BannerService($this->bannerRepository);

        $this->createData = [
            "name" => "Test",
            "is_for_signed_in" => "all",
            "platform" =>"all",
            "mobile_apk_install" => true,
            "image" => "https://upload.deversin.com/storage/1606530/2015-08-14-dog-05.jpg",
            "weight" => 2,
            "type" => "big",
            "disposition" => [
                "Virtual Sport"
            ],
            "enabled" => true,
            "smartico_segment_id" => "343423",
            "start_at" => 1718969057,
            "end_at" => 1718969057
        ];
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function testCreate()
    {
        $bannerCreateDto = BannerCreateDTO::fromArray($this->createData);

        $mockedModel = new Banner();
        $this->bannerRepository
            ->shouldReceive('saveBanner')
            ->once()
            ->andReturn($mockedModel);

        /** @var BannerCreateDTO $bannerCreateDto */
        $result = $this->bannerService->create($bannerCreateDto);

        $this->assertInstanceOf(Banner::class, $result);
    }

    public function testUpdate()
    {
        $bannerCreateDto = new BannerCreateDTO($this->createData);
        $bannerId = 1;

        $mockedModel = new Banner();
        $mockedModel->fill($bannerCreateDto->toArray(false));
        $this->bannerRepository
            ->shouldReceive('saveBanner')
            ->once()
            ->withArgs([$bannerCreateDto->getData(false), $bannerId])
            ->withArgs([$mockedModel])
            ->andReturn($mockedModel);

        $this->bannerRepository
            ->shouldReceive('getBannerById')
            ->once()
            ->with($bannerId)
            ->andReturn($mockedModel);

        $result = $this->bannerService->update($bannerCreateDto, $bannerId);

        $this->assertInstanceOf(Banner::class, $result);
        $this->assertEquals($mockedModel['name'], $result->name);
        $this->assertEquals($mockedModel['is_for_signed_in'], $result->is_for_signed_in);
        $this->assertTrue($result->enabled);
    }

    public function testList()
    {
        $searchBannersDTO = new SearchBannersDTO([
            'type' => 'big',
        ]);

        $mockedPaginator = Mockery::mock(LengthAwarePaginator::class);

        $this->bannerRepository
            ->shouldReceive('getWithPagination')
            ->once()
            ->withArgs([$searchBannersDTO])
            ->andReturn($mockedPaginator);

        $result = $this->bannerService->list($searchBannersDTO);

        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
    }

    public function testChangeBannerStatus()
    {
        $bannerId = 1;

        $bannerModel = new Banner();
        $bannerModel->id = $bannerId;
        $bannerModel->enabled = true;

        $this->bannerRepository
            ->shouldReceive('save')
            ->once()
            ->withArgs([$bannerModel])
            ->andReturn(true);

        $this->bannerRepository
            ->shouldReceive('getBannerById')
            ->once()
            ->with($bannerId)
            ->andReturn($bannerModel);

        $result = $this->bannerService->changeStatus($bannerId, true);

        $this->assertTrue($result);
        $this->assertTrue($bannerModel->enabled);
    }

    public function testDelete()
    {
        $bannerId = 1;

        $bannerModel = new Banner();
        $bannerModel->id = $bannerId;
        $bannerModel->enabled = true;

        $this->bannerRepository
            ->shouldReceive('getBannerById')
            ->once()
            ->with($bannerId)
            ->andReturn($bannerModel);

        $this->bannerRepository
            ->shouldReceive('remove')
            ->once()
            ->withArgs([$bannerModel])
            ->andReturn(true);

        $result = $this->bannerService->delete($bannerId);
        $this->assertTrue($result);
    }

    public function testEnabledBanners()
    {
        $dto = new BannerEnabledDTO();
        $mockedPaginator = Mockery::mock(LengthAwarePaginator::class);

        $this->bannerRepository
            ->shouldReceive('getEnabledBanners')
            ->once()
            ->with($dto)
            ->andReturn($mockedPaginator);

        $result = $this->bannerService->enabledBanners($dto);
        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
    }

    public function testChangeStatusManyActivatesBanners()
    {
        $bannerService = Mockery::mock(BannerService::class)->makePartial();

        $ids = [1, 2, 3];
        $processedIds = [
            'processed_ids' => [
                1 => 'Banner 1',
                2 => 'Banner 2',
                3 => 'Banner 3',
            ],
            'non_processed_ids' => [],
        ];

        $expectedStatus = 'success';

        $bannerService
            ->shouldReceive('activateSelectedBanners')
            ->once()
            ->with($ids)
            ->andReturn($processedIds);

        $result = $bannerService->changeStatusMany($ids, true);

        $this->assertInstanceOf(ActivationSelectedBannersOutputDTO::class, $result);
        $this->assertEquals($processedIds['processed_ids'], $result->processedIds);
        $this->assertEquals($processedIds['non_processed_ids'], $result->nonProcessedIds);
        $this->assertEquals($expectedStatus, $result->status);
    }

    public function testChangeStatusManyDeactivatesBanners()
    {
        $bannerService = Mockery::mock(BannerService::class)->makePartial();

        $ids = [1, 2, 3];
        $processedIds = [
            'processed_ids' => [
                1 => 'Banner 1',
            ],
            'non_processed_ids' => [
                2,
                3,
            ],
        ];

        $expectedStatus = 'success partially';

        $bannerService
            ->shouldReceive('deactivateSelectedBanners')
            ->once()
            ->with($ids)
            ->andReturn($processedIds);

        $result = $bannerService->changeStatusMany($ids, false);

        $this->assertInstanceOf(ActivationSelectedBannersOutputDTO::class, $result);
        $this->assertEquals($processedIds['processed_ids'], $result->processedIds);
        $this->assertEquals($processedIds['non_processed_ids'], $result->nonProcessedIds);
        $this->assertEquals($expectedStatus, $result->status);
    }

    public function testChangeStatusManyReturnsNotSuccess()
    {
        $bannerService = Mockery::mock(BannerService::class)->makePartial();

        $ids = [1, 2, 3];
        $processedIds = [
            'processed_ids' => [],
            'non_processed_ids' => $ids,
        ];

        $expectedStatus = 'not success';

        $bannerService
            ->shouldReceive('deactivateSelectedBanners')
            ->once()
            ->with($ids)
            ->andReturn($processedIds);

        $result = $bannerService->changeStatusMany($ids, false);

        $this->assertInstanceOf(ActivationSelectedBannersOutputDTO::class, $result);
        $this->assertEquals($processedIds['processed_ids'], $result->processedIds);
        $this->assertEquals($processedIds['non_processed_ids'], $result->nonProcessedIds);
        $this->assertEquals($expectedStatus, $result->status);
    }
}
