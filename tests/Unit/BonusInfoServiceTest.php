<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Models\BonusInfo;
use App\Repositories\BonusInfoRepository;
use App\Services\BonusInfoService;
use App\Services\Dto\ActiveBonusesInfoDTO;
use App\Services\Dto\BonusInfoCreateDTO;
use App\Services\Dto\BonusInfoSearchDTO;
use App\Services\Dto\BonusInfoUpdateDTO;
use App\Services\PlayerService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Mockery;
use PHPUnit\Framework\TestCase;

class BonusInfoServiceTest extends TestCase
{
    protected BonusInfoService $service;
    protected BonusInfoRepository $repositoryMock;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repositoryMock = Mockery::mock(BonusInfoRepository::class);
        $this->playerService = Mockery::mock(PlayerService::class);
        $this->service = new BonusInfoService($this->repositoryMock, $this->playerService);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function testGetActiveBonusesInfoDataReturnsCollection()
    {
        // Prepare mock data
        $mockedBonuses = new Collection([
            ['id' => 1, 'name' => 'Bonus 1'],
            ['id' => 2, 'name' => 'Bonus 2'],
        ]);

        // Define the expected behavior of the mocked repository
        $this->repositoryMock
            ->shouldReceive('getActiveBonusesInfoData')
            ->once() // Ensure the method is called exactly once
            ->andReturn($mockedBonuses); // Return the mocked bonuses collection

        // Call the method on the service
        $result = $this->service->getActiveBonusesInfoData();

        // Assert that the result is a Collection
        $this->assertInstanceOf(Collection::class, $result);

        // Assert that the result matches the mocked bonuses
        $this->assertEquals($mockedBonuses, $result);
    }

    public function testGetActiveBonusesReturnsCollection()
    {
        // Prepare mock data
        $mockedBonuses = new Collection([
            ['id' => 1, 'name' => 'Bonus 1'],
            ['id' => 2, 'name' => 'Bonus 2'],
        ]);

        // Define the expected behavior of the mocked repository
        $this->repositoryMock
            ->shouldReceive('getActiveBonuses')
            ->once() // Ensure the method is called exactly once
            ->andReturn($mockedBonuses); // Return the mocked bonuses collection

        // Call the method on the service
        $result = $this->service->getActiveBonuses(new ActiveBonusesInfoDTO);

        // Assert that the result is a Collection
        $this->assertInstanceOf(Collection::class, $result);

        // Assert that the result matches the mocked bonuses
        $this->assertEquals($mockedBonuses, $result);
    }

    public function testList()
    {
        $dtoMock = Mockery::mock(BonusInfoSearchDTO::class);
        $lengthAwarePaginatorMock = Mockery::mock(LengthAwarePaginator::class);
        $this->repositoryMock->shouldReceive('searchBonusesInfo')->with($dtoMock)->once()
            ->andReturn($lengthAwarePaginatorMock);

        $result = $this->service->list($dtoMock);
        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
    }

    public function testGetBonusInfoById()
    {
        $bonusInfo = new BonusInfo();
        $this->repositoryMock->shouldReceive('getBonusInfoById')->with(1)->once()
            ->andReturn($bonusInfo);

        $result = $this->service->getBonusInfoById(1);
        $this->assertInstanceOf(BonusInfo::class, $result);
    }

    public function testCreateBonusInfo(): void
    {
        $dto = Mockery::mock(BonusInfoCreateDTO::class);
        $dto->shouldReceive('toArray')->with(false)->once()->andReturn();
        $this->repositoryMock->shouldReceive('save')->once()->andReturn(true);

        $result = $this->service->create($dto);

        $this->assertInstanceOf(BonusInfo::class, $result);
    }

    public function testUpdateBonusInfo(): void
    {
        $bonusInfo = new BonusInfo();
        $bonusInfo->id = 1;
        $dto = Mockery::mock(BonusInfoUpdateDTO::class);
        $dto->id = 1;
        $dto->shouldReceive('toArray')->with(false)->once()->andReturn();
        $this->repositoryMock->shouldReceive('save')->once()->andReturn(true);
        $this->repositoryMock->shouldReceive('getBonusInfoById')
            ->with(1)->once()->andReturn($bonusInfo);

        $result = $this->service->update($dto);

        $this->assertInstanceOf(BonusInfo::class, $result);
    }
}
