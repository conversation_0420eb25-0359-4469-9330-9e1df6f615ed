<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Enums\BonusPlayerStatusEnum;
use App\Models\Bonus;
use App\Models\BonusPlayer;
use App\Repositories\BonusPlayerRepository;
use App\Services\BonusInfoService;
use App\Services\BonusPlayerCardService;
use App\Services\BonusPlayerService;
use App\Services\Dto\PlayerDto;
use App\Services\PlayerService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Mockery;
use Mockery\MockInterface;
use Tests\TestCase;

class BonusPlayerServiceTest extends TestCase
{
    private BonusPlayerRepository&MockInterface $bonusPlayerRepository;
    private BonusPlayerCardService&MockInterface $bonusPlayerCardService;
    private BonusInfoService&MockInterface $bonusInfoService;
    private MockInterface&PlayerService $playerService;

    private BonusPlayerService $bonusPlayerService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->bonusPlayerRepository = Mockery::mock(BonusPlayerRepository::class);
        $this->bonusPlayerCardService = Mockery::mock(BonusPlayerCardService::class);
        $this->bonusInfoService = Mockery::mock(BonusInfoService::class);
        $this->playerService = Mockery::mock(PlayerService::class);

        $this->bonusPlayerService = new BonusPlayerService(
            $this->bonusPlayerRepository,
            $this->bonusPlayerCardService,
            $this->bonusInfoService,
            $this->playerService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testGetSelectedBonusByPlayerUuidWithMissingBonus(): void
    {
        $playerId = 'uuid-1';

        $playerDto = new PlayerDto([
            'id' => 1,
            'uuid' => $playerId,
            'currency' => 'USD',
        ]);

        $this->playerService
            ->shouldReceive('getPlayerByUUID')
            ->with($playerId)
            ->once()
            ->andReturn($playerDto);

        $this->bonusPlayerRepository
            ->shouldReceive('getSelectedBonusPlayerData')
            ->with($playerDto->id)
            ->once()
            ->andReturnNull();

        $this->expectException(ModelNotFoundException::class);
        $this->expectExceptionMessage(sprintf('Bonus for player with uuid %s not found', $playerId));

        $this->bonusPlayerService->getSelectedBonusByPlayerUuid($playerId);
    }

    public function testGetSelectedBonusByPlayerUuidWithExpiredBonus(): void
    {
        $playerId = 'uuid-1';

        $playerDto = new PlayerDto([
            'id' => 1,
            'uuid' => $playerId,
            'currency' => 'USD',
        ]);

        $this->playerService
            ->shouldReceive('getPlayerByUUID')
            ->with($playerId)
            ->once()
            ->andReturn($playerDto);

        $bonus = new Bonus();
        $bonus->active_from = now()->subDay();
        $bonus->active_til = now()->subDay();

        $bonusPlayer = new BonusPlayer();
        $bonusPlayer->status = BonusPlayerStatusEnum::EXPIRED->value;
        $bonusPlayer->bonus = $bonus;

        $this->bonusPlayerRepository
            ->shouldReceive('getSelectedBonusPlayerData')
            ->with($playerDto->id)
            ->once()
            ->andReturn($bonusPlayer);

        $this->expectException(ModelNotFoundException::class);
        $this->expectExceptionMessage(sprintf('Bonus for player with uuid %s is expired', $playerId));

        $this->bonusPlayerService->getSelectedBonusByPlayerUuid($playerId);
    }

    public function testGetSelectedBonusByPlayerUuidWithAlreadyExpiredBonus(): void
    {
        $playerId = 'uuid-1';

        $playerDto = new PlayerDto([
            'id' => 1,
            'uuid' => $playerId,
            'currency' => 'USD',
        ]);

        $this->playerService
            ->shouldReceive('getPlayerByUUID')
            ->with($playerId)
            ->once()
            ->andReturn($playerDto);

        $bonus = new Bonus();
        $bonus->active_from = now()->subDay();
        $bonus->active_til = now()->subDay();

        $bonusPlayer = Mockery::mock(BonusPlayer::class)->makePartial();
        $bonusPlayer->status = BonusPlayerStatusEnum::WAITING_FOR_DEPOSIT->value;
        $bonusPlayer->player_id = $playerDto->id;
        $bonusPlayer->bonus = $bonus;
        $bonusPlayer->trigger_session_id = 1;

        $this->bonusPlayerRepository
            ->shouldReceive('getSelectedBonusPlayerData')
            ->with($playerDto->id)
            ->once()
            ->andReturn($bonusPlayer);

        $bonusPlayer->shouldReceive('save')
            ->once()
            ->andReturnTrue();

        $this->playerService
            ->shouldReceive('getPlayerById')
            ->with($bonusPlayer->player_id)
            ->once()
            ->andReturn($playerDto);

        $this->expectException(ModelNotFoundException::class);
        $this->expectExceptionMessage(sprintf('Bonus for player with uuid %s is expired', $playerId));

        $this->bonusPlayerService->getSelectedBonusByPlayerUuid($playerId);
    }

    public function testGetSelectedBonusByPlayerUuidSuccess(): void
    {
        $playerId = 'uuid-1';

        $playerDto = new PlayerDto([
            'id' => 1,
            'uuid' => $playerId,
            'currency' => 'USD',
        ]);

        $this->playerService
            ->shouldReceive('getPlayerByUUID')
            ->with($playerId)
            ->once()
            ->andReturn($playerDto);

        $bonus = new Bonus();
        $bonus->active_from = now()->subDay();
        $bonus->active_til = now()->addDay();

        $bonusPlayer = Mockery::mock(BonusPlayer::class)->makePartial();
        $bonusPlayer->status = BonusPlayerStatusEnum::WAITING_FOR_DEPOSIT;
        $bonusPlayer->bonus = $bonus;
        $bonusPlayer->player_id = $playerDto->id;

        $this->bonusPlayerRepository
            ->shouldReceive('getSelectedBonusPlayerData')
            ->with($playerDto->id)
            ->once()
            ->andReturn($bonusPlayer);

        $result = $this->bonusPlayerService->getSelectedBonusByPlayerUuid($playerId);

        $this->assertSame($bonusPlayer->bonus, $result);
    }
}
