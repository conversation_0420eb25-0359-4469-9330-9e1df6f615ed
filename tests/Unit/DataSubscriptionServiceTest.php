<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Models\DataSubscriptions;
use App\Repositories\DataSubscriptionRepository;
use App\Services\DataSubscriptionService;
use Illuminate\Foundation\Testing\TestCase;
use Mockery;
use Tests\CreatesApplication;

class DataSubscriptionServiceTest extends TestCase
{
    use CreatesApplication;

    private DataSubscriptionService $service;
    private DataSubscriptionRepository $dataSubscriptionRepositoryMock;

    public function setUp(): void
    {
        parent::setUp();

        $this->dataSubscriptionRepositoryMock = Mockery::mock(DataSubscriptionRepository::class);
        $this->service = new DataSubscriptionService($this->dataSubscriptionRepositoryMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_data_subscription_service_get_by_provider_name()
    {
        $dataSubscription = new DataSubscriptions();
        $dataSubscription->id = 1;
        $dataSubscription->provider = 'betby';

        $this->dataSubscriptionRepositoryMock->shouldReceive('getByProviderName')
            ->with('betby')->once()
            ->andReturn($dataSubscription);

        /** @var DataSubscriptions $sub */
        $sub = $this->service->getByProviderName($dataSubscription->provider);

        $this->assertEquals($dataSubscription->id, $sub->id);
    }
}
