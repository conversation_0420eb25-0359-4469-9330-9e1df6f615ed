<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Jobs\FreeBetStartApplyJob;
use App\Models\Bonus;
use App\Models\MassFreeBetBonusInfo;
use App\Repositories\BonusApplyFreebetRepository;
use App\Services\BonusApplyService;
use App\Services\BonusService;
use App\Services\Dto\BonusApplyForPlayerDTO;
use App\Services\Dto\BonusApplySearchDTO;
use App\Services\Dto\MassBonusApplyDTO;
use App\Services\FreeBetBonusService;
use App\Services\PlayerService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Queue;
use Mockery;
use PHPUnit\Framework\MockObject\Exception;
use Tests\TestCase;

class FreeBetBonusServiceTest extends TestCase
{
    protected BonusApplyFreebetRepository $repository;

    protected PlayerService $playerService;

    protected BonusService $bonusService;
    protected BonusApplyService $bonusApplyService;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->repository = $this->createMock(BonusApplyFreebetRepository::class);
        $this->playerService = $this->createMock(PlayerService::class);
        $this->bonusService = $this->createMock(BonusService::class);
        $this->bonusApplyService = $this->createMock(BonusApplyService::class);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @throws Exception
     */
    public function testList(): void
    {
        $dto = $this->createMock(BonusApplySearchDTO::class);
        $paginator = $this->createMock(LengthAwarePaginator::class);

        $this->repository->expects($this->once())
            ->method('list')
            ->with($dto)
            ->willReturn($paginator);

        $service = new FreeBetBonusService(
            $this->playerService,
            $this->repository,
            $this->bonusService,
            $this->bonusApplyService
        );

        $result = $service->list($dto);

        $this->assertSame($paginator, $result);
    }

    public function testHandleDispatchesJob(): void
    {
        Queue::fake();

        $key = 'mass_bonus_apply_freebet_' . md5((string)time());

        $dto = MassBonusApplyDTO::fromArray([
            'bonus_id' => 1,
            'file' => UploadedFile::fake()->create('file.csv'),
        ]);

        $service = $this->getMockBuilder(FreeBetBonusService::class)
            ->setConstructorArgs([$this->playerService, $this->repository, $this->bonusService, $this->bonusApplyService])
            ->onlyMethods(['getSeparator'])
            ->getMock();

        $service->expects($this->once())
            ->method('getSeparator')
            ->with($dto->file)
            ->willReturn(',');

        $service->startApplyJob($dto);

        Queue::assertPushed(FreeBetStartApplyJob::class, static fn($job) => $job->bonusId === $dto->bonusId
                && $job->key === $key);
    }

    public function testApplyBonusForPlayer(): void
    {
        $dto = BonusApplyForPlayerDTO::fromArray([
            'bonus_id' => 1,
            'player_id' => 100,
            'amount' => 50,
            'smartico_bonus_id' => 200,
        ]);

        $bonus = Mockery::mock(Bonus::class)->makePartial();
        $bonus->setAttribute('bonuses', [5000]);
        $bonus->setAttribute('currency', 'USD');

        $this->bonusService->method('getBonusById')
            ->with($dto->bonusId)
            ->willReturn($bonus);

        $massFreebetBonusInfo = Mockery::mock(MassFreeBetBonusInfo::class);
        $massFreebetBonusInfo->shouldReceive('getAttribute')
            ->with('id')
            ->andReturn(123);

        $service = $this->getMockBuilder(FreeBetBonusService::class)
            ->setConstructorArgs([$this->playerService, $this->repository, $this->bonusService, $this->bonusApplyService])
            ->onlyMethods(['createBonusInfo', 'apply'])
            ->getMock();

        $service->expects($this->once())
            ->method('createBonusInfo')
            ->with([
                'bonusId' => $dto->bonusId,
                'playerId' => $dto->playerId,
                'amount' => $bonus->bonuses[0] / 100,
                'currency' => $bonus->currency,
                'smarticoBonusId' => $dto->smarticoBonusId,
            ])
            ->willReturn($massFreebetBonusInfo);

        $expectedResponse = ['status' => 'success'];

        $service->expects($this->once())
            ->method('apply')
            ->with(123)
            ->willReturn($expectedResponse);

        $result = $service->applyBonusForPlayer($dto);

        $this->assertSame($expectedResponse, $result);
    }
}
