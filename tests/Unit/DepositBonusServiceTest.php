<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Enums\BonusStatusEnum;
use App\Exceptions\SmarticoException;
use App\Models\Bonus;
use App\Models\BonusInfo;
use App\Models\BonusPlayerCard;
use App\Models\BonusTriggerSession;
use App\Models\DepositBonusInfo;
use App\Services\BonusHistoryService;
use App\Services\BonusPlayerService;
use App\Services\BonusService;
use App\Services\BonusTriggerSessionService;
use App\Services\DepositBonusService;
use App\Services\Dto\PlayerDto;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Mockery;
use Tests\CreatesApplication;

class DepositBonusServiceTest extends TestCase
{
    use CreatesApplication;
    use WithFaker;

    private DepositBonusService $depositBonusService;
    private BonusHistoryService $bonusHistoryServiceMock;
    private BonusService $bonusServiceMock;
    private BonusPlayerService $bonusPlayerServiceMock;
    private BonusTriggerSessionService $bonusTriggerSessionServiceMock;

    public function setUp(): void
    {
        parent::setUp();

        $this->bonusHistoryServiceMock = Mockery::mock(BonusHistoryService::class);
        $this->bonusServiceMock = Mockery::mock(BonusService::class);
        $this->bonusPlayerServiceMock = Mockery::mock(BonusPlayerService::class);
        $this->bonusTriggerSessionServiceMock = Mockery::mock(BonusTriggerSessionService::class);

        $this->depositBonusService = new DepositBonusService($this->bonusHistoryServiceMock,
            $this->bonusServiceMock, $this->bonusPlayerServiceMock, $this->bonusTriggerSessionServiceMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_deposit_bonus_service_apply_bonus_for_player(): void
    {
        $bonus = new Bonus();
        $bonus->id = 1;

        $player = new PlayerDto([
            'id' => 1,
            'uuid' => $this->faker->uuid,
            'currency' => 'EUR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ]);

        $smarticoBonusId = rand(10, 1000000000);

        $this->bonusHistoryServiceMock->shouldReceive('existsSuccessfulTransfer')
            ->with($smarticoBonusId)->once()->andReturn(false);

        $bonusInfo = new BonusInfo();
        $bonusInfo->id = 1;

        $depositBonusInfo = new DepositBonusInfo();
        $depositBonusInfo->id = 1;
        $depositBonusInfo->bonus_id = $bonus->id;
        $depositBonusInfo->player_id = $player->id;

        $this->bonusHistoryServiceMock->shouldReceive('createBonusInfo')
            ->with($bonus->id, $player, $smarticoBonusId)->once()->andReturn($depositBonusInfo);

        $this->bonusServiceMock->shouldReceive('getSmarticoBonusInfoByBonusId')
            ->with($bonus->id)->once()->andReturn($bonusInfo);

        $triggerSession = new BonusTriggerSession();
        $triggerSession->id = 1;
        $triggerSession->end_at = Carbon::now()->addDay()->toDateTimeString();

        $this->bonusTriggerSessionServiceMock->shouldReceive('getBonusTriggerSessionByBonusId')
            ->with($bonus->id)->once()->andReturn($triggerSession);

        $this->bonusPlayerServiceMock->shouldReceive('saveBonus')
            ->with($bonus->id, $player->id, $bonusInfo->id)->once()->andReturn(new BonusPlayerCard());

        $this->bonusHistoryServiceMock->shouldReceive('saveBonusInfoStatus')
            ->with($depositBonusInfo, 'Bonus accepted for player', BonusStatusEnum::SUCCESS_STATUS)->once();

        $response = $this->depositBonusService->applyBonusForPlayer($bonus, $player, $smarticoBonusId);

        $this->assertIsArray($response);
        $this->assertNotEmpty($response);
        $this->assertArrayHasKey('status', $response);
        $this->assertArrayHasKey('message', $response);
        $this->assertTrue($response['status']);
    }

    public function test_deposit_bonus_service_apply_throw_error(): void
    {
        $bonus = new Bonus();
        $bonus->id = 1;
        $player = new PlayerDto([
            'id' => 1,
            'uuid' => $this->faker->uuid,
            'currency' => 'EUR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ]);

        $smarticoBonusId = rand(10, 1000000000);

        $this->bonusHistoryServiceMock->shouldReceive('existsSuccessfulTransfer')
            ->with($smarticoBonusId)->once()->andReturn(true);

        $isError = false;
        try {
            $this->depositBonusService->applyBonusForPlayer($bonus, $player, $smarticoBonusId);
        } catch(SmarticoException) {
            $isError = true;
        } finally {
            $this->assertTrue($isError);
        }
    }

    public function test_deposit_bonus_service_create_bonus_info_throw_error(): void
    {
        $bonus = new Bonus();
        $bonus->id = 1;
        $player = new PlayerDto([
            'id' => 1,
            'uuid' => $this->faker->uuid,
            'currency' => 'EUR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ]);

        $smarticoBonusId = rand(10, 1000000000);

        $this->bonusHistoryServiceMock->shouldReceive('existsSuccessfulTransfer')
            ->with($smarticoBonusId)->once()->andReturn(false);

        $depositBonusInfo = new DepositBonusInfo();
        $depositBonusInfo->id = 1;
        $depositBonusInfo->bonus_id = $bonus->id;
        $depositBonusInfo->player_id = $player->id;

        $this->bonusHistoryServiceMock->shouldReceive('createBonusInfo')
            ->with($bonus->id, $player, $smarticoBonusId)->once()->andReturn($depositBonusInfo);

        $this->bonusServiceMock->shouldReceive('getSmarticoBonusInfoByBonusId')
            ->with($bonus->id)->once()->andReturn(null);

        $this->bonusHistoryServiceMock->shouldReceive('saveBonusInfoStatus')
            ->with($depositBonusInfo, 'Bonus Card is not Found in the system', BonusStatusEnum::ERROR_STATUS)
            ->once();

        $isError = false;
        try {
            $this->depositBonusService->applyBonusForPlayer($bonus, $player, $smarticoBonusId);
        } catch(SmarticoException) {
            $isError = true;
        } finally {
            $this->assertTrue($isError);
        }
    }

    public function test_deposit_bonus_service_assign_bonus_to_player_throw_error(): void
    {
        $bonus = new Bonus();
        $bonus->id = 1;
        $player = new PlayerDto([
            'id' => 1,
            'uuid' => $this->faker->uuid,
            'currency' => 'EUR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ]);

        $smarticoBonusId = rand(10, 1000000000);

        $this->bonusHistoryServiceMock->shouldReceive('existsSuccessfulTransfer')
            ->with($smarticoBonusId)->once()->andReturn(false);

        $bonusInfo = new BonusInfo();
        $bonusInfo->id = 1;

        $depositBonusInfo = new DepositBonusInfo();
        $depositBonusInfo->id = 1;
        $depositBonusInfo->bonus_id = $bonus->id;
        $depositBonusInfo->player_id = $player->id;

        $this->bonusHistoryServiceMock->shouldReceive('createBonusInfo')
            ->with($bonus->id, $player, $smarticoBonusId)->once()->andReturn($depositBonusInfo);

        $this->bonusServiceMock->shouldReceive('getSmarticoBonusInfoByBonusId')
            ->with($bonus->id)->once()->andReturn($bonusInfo);

        $triggerSession = new BonusTriggerSession();
        $triggerSession->id = 1;
        $triggerSession->end_at = Carbon::now()->subDay()->toDateTimeString();

        $this->bonusTriggerSessionServiceMock->shouldReceive('getBonusTriggerSessionByBonusId')
            ->with($bonus->id)->once()->andReturn($triggerSession);

        $this->bonusHistoryServiceMock->shouldReceive('saveBonusInfoStatus')
            ->with($depositBonusInfo, 'Bonus Card is Expired in the system', BonusStatusEnum::ERROR_STATUS)
            ->once();

        $isError = false;
        try {
            $this->depositBonusService->applyBonusForPlayer($bonus, $player, $smarticoBonusId);
        } catch(SmarticoException) {
            $isError = true;
        } finally {
            $this->assertTrue($isError);
        }
    }
}
