<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Http\Controllers\MaintenanceController;
use App\Http\Requests\Maintenance\ClearCacheRequest;
use App\Services\RealIpService;
use Illuminate\Cache\Repository;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;
use Exception;
use JsonException;

class MaintenanceControllerTest extends TestCase
{
    private ClearCacheRequest $request;
    private RealIpService $realIpService;
    private Repository $cache;

    /**
     * Set up the test environment.
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->request = Mockery::mock(ClearCacheRequest::class);
        $this->realIpService = Mockery::mock(RealIpService::class);
        $this->cache = Mockery::mock(Repository::class);
    }

    /**
     * Test the clearCache method when environment is allowed
     */
    public function testClearCacheInAllowedEnvironment(): void
    {
        App::shouldReceive('environment')
            ->once()
            ->with(['local', 'dev', 'staging'])
            ->andReturn(true);

        $this->request->tags = null;
        $this->request->keys = null;

        $this->cache->shouldReceive('flush')
            ->once()
            ->andReturn(true);

        $controller = new MaintenanceController();

        $response = $controller->clearCache($this->request, $this->realIpService, $this->cache);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertSame(200, $response->getStatusCode());
        $this->assertSame(['message' => 'Cache cleared successfully'], json_decode($response->getContent(), true));

        Mockery::close();
    }

    /**
     * Test the clearCache method when environment is not allowed
     */
    public function testClearCacheInUnauthorizedEnvironment(): void
    {
        App::shouldReceive('environment')
            ->once()
            ->with(['local', 'dev', 'staging'])
            ->andReturn(false);

        $this->realIpService->shouldReceive('getIpAddress')
            ->once()
            ->andReturn('***********');

        Log::shouldReceive('warning')
            ->once()
            ->with('Attempt to clear cache in unauthorized environment', ['ip' => '***********']);

        $controller = new MaintenanceController();

        $response = $controller->clearCache($this->request, $this->realIpService, $this->cache);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertSame(400, $response->getStatusCode());
        $this->assertSame(['message' => 'This operation is only allowed in Dev/Staging environments'], json_decode($response->getContent(), true));

        Mockery::close();
    }

    /**
     * Test the clearCache method with specific keys
     */
    public function testClearCacheWithKeys(): void
    {
        App::shouldReceive('environment')
            ->once()
            ->with(['local', 'dev', 'staging'])
            ->andReturn(true);

        $this->request->tags = null;
        $this->request->keys = ['key1', 'key2'];

        $this->cache->shouldReceive('deleteMultiple')
            ->once()
            ->with(['key1', 'key2'])
            ->andReturn(true);

        $controller = new MaintenanceController();

        $response = $controller->clearCache($this->request, $this->realIpService, $this->cache);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertSame(200, $response->getStatusCode());
        $this->assertSame(['message' => 'Cache cleared successfully'], json_decode($response->getContent(), true));

        Mockery::close();
    }

    /**
     * Test the clearCache method when an exception occurs
     *
     * @throws JsonException
     */
    public function testClearCacheWithException(): void
    {
        App::shouldReceive('environment')
            ->once()
            ->with(['local', 'dev', 'staging'])
            ->andReturn(true);

        $this->request->tags = null;
        $this->request->keys = null;

        $this->cache->shouldReceive('flush')
            ->once()
            ->andThrow(new Exception('Cache error'));

        $controller = new MaintenanceController();

        $response = $controller->clearCache($this->request, $this->realIpService, $this->cache);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertSame(400, $response->getStatusCode());
        $this->assertSame(
            ['message' => 'Failed to clear cache: Cache error'],
            json_decode($response->getContent(), true, 512, JSON_THROW_ON_ERROR)
        );

        Mockery::close();
    }
}
