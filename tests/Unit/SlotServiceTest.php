<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Clients\AppClient;
use App\Models\DataSubscriptions;
use App\Repositories\SlotRepository;
use App\Services\Dto\SlotDto;
use App\Services\SlotService;
use Illuminate\Foundation\Testing\TestCase;
use Mockery;
use Tests\CreatesApplication;

class SlotServiceTest extends TestCase
{
    use CreatesApplication;

    private SlotRepository $slotsRepositoryMock;
    private SlotService $slotServiceMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->slotsRepositoryMock = Mockery::mock(SlotRepository::class);

        $this->slotServiceMock = Mockery::mock(SlotService::class, [
            $this->slotsRepositoryMock,
        ])->makePartial();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testSlotServiceGetSlotById(): void
    {
        $slotId = 1;
        $slotDto = Mockery::mock(SlotDto::class);
        $slotServiceMock = Mockery::mock(SlotService::class)->makePartial();

        $slotServiceMock->shouldReceive('getSlotListByIds')
            ->once()
            ->with([$slotId])
            ->andReturn([$slotId => $slotDto]);

        $result = $slotServiceMock->getSlotById($slotId);

        $this->assertInstanceOf(SlotDto::class, $result);
    }

    public function testSlotServiceGetSubscriptionBySlotId(): void
    {
        $slotId = 1;
        $slotDto = Mockery::mock(SlotDto::class);
        $slotDto->slot_provider = 'test_provider';
        $subscription = Mockery::mock(DataSubscriptions::class);

        $this->slotServiceMock->shouldReceive('getSlotById')
            ->once()
            ->with($slotId)
            ->andReturn($slotDto);

        $this->slotsRepositoryMock->shouldReceive('getSubscriptionByProvider')
            ->once()
            ->with($slotDto->slot_provider)
            ->andReturn($subscription);

        $result = $this->slotServiceMock->getSubscriptionBySlotId($slotId);

        $this->assertInstanceOf(DataSubscriptions::class, $result);
    }

    public function testSlotServiceGetSubscriptionByProvider(): void
    {
        $provider = 'test_provider';
        $subscription = Mockery::mock(DataSubscriptions::class);

        $this->slotsRepositoryMock->shouldReceive('getSubscriptionByProvider')
            ->once()
            ->with($provider)
            ->andReturn($subscription);

        $result = $this->slotsRepositoryMock->getSubscriptionByProvider($provider);

        $this->assertInstanceOf(DataSubscriptions::class, $result);
    }
}
