<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Models\FreeSpin;
use App\Models\FreeSpinHistoryLog;
use App\Repositories\FreeSpinHistoryLogRepository;
use App\Services\Dto\FreeSpinHistoryLogDTO;
use App\Services\Dto\SlotDto;
use App\Services\FreeSpinHistoryLogService;
use App\Services\SlotProviderService;
use App\Services\SlotService;
use Illuminate\Pagination\LengthAwarePaginator;
use Tests\TestCase;
use Mockery;
use Mockery\MockInterface;

class FreeSpinHistoryLogServiceTest extends TestCase
{
    protected FreeSpinHistoryLogRepository|MockInterface $repository;
    protected MockInterface|SlotService $slotService;
    protected MockInterface|SlotProviderService $slotProviderService;
    protected FreeSpinHistoryLogService|MockInterface $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->repository = Mockery::mock(FreeSpinHistoryLogRepository::class);
        $this->slotService = Mockery::mock(SlotService::class);
        $this->slotProviderService = Mockery::mock(SlotProviderService::class);

        $this->service = new FreeSpinHistoryLogService(
            $this->repository,
            $this->slotService,
            $this->slotProviderService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testReturnsLogsFromRepository(): void
    {
        $dto = Mockery::mock(FreeSpinHistoryLogDTO::class);
        $paginator = Mockery::mock(LengthAwarePaginator::class);

        $this->repository
            ->shouldReceive('getLogsByFreeSpinId')
            ->once()
            ->with($dto)
            ->andReturn($paginator);

        $result = $this->service->list($dto);

        $this->assertSame($paginator, $result);
    }

    public function testCreatesLogWithDiffWhenUpdated(): void
    {
        $prev = Mockery::mock(FreeSpin::class)->makePartial();
        $current = Mockery::mock(FreeSpin::class)->makePartial();

        $prev->shouldAllowMockingProtectedMethods();
        $current->shouldAllowMockingProtectedMethods();

        $prev->shouldReceive('toArray')->andReturn([
            'name' => 'Old Spin',
            'slot_id' => 1,
            'provider_id' => 10,
        ]);
        $current->shouldReceive('toArray')->andReturn([
            'name' => 'New Spin',
            'slot_id' => 2,
            'provider_id' => 20,
        ]);

        $prev->setAttribute('bonus', null);
        $current->setAttribute('bonus', null);

        $prev->slot_id = 1;
        $current->slot_id = 2;
        $prev->provider_id = 10;
        $current->provider_id = 20;
        $current->id = 100;

        $slotDto1 = Mockery::mock(SlotDto::class);
        $slotDto2 = Mockery::mock(SlotDto::class);
        $slotDto1->id = 1;
        $slotDto2->id = 2;
        $slotDto1->name = 'Slot A';
        $slotDto2->name = 'Slot B';

        $this->slotService
            ->shouldReceive('getSlotById')
            ->with(1)->andReturn($slotDto2);
        $this->slotService
            ->shouldReceive('getSlotById')
            ->with(2)->andReturn($slotDto2);

        $this->slotProviderService
            ->shouldReceive('getSlotProvidersByIds')
            ->with([10])
            ->andReturn([10 => (object)['id' => 10, 'name' => 'Provider X']]);
        $this->slotProviderService
            ->shouldReceive('getSlotProvidersByIds')
            ->with([20])
            ->andReturn([20 => (object)['id' => 20, 'name' => 'Provider Y']]);

        $mock = Mockery::mock('alias:' . FreeSpinHistoryLog::class);
        $mock->shouldReceive('on')
            ->with('mysql.bonus')
            ->andReturnSelf();
        $mock->shouldReceive('create')->once()->with(Mockery::on(static fn($data) => $data['type'] === 'update'
                && $data['free_spin_id'] === 100
                && $data['author_email'] === '<EMAIL>'
                && isset($data['data']['diff'])));

        $this->service->createLog($prev, $current, '<EMAIL>');

        $this->assertNotEmpty($current->toArray());
    }

    public function testCreatesLogWithTypeCreateWhenNoPrev(): void
    {
        $current = Mockery::mock(FreeSpin::class)->makePartial();
        $current->shouldAllowMockingProtectedMethods();
        $current->shouldReceive('toArray')->andReturn(['name' => 'New Spin']);
        $current->setAttribute('bonus', null);
        $current->slot_id = 0;
        $current->provider_id = 0;
        $current->id = 101;

        $mock = Mockery::mock('alias:' . FreeSpinHistoryLog::class);
        $mock->shouldReceive('on')
            ->with('mysql.bonus')
            ->andReturnSelf();
        $mock->shouldReceive('create')->once()->with(Mockery::on(static fn($data) => $data['type'] === 'create'
            && $data['free_spin_id'] === 101
            && $data['author_email'] === '<EMAIL>'));

        $this->service->createLog(null, $current, '<EMAIL>');

        $this->assertNotEmpty($current->toArray());
    }
}
