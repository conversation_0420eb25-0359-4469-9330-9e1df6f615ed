<?php

namespace Tests\Unit;

use App\Clients\AppClient;
use App\Enums\BonusBalanceStatusEnum;
use App\Enums\BonusTypeEnum;
use App\Exceptions\Exception;
use App\Models\Bonus;
use App\Models\IssuedBonus;
use App\Repositories\BonusBalanceRepository;
use App\Services\BetbyService;
use App\Services\BonusBalanceService;
use App\Services\BonusPlayerService;
use App\Services\Dto\PlayerDto;
use App\Services\PromoCodeService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\WithFaker;
use Mockery;
use Tests\TestCase;

class BonusBalanceTest extends TestCase
{
    use WithFaker;

    private BonusBalanceRepository $bonusBalanceRepository;
    private BonusPlayerService $bonusPlayerService;
    private BetbyService $betbyService;
    private BonusBalanceService $bonusBalanceService;
    private AppClient $appClient;

    public function setUp(): void
    {
        parent::setUp();

        $this->bonusBalanceRepository = Mockery::mock(BonusBalanceRepository::class);
        $this->bonusPlayerService = Mockery::mock(BonusPlayerService::class);
        $this->betbyService = Mockery::mock(BetbyService::class);
        $this->appClient = Mockery::mock(AppClient::class);

        $this->bonusBalanceService = Mockery::mock(bonusBalanceService::class, [
            $this->bonusBalanceRepository,
            $this->betbyService,
            $this->bonusPlayerService,
            $this->appClient,
        ])->makePartial();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @throws Exception
     */
    public function testCreateBalanceForInternalBonusNoFactor()
    {
        $player = new PlayerDto([
            'id' => 1,
            'uuid' => $this->faker->uuid,
            'currency' => 'EUR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ]);

        $bonus = new Bonus();
        $bonus->setAttribute('id', 1);
        $bonus->setAttribute('type', BonusTypeEnum::ONETIME->value);
        $bonus->setAttribute('bonuses', [100]);
        $bonus->setAttribute('max_transfers', [50]);
        $bonus->setAttribute('deposit_factors', [null]);
        $bonus->setAttribute('wager', 10);
        $bonus->setAttribute('currency', 'EUR');
        $bonus->setAttribute('casino', true);
        $bonus->setAttribute('bets', false);
        $bonus->setAttribute('duration', null);
        $bonus->setAttribute('data', []);

        $issuedBonus = new IssuedBonus();
        $issuedBonus->setAttribute('id', 1);
        $issuedBonus->setAttribute('bonus_id', $bonus->id);
        $issuedBonus->setAttribute('player_id', $player->id);
        $issuedBonus->setAttribute('balance', 100);
        $issuedBonus->setAttribute('orig_bonus', 100);
        $issuedBonus->setAttribute('orig_wager', 1000);
        $issuedBonus->setAttribute('wager', 1000);
        $issuedBonus->setAttribute('currency', 'EUR');
        $issuedBonus->setAttribute('status', BonusBalanceStatusEnum::PENDING->value);
        $issuedBonus->setAttribute('expire_at', Carbon::now()->addMonth());
        $issuedBonus->setAttribute('type', BonusTypeEnum::ONETIME->value);
        $issuedBonus->setAttribute('min_bet', 0);
        $issuedBonus->setAttribute('in_game', 0);
        $issuedBonus->setAttribute('active', true);
        $issuedBonus->setAttribute('casino', true);
        $issuedBonus->setAttribute('bets', false);
        $issuedBonus->setAttribute('transfer', 100);


        $this->bonusBalanceService->shouldReceive('prepareIssuedBonusModel')->once()
            ->with($bonus, $player, Mockery::any(), Mockery::any())
            ->andReturn($issuedBonus);

        $this->bonusBalanceRepository->shouldReceive('save')->once()->with($issuedBonus);

        $this->appClient->shouldReceive('issueBonus')->once()->andReturn(['id' => 123]);

        $promoCodeService = Mockery::mock(PromoCodeService::class);
        $promoCodeService->shouldReceive('attachBonusBalance')->once();
        $this->app->instance(PromoCodeService::class, $promoCodeService);

        $this->bonusPlayerService->shouldReceive('clearPlayerCache')->once()->with($player->uuid);

        $result = $this->bonusBalanceService->createBalanceFromBonus($bonus, $player, 50);

        $this->assertInstanceOf(IssuedBonus::class, $result);
    }

    /**
     * @throws Exception
     */
    public function testCreateBalanceForExternalBonusWithFactorAndDuration()
    {
        $player = new PlayerDto([
            'id' => 1,
            'uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'currency' => 'EUR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ]);

        $bonus = new Bonus();
        $bonus->setAttribute('id', 1);
        $bonus->setAttribute('type', BonusTypeEnum::FREE_BET->value);
        $bonus->setAttribute('bonuses', [200]);
        $bonus->setAttribute('max_transfers', [150]);
        $bonus->setAttribute('deposit_factors', [1.5]);
        $bonus->setAttribute('wager', 10);
        $bonus->setAttribute('currency', 'EUR');
        $bonus->setAttribute('casino', true);
        $bonus->setAttribute('bets', false);
        $bonus->setAttribute('duration', 3600);
        $bonus->setAttribute('data', ['external_id' => 12345]);
        $bonus->setAttribute('is_external', true);
        $bonus->setAttribute('active_til', Carbon::now()->addMonth());

        $issuedBonus = new IssuedBonus();
        $issuedBonus->setAttribute('id', 1);
        $issuedBonus->setAttribute('bonus_id', $bonus->id);
        $issuedBonus->setAttribute('player_id', $player->id);
        $issuedBonus->setAttribute('bonus_external_id', $bonus->data['external_id']);
        $issuedBonus->setAttribute('balance', 100);
        $issuedBonus->setAttribute('orig_bonus', 100);
        $issuedBonus->setAttribute('orig_wager', 1000);
        $issuedBonus->setAttribute('wager', 1000);
        $issuedBonus->setAttribute('currency', 'EUR');
        $issuedBonus->setAttribute('status', BonusBalanceStatusEnum::PENDING->value);
        $issuedBonus->setAttribute('expire_at', Carbon::now()->addMonth());
        $issuedBonus->setAttribute('type', BonusTypeEnum::ONETIME->value);
        $issuedBonus->setAttribute('min_bet', 0);
        $issuedBonus->setAttribute('in_game', 0);
        $issuedBonus->setAttribute('active', true);
        $issuedBonus->setAttribute('casino', true);
        $issuedBonus->setAttribute('bets', false);
        $issuedBonus->setAttribute('transfer', 100);

        $this->bonusBalanceService->shouldReceive('prepareIssuedBonusModel')->once()
            ->with($bonus, $player, Mockery::any(), Mockery::any())
            ->andReturn($issuedBonus);

        $this->bonusBalanceRepository->shouldReceive('save')->once()->with($issuedBonus);

        $this->appClient->shouldReceive('issueBonus')->once()->andReturn(['id' => 123]);

        $promoCodeServiceMock = Mockery::mock(PromoCodeService::class);
        $promoCodeServiceMock->shouldReceive('attachBonusBalance')->once();
        $this->app->instance(PromoCodeService::class, $promoCodeServiceMock);

        $this->bonusPlayerService->shouldReceive('clearPlayerCache')->once()->with($player->uuid);

        $result = $this->bonusBalanceService->createBalanceFromBonus($bonus, $player, 50);
        $this->assertInstanceOf(IssuedBonus::class, $result);
    }
}
