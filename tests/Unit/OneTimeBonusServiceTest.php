<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Jobs\OneTimeBonusStartApplyJob;
use App\Models\MassOnetimeBonusInfo;
use App\Repositories\BonusApplyOnetimeRepository;
use App\Services\BonusApplyService;
use App\Services\BonusService;
use App\Services\Dto\BonusApplyForPlayerDTO;
use App\Services\Dto\BonusApplySearchDTO;
use App\Services\Dto\MassBonusApplyDTO;
use App\Services\OneTimeBonusService;
use App\Services\PlayerService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\MockObject\Exception;
use Tests\TestCase;

class OneTimeBonusServiceTest extends TestCase
{
    protected BonusApplyOnetimeRepository $repository;
    protected PlayerService $playerService;
    protected BonusService $bonusService;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->repository = $this->createMock(BonusApplyOnetimeRepository::class);
        $this->playerService = $this->createMock(PlayerService::class);
        $this->bonusService = $this->createMock(BonusService::class);
        $this->bonusApplyService = $this->createMock(BonusApplyService::class);
    }

    /**
     * @throws Exception
     */
    public function testList(): void
    {
        $dto = $this->createMock(BonusApplySearchDTO::class);
        $paginator = $this->createMock(LengthAwarePaginator::class);

        $this->repository->expects($this->once())
            ->method('list')
            ->with($dto)
            ->willReturn($paginator);

        $service = new OneTimeBonusService(
            $this->playerService,
            $this->repository,
            $this->bonusService,
            $this->bonusApplyService
        );

        $result = $service->list($dto);

        $this->assertSame($paginator, $result);
    }

    public function testHandleDispatchesJob(): void
    {
        Queue::fake();

        $key = 'mass_bonus_apply_' . md5((string)time());

        $dto = MassBonusApplyDTO::fromArray([
            'bonus_id' => 1,
            'file' => UploadedFile::fake()->create('file.csv'),
        ]);

        $service = $this->getMockBuilder(OneTimeBonusService::class)
            ->setConstructorArgs([$this->playerService, $this->repository, $this->bonusService, $this->bonusApplyService])
            ->onlyMethods(['getSeparator'])
            ->getMock();

        $service->expects($this->once())
            ->method('getSeparator')
            ->with($dto->file)
            ->willReturn(',');

        $service->startApplyJob($dto);

        Queue::assertPushed(OneTimeBonusStartApplyJob::class, static fn($job) => $job->bonusId === $dto->bonusId
                && $job->key === $key);
    }

    /**
     * @throws \Exception
     */
    public function testApplyBonusForPlayer(): void
    {
        $dto = BonusApplyForPlayerDTO::fromArray([
            'bonus_id' => 1,
            'player_id' => 100,
            'amount' => 50,
            'smartico_bonus_id' => 200,
        ]);

        $massOnetimeBonusInfo = new MassOnetimeBonusInfo();
        $massOnetimeBonusInfo->id = 123;

        $service = $this->getMockBuilder(OneTimeBonusService::class)
            ->setConstructorArgs([$this->playerService, $this->repository, $this->bonusService, $this->bonusApplyService])
            ->onlyMethods(['createBonusInfo', 'apply'])
            ->getMock();

        $service->expects($this->once())
            ->method('createBonusInfo')
            ->with([
                'bonusId' => $dto->bonusId,
                'playerId' => $dto->playerId,
                'amount' => $dto->amount,
                'smarticoBonusId' => $dto->smarticoBonusId,
            ])
            ->willReturn($massOnetimeBonusInfo);

        $expectedResponse = ['status' => 'success'];

        $service->expects($this->once())
            ->method('apply')
            ->with($massOnetimeBonusInfo->id)
            ->willReturn($expectedResponse);

        $result = $service->applyBonusForPlayer($dto);

        $this->assertSame($expectedResponse, $result);
    }
}
