<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Exceptions\SmarticoException;
use App\Models\Bonus;
use App\Repositories\SmarticoFreeSpinRepository;
use App\Services\BonusService;
use App\Services\CacheService;
use App\Services\DepositBonusService;
use App\Services\Dto\PlayerDto;
use App\Services\Dto\SmarticoIssueBonusToPlayerDTO;
use App\Services\FreeBetBonusService;
use App\Services\FreeSpinBoundService;
use App\Services\FreeSpinService;
use App\Services\GamicorpFreeSpinBoundService;
use App\Services\OneTimeBonusService;
use App\Services\PlayerService;
use App\Services\SmarticoService;
use Exception;
use Illuminate\Foundation\Testing\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\App;
use Mockery;
use Tests\CreatesApplication;

class SmarticoServiceTest extends TestCase
{
    use CreatesApplication;
    use WithFaker;

    private SmarticoService $smarticoService;
    private FreeBetBonusService $freeBetBonusServiceMock;
    private OneTimeBonusService $oneTimeBonusServiceMock;
    private DepositBonusService $depositBonusServiceMock;
    private PlayerService $playerServiceMock;
    private BonusService $bonusServiceMock;
    private FreeSpinService $freeSpinServiceMock;
    private GamicorpFreeSpinBoundService $gamicorpFreeSpinBoundServiceMock;
    private FreeSpinBoundService $freeSpinBoundServiceMock;
    private SmarticoFreeSpinRepository $smarticoFreeSpinRepository;

    public function setUp(): void
    {
        parent::setUp();

        $this->freeBetBonusServiceMock = Mockery::mock(FreeBetBonusService::class);
        $this->oneTimeBonusServiceMock = Mockery::mock(OneTimeBonusService::class);
        $this->depositBonusServiceMock = Mockery::mock(DepositBonusService::class);
        $this->playerServiceMock = Mockery::mock(PlayerService::class);
        $this->bonusServiceMock = Mockery::mock(BonusService::class);
        $this->freeSpinServiceMock = Mockery::mock(FreeSpinService::class);
        $this->freeSpinBoundServiceMock = Mockery::mock(FreeSpinBoundService::class);
        $this->gamicorpFreeSpinBoundServiceMock = Mockery::mock(GamicorpFreeSpinBoundService::class);
        $this->smarticoFreeSpinRepository = Mockery::mock(SmarticoFreeSpinRepository::class);

        $this->smarticoService = new SmarticoService(
            $this->freeBetBonusServiceMock,
            $this->oneTimeBonusServiceMock,
            $this->depositBonusServiceMock,
            $this->playerServiceMock,
            $this->bonusServiceMock,
            $this->freeSpinServiceMock,
            $this->gamicorpFreeSpinBoundServiceMock,
            $this->freeSpinBoundServiceMock,
            $this->smarticoFreeSpinRepository
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @throws Exception
     */
    public function testHandleRequestOneTime(): void
    {
        $dto = new SmarticoIssueBonusToPlayerDTO([
            'bonus_id' => 1,
            'user_id' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'smartico_bonus_id' => 1,
            'amount' => 100,
        ]);

        $bonus = new Bonus();
        $bonus->id = 1;
        $bonus->type = 'onetime';
        $bonus->currency = 'INR';

        $player = new PlayerDto([
            'id' => 1,
            'uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'currency' => 'INR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ]);

        $this->bonusServiceMock->shouldReceive('getBonusForSmarticoById')
            ->with($dto->bonusId)->once()->andReturn($bonus);
        $this->playerServiceMock->shouldReceive('getFirstPlayerByUUID')
            ->with($dto->userId)->once()->andReturn($player);

        $this->oneTimeBonusServiceMock->shouldReceive('applyBonusForPlayer')
            ->once()->andReturn([
                'status' => true,
                'message' => 'Bonus applied successfully',
            ]);

        $response = $this->smarticoService->issueBonus($dto);
        $this->makeAsserts($response);
    }

    /**
     * @throws Exception
     */
    public function testHandleRequestFreeMoney(): void
    {
        $dto = new SmarticoIssueBonusToPlayerDTO([
            'bonus_id' => 1,
            'user_id' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'smartico_bonus_id' => 1,
            'amount' => 100,
        ]);

        $bonus = new Bonus();
        $bonus->id = 1;
        $bonus->type = 'free_money';
        $bonus->currency = 'INR';

        $player = new PlayerDto([
            'id' => 1,
            'uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'currency' => 'INR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ]);

        $this->bonusServiceMock->shouldReceive('getBonusForSmarticoById')
            ->with($dto->bonusId)->once()->andReturn($bonus);
        $this->playerServiceMock->shouldReceive('getFirstPlayerByUUID')
            ->with($dto->userId)->once()->andReturn($player);
        $this->freeBetBonusServiceMock->shouldReceive('applyBonusForPlayer')
            ->once()->andReturn([
                'status' => true,
                'message' => 'Bonus applied successfully',
            ]);

        $response = $this->smarticoService->issueBonus($dto);
        $this->makeAsserts($response);
    }

    /**
     * @throws Exception
     */
    public function testHandleRequestDeposit(): void
    {
        $dto = new SmarticoIssueBonusToPlayerDTO([
            'bonus_id' => 1,
            'user_id' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'smartico_bonus_id' => 1,
            'amount' => 100,
        ]);

        $bonus = new Bonus();
        $bonus->id = 1;
        $bonus->type = 'deposit';
        $bonus->currency = 'INR';

        $player = new PlayerDto([
            'id' => 1,
            'uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'currency' => 'INR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ]);

        $this->bonusServiceMock->shouldReceive('getBonusForSmarticoById')
            ->with($dto->bonusId)->once()->andReturn($bonus);
        $this->playerServiceMock->shouldReceive('getFirstPlayerByUUID')
            ->with($dto->userId)->once()->andReturn($player);

        $this->depositBonusServiceMock->shouldReceive('applyBonusForPlayer')
            ->with($bonus, $player, $dto->smarticoBonusId)->once()->andReturn([
                'status' => true,
                'message' => 'Bonus applied successfully',
            ]);

        $response = $this->smarticoService->issueBonus($dto);
        $this->makeAsserts($response);
    }

    /**
     * @throws Exception
     */
    public function testHandleRequestEmptyBonus(): void
    {
        $dto = new SmarticoIssueBonusToPlayerDTO([
            'bonus_id' => 1,
            'user_id' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'smartico_bonus_id' => 1,
        ]);

        $player = new PlayerDto([
            'id' => 1,
            'uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'currency' => 'INR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ]);

        $this->bonusServiceMock->shouldReceive('getBonusForSmarticoById')
            ->with($dto->bonusId)->once()->andReturn(null);
        $this->playerServiceMock->shouldReceive('getFirstPlayerByUUID')
            ->with($dto->userId)->once()->andReturn($player);

        $isError = false;
        try {
            $this->smarticoService->issueBonus($dto);
        } catch (SmarticoException) {
            $isError = true;
        } finally {
            $this->assertTrue($isError);
        }
    }

    /**
     * @throws Exception
     */
    public function testHandleRequestNotAllowedBonusType(): void
    {
        $dto = new SmarticoIssueBonusToPlayerDTO([
            'bonus_id' => 1,
            'user_id' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'smartico_bonus_id' => 1,
            'amount' => 100,
        ]);

        $bonus = new Bonus();
        $bonus->id = 1;
        $bonus->type = 'deposit_test';
        $bonus->currency = 'INR';

        $player = new PlayerDto([
            'id' => 1,
            'uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'currency' => 'INR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ]);

        $this->bonusServiceMock->shouldReceive('getBonusForSmarticoById')
            ->with($dto->bonusId)->once()->andReturn($bonus);
        $this->playerServiceMock->shouldReceive('getFirstPlayerByUUID')
            ->with($dto->userId)->once()->andReturn($player);

        $this->expectException(SmarticoException::class);
        $this->expectExceptionMessage('Bonus type is not allowed');

        $this->smarticoService->issueBonus($dto);
    }

    /**
     * @throws Exception
     */
    public function testHandleRequestEmptyPlayer(): void
    {
        $dto = new SmarticoIssueBonusToPlayerDTO([
            'bonus_id' => 1,
            'user_id' => '',
            'smartico_bonus_id' => 1,
            'amount' => 100,
        ]);

        $bonus = new Bonus();
        $bonus->id = 1;
        $bonus->type = 'deposit';
        $bonus->currency = 'INR';

        $this->bonusServiceMock->shouldReceive('getBonusForSmarticoById')
            ->with($dto->bonusId)->once()->andReturn($bonus);

        $cacheServiceMock  = Mockery::mock(CacheService::class);
        $cacheServiceMock->shouldReceive('getCachedItemsAndFetchMissing')
            ->once()->andReturn([]);
        $this->instance(CacheService::class, $cacheServiceMock);

        $smarticoService = new SmarticoService(
            $this->freeBetBonusServiceMock,
            $this->oneTimeBonusServiceMock,
            $this->depositBonusServiceMock,
            App::make(PlayerService::class),
            $this->bonusServiceMock,
            $this->freeSpinServiceMock,
            $this->gamicorpFreeSpinBoundServiceMock,
            $this->freeSpinBoundServiceMock,
            $this->smarticoFreeSpinRepository
        );

        $this->expectException(SmarticoException::class);
        $this->expectExceptionMessage('Player Not Found');

        $smarticoService->issueBonus($dto);
    }

    /**
     * @throws Exception
     */
    public function testHandleRequestDifferentCurrency(): void
    {
        $dto = new SmarticoIssueBonusToPlayerDTO([
            'bonus_id' => 1,
            'user_id' => '',
            'smartico_bonus_id' => 1,
            'amount' => 100,
        ]);

        $bonus = new Bonus();
        $bonus->id = 1;
        $bonus->type = 'deposit';
        $bonus->currency = 'INR';

        $player = new PlayerDto([
            'id' => 1,
            'uuid' => 'test',
            'currency' => 'EUR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ]);

        $this->bonusServiceMock->shouldReceive('getBonusForSmarticoById')
            ->with($dto->bonusId)->once()->andReturn($bonus);
        $this->playerServiceMock->shouldReceive('getFirstPlayerByUUID')
            ->with($dto->userId)->once()->andReturn($player);

        $this->expectException(SmarticoException::class);
        $this->expectExceptionMessage('Player and Bonus Currencies doesnt match');

        $this->smarticoService->issueBonus($dto);
    }

    /**
     * @throws Exception
     */
    public function testHandleRequestBlockedPlayer(): void
    {
        $dto = new SmarticoIssueBonusToPlayerDTO([
            'bonus_id' => 1,
            'user_id' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'smartico_bonus_id' => 1,
            'amount' => 100,
        ]);

        $bonus = new Bonus();
        $bonus->id = 1;
        $bonus->type = 'deposit';
        $bonus->currency = 'INR';

        $player = new PlayerDto([
            'id' => 1,
            'uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'currency' => 'INR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
            'blocked' => true
        ]);

        $this->bonusServiceMock->shouldReceive('getBonusForSmarticoById')
            ->with($dto->bonusId)->once()->andReturn($bonus);
        $this->playerServiceMock->shouldReceive('getFirstPlayerByUUID')
            ->with($dto->userId)->once()->andReturn($player);

        $this->expectException(SmarticoException::class);
        $this->expectExceptionMessage('Player is blocked');

        $this->smarticoService->issueBonus($dto);
    }

    private function makeAsserts(array $response): void
    {
        $this->assertIsArray($response);
        $this->assertNotEmpty($response);
        $this->assertArrayHasKey('status', $response);
        $this->assertArrayHasKey('message', $response);
        $this->assertTrue($response['status']);
    }
}
