<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Services\CacheService;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class CacheServiceTest extends TestCase
{
    protected CacheService $cacheService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->cacheService = new CacheService();
    }

    /**
     * Test the getCachedItemsAndFetchMissing method
     * whereby the cache is initially empty,
     * and the fetchMissingItems callback function successfully retrieves the data.
     */
    public function testGetCachedItemsAndFetchMissingWithEmptyCache(): void
    {
        $cacheTag = 'testTag';
        $cacheKeyField = 'id';
        $keys = ['key1', 'key2'];
        $fetchMissingItems = static fn(...$keys) => array_map(static fn($key): array => ['id' => $key, 'value' => 'value_' . $key], $keys);

        // Empty the cache initially
        Cache::shouldReceive('many')->andReturn([]);

        // Simulate successful fetch
        Cache::shouldReceive('putMany')->andReturn(true);

        $result = $this->cacheService->getCachedItemsAndFetchMissing(
            $cacheTag,
            $cacheKeyField,
            $keys,
            $fetchMissingItems
        );

        $this->assertSame(
            [
                'key1' => ['id' => 'key1', 'value' => 'value_key1'],
                'key2' => ['id' => 'key2', 'value' => 'value_key2'],
            ],
            $result
        );
    }

    /**
     * Test the getCachedItemsAndFetchMissing method
     * whereby the cache already contains some items,
     * but there are also some missing items that need to be fetched.
     */
    public function testGetCachedItemsAndFetchMissingWithPartialCacheHit(): void
    {
        $cacheTag = 'testTag';
        $cacheKeyField = 'id';
        $keys = ['key1', 'key2', 'key3'];
        $fetchMissingItems = static fn($key) => [
            ['id' => $key, 'value' => 'value_' . $key],
        ];

        // Simulate a scenario where key1 and key2 already exist in the cache
        Cache::shouldReceive('many')->andReturn([
            'key1' => ['id' => 'key1', 'value' => 'value_key1'],
            'key2' => ['id' => 'key2', 'value' => 'value_key2'],
        ]);

        // Simulate successful fetch for key3
        Cache::shouldReceive('putMany')->andReturn(true);

        $result = $this->cacheService->getCachedItemsAndFetchMissing(
            $cacheTag,
            $cacheKeyField,
            $keys,
            $fetchMissingItems
        );

        $this->assertSame(
            [
                'key1' => ['id' => 'key1', 'value' => 'value_key1'],
                'key2' => ['id' => 'key2', 'value' => 'value_key2'],
                'key3' => ['id' => 'key3', 'value' => 'value_key3'],
            ],
            $result
        );
    }
}
