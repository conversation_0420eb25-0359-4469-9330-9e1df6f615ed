<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Clients\AppClient;
use App\Models\UserBalance;
use App\Repositories\PlayerRepository;
use App\Services\CacheService;
use App\Services\PlayerService;
use Illuminate\Foundation\Testing\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cache;
use JsonException;
use Mockery;
use Tests\CreatesApplication;

class PlayerServiceTest extends TestCase
{
    use CreatesApplication;
    use WithFaker;

    private PlayerService $playerService;
    private PlayerRepository $playerRepositoryMock;
    private CacheService $cacheServiceMock;

    public function setUp(): void
    {
        parent::setUp();

        $this->playerRepositoryMock = Mockery::mock(PlayerRepository::class);
        $this->cacheServiceMock = Mockery::mock(CacheService::class);
        $this->appClient = Mockery::mock(AppClient::class);

        $this->playerService = new PlayerService(
            $this->playerRepositoryMock,
            $this->cacheServiceMock,
            $this->appClient
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_player_service_get_player_by_uuid(): void
    {
        $playerData = [
            'id' => 1,
            'uuid' => 'test_uuid',
            'currency' => 'USD',
            'email' => '<EMAIL>',
            'username' => 'test',
            'first_name' => 'Test',
        ];

        $this->cacheServiceMock->shouldReceive('getCachedItemsAndFetchMissing')
            ->andReturn([$playerData]);

        $response = $this->playerService->getPlayerByUUID($playerData['uuid']);

        $this->assertEquals($playerData['uuid'], $response->uuid);
    }

    public function test_player_service_get_player_by_id(): void
    {
        $playerData = [
            'id' => 1,
            'uuid' => 'test_uuid',
            'currency' => 'USD',
            'email' => '<EMAIL>',
            'username' => 'test',
            'first_name' => 'Test',
        ];

        $this->cacheServiceMock->shouldReceive('getCachedItemsAndFetchMissing')
            ->andReturn([$playerData]);

        $response = $this->playerService->getPlayerByID($playerData['id']);

        $this->assertEquals($playerData['id'], $response->id);
    }

    public function test_player_service_get_balance_by_player_id(): void
    {
        $userBalance = new UserBalance();
        $userBalance->id = 1;
        $userBalance->player_id = 1;

        $this->playerRepositoryMock->shouldReceive('getBalanceByPlayerID')
            ->with($userBalance->player_id)->once()->andReturn($userBalance);

        $response = $this->playerService->getBalanceByPlayerID($userBalance->player_id);

        $this->assertEquals($userBalance->id, $response->id);
        $this->assertEquals($userBalance->player_id, $response->player_id);
    }

    /**
     * @throws JsonException
     */
    public function test_player_service_get_player_data_by_token(): void
    {
        $token = 'simple_token_test' . rand(1, 10000000);

        $player = [
            'id' => 1,
            'uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'currency' => 'INR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ];

        $this->appClient->shouldReceive('getUserInfoByToken')->with($token, [])->once()->andReturn($player);

        $response = $this->playerService->getPlayerDataByToken($token);

        $this->assertIsObject($response);
        $this->assertNotEmpty($response);
    }

    /**
     * @throws JsonException
     */
    public function test_player_service_get_player_data_by_token2(): void
    {
        $token = 'simple_token_test' . rand(1, 10000000);

        $player = [
            'id' => 1,
            'uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'currency' => 'INR',
            'email' => $this->faker->email,
            'username' => $this->faker->userName,
            'first_name' => $this->faker->firstName,
        ];

        Cache::shouldReceive('tags')->with('cache:players_token')->andReturnSelf();
        Cache::shouldReceive('remember')->andReturn($player);

        $this->appClient->shouldReceive('getUserInfoByToken')->with($token)->never();

        $response = $this->playerService->getPlayerDataByToken($token);

        $this->assertIsObject($response);
        $this->assertNotEmpty($response);
    }

    /**
     * @throws JsonException
     */
    public function test_player_service_get_player_data_by_token3(): void
    {
        $token = 'simple_token_test' . rand(1, 10000000);

        $this->appClient->shouldReceive('getUserInfoByToken')->with($token, [])->once()->andReturn(null);

        $response = $this->playerService->getPlayerDataByToken($token);

        $this->assertNull($response);
    }
}
