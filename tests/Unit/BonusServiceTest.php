<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Enums\BonusTypeEnum;
use App\Enums\SupplierEnum;
use App\Exceptions\ApiException;
use App\Exceptions\Exception;
use App\Exceptions\InternalLogicException;
use App\Exceptions\InvalidArgumentException;
use App\Models\Bonus;
use App\Models\BonusInfo;
use App\Models\BonusTriggerSession;
use App\Models\FreeSpin;
use App\Repositories\BonusRepository;
use App\Services\BetbyService;
use App\Services\BonusApplyService;
use App\Services\BonusService;
use App\Services\BonusTriggerSessionService;
use App\Services\Dto\BetbyBonusDTO;
use App\Services\Dto\BetbySearchDTO;
use App\Services\Dto\BonusApplyDTO;
use App\Services\Dto\BonusCrudDto;
use App\Services\Dto\BonusSearchDto;
use App\Services\Dto\FreeSpinCrudDto;
use App\Services\Dto\IsWagerPayableDTO;
use App\Services\Dto\PlayerDto;
use App\Services\FreeSpinService;
use App\Services\PlayerService;
use App\Services\SlotProviderService;
use App\Services\SlotService;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Testing\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Mockery;
use Mockery\MockInterface;
use ReflectionException;
use ReflectionMethod;
use Tests\CreatesApplication;

class BonusServiceTest extends TestCase
{
    use CreatesApplication;
    use WithFaker;

    private BonusService $bonusService;
    private BonusRepository&MockInterface $repositoryMock;
    private BonusTriggerSessionService&MockInterface $bonusTriggerSessionServiceMock;
    private MockInterface&PlayerService $playerService;
    private BonusApplyService&MockInterface $bonusApplyService;
    private BetbyService&MockInterface $betbyService;
    private MockInterface&SlotService $slotServiceMock;
    private MockInterface&SlotProviderService $slotProviderServiceMock;
    private BonusService&MockInterface $mockBonusService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->repositoryMock = Mockery::mock(BonusRepository::class);
        $this->bonusTriggerSessionServiceMock = Mockery::mock(BonusTriggerSessionService::class);
        $this->playerService = Mockery::mock(PlayerService::class);
        $this->bonusApplyService = Mockery::mock(BonusApplyService::class);
        $this->betbyService = Mockery::mock(BetbyService::class);
        $this->slotServiceMock = Mockery::mock(SlotService::class);
        $this->slotProviderServiceMock = Mockery::mock(SlotProviderService::class);

        $this->bonusService = new BonusService(
            $this->repositoryMock,
            $this->bonusTriggerSessionServiceMock,
            $this->playerService,
            $this->bonusApplyService,
            $this->betbyService,
            $this->slotServiceMock,
            $this->slotProviderServiceMock
        );

        $this->mockBonusService = Mockery::mock(BonusService::class, [
            $this->repositoryMock, $this->bonusTriggerSessionServiceMock,
            $this->playerService, $this->bonusApplyService, $this->betbyService,
            $this->slotServiceMock, $this->slotProviderServiceMock,
        ])->makePartial()->shouldAllowMockingProtectedMethods();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testBonusServiceGetBonusId(): void
    {
        $bonus = new Bonus();
        $bonus->id = 1;
        $bonus->client_id = 2;

        $this->repositoryMock
            ->shouldReceive('getBonusById')->with(1)
            ->once()
            ->andReturn($bonus);

        $response = $this->bonusService->getBonusById($bonus->id);

        if (!$response) {
            $this->fail();
        }

        $bonusResponseArray = $response->toArray();

        $this->assertSame($bonus->id, $bonusResponseArray['id']);
        $this->assertSame($bonus->client_id, $bonusResponseArray['client_id']);
    }

    public function testBonusServiceGetActiveBonusTriggerSessionByBonusId(): void
    {
        $bonusTriggerSession = new BonusTriggerSession();
        $bonusTriggerSession->id = 1;
        $bonusTriggerSession->bonus_id = 1;

        $carbonNow = Carbon::now();
        $carbon = Mockery::mock(Carbon::class);
        $carbon->shouldReceive('now')->andReturn($carbonNow);
        $carbon->shouldReceive('format')
            ->with('Y-m-d H:i:s')->andReturn($carbonNow->format('Y-m-d H:i:s'));

        Carbon::setTestNow($carbonNow);

        $this->repositoryMock
            ->shouldReceive('getActiveBonusTriggerSessionAtTimeByBonusId')
            ->with(1, $carbonNow->format('Y-m-d H:i:s'))
            ->once()
            ->andReturn($bonusTriggerSession);

        /** @var BonusTriggerSession $response */
        $response = $this->bonusService->getActiveBonusTriggerSessionByBonusId(1);

        $this->assertSame($response->id, $bonusTriggerSession->id);
        $this->assertSame($response->bonus_id, $bonusTriggerSession->bonus_id);
    }

    public function testSearchFilteredBonusesForBonusInfoPage(): void
    {
        $bonusSearchDto = new BonusSearchDto();
        $paginator = new LengthAwarePaginator([], 0, 10, 1);

        $this->repositoryMock
            ->shouldReceive('searchFilteredBonusesForBonusInfoPage')
            ->with($bonusSearchDto)
            ->once()
            ->andReturn($paginator);

        $this->slotServiceMock->shouldReceive('getSlotListByIds')->atLeast()->once()->andReturn([]);
        $this->slotProviderServiceMock->shouldReceive('getSlotProvidersByIds')->atLeast()->once()
            ->andReturn([]);

        $result = $this->bonusService->searchFilteredBonusesForBonusInfoPage($bonusSearchDto);

        /** @phpstan-ignore-next-line */
        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
    }

    public function testGetSmarticoBonusInfoByBonusId(): void
    {
        $bonusId = 1;
        $bonusInfo = new BonusInfo();
        $this->repositoryMock
            ->shouldReceive('getSmarticoBonusInfoByBonusId')
            ->with($bonusId)
            ->once()->andReturn($bonusInfo);

        $result = $this->bonusService->getSmarticoBonusInfoByBonusId($bonusId);

        $this->assertInstanceOf(BonusInfo::class, $result);
    }

    public function testRemoveByIds(): void
    {
        $this->repositoryMock
            ->shouldReceive('removeBonuses')
            ->with([1])
            ->once()->andReturn(true);

        $result = $this->bonusService->removeByIds([1]);
        $this->assertTrue($result);
    }

    public function testSaveBonus(): void
    {
        $bonusCrudDto = Mockery::mock(BonusCrudDto::class);
        $bonusCrudDto->id = 1;
        $bonus = new Bonus();
        $bonus->id = 1;

        $this->repositoryMock
            ->shouldReceive('getBonus')->with($bonus->id)
            ->once()
            ->andReturn($bonus);
        $this->repositoryMock
            ->shouldReceive('save')->with($bonus)
            ->once()
            ->andReturn(true);
        $bonusCrudDto
            ->shouldReceive('getDataForCreatingBonus')
            ->once()
            ->andReturn([]);

        $result = $this->bonusService->saveBonus($bonusCrudDto);

        /** @phpstan-ignore-next-line */
        $this->assertInstanceOf(Bonus::class, $result);
    }

    public function testDelete(): void
    {
        $bonus = Mockery::mock(Bonus::class);
        $hasMany = Mockery::mock(HasMany::class);

        $bonus->shouldReceive('balances')->once()->andReturn($hasMany);
        $hasMany->shouldReceive('delete')->once();
        $bonus->shouldReceive('delete')->once();

        DB::shouldReceive('transaction')->once()->andReturnUsing(static function ($callback): void {
            $callback();
        });

        $this->bonusService->delete($bonus);

        /** @phpstan-ignore-next-line */
        $this->assertTrue(true);
    }

    public function testCreateDefaultFreespinBonus(): void
    {
        $freeSpinCrudDto = Mockery::mock(FreeSpinCrudDto::class);
        $freeSpinCrudDto->maxTransfer = 1;
        //        $freeSpinCrudDto->bonuses = 1;
        $freeSpinCrudDto->currency = 'INR';
        $freeSpinCrudDto->duration = 10;
        $freeSpinCrudDto->minBet = 10;
        $freeSpinCrudDto->wager = 10.0;
        $freeSpinCrudDto->authorEmail = '<EMAIL>';

        $preparedDataForDefaultFreespinBonus = ['data' => 'mocked data'];

        $bonus = Mockery::mock(Bonus::class)->makePartial();

        $this->mockBonusService->shouldReceive('preparedDataForDefaultFreespinBonus')
            ->with($freeSpinCrudDto)
            ->andReturn($preparedDataForDefaultFreespinBonus);

        $this->mockBonusService->shouldReceive('fillBonusData')
            ->with($preparedDataForDefaultFreespinBonus, null)
            ->andReturn($bonus);

        $this->repositoryMock
            ->shouldReceive('save')
            ->once()
            ->andReturn(true);

        $bonus->shouldReceive('load')
            ->with(['bonusSlotProviders', 'bonusSlots', 'triggerSessions', 'freespin'])
            ->once()
            ->andReturnSelf();

        $result = $this->mockBonusService->createOrUpdateDefaultFreespinBonus($freeSpinCrudDto);

        /** @phpstan-ignore-next-line */
        $this->assertInstanceOf(Bonus::class, $result);
    }

    public function testGetFreeSpinDto(): void
    {
        $bonus = new Bonus();
        $bonus->id = 1;
        $bonusCrudDto = Mockery::mock(BonusCrudDto::class);
        $bonusCrudDto->slots = [$this->faker->numberBetween(1, 100)];
        $bonusCrudDto->slotProviders = [$this->faker->numberBetween(1, 100)];
        $freespin = new FreeSpin();
        $freespin->id = 1;

        app()->bind(FreeSpinService::class, static function () use ($bonus, $freespin) {
            $service = Mockery::mock(FreeSpinService::class);
            $service->shouldReceive('getFreeSpinByBonusId')->with($bonus->id)
                ->once()
                ->andReturn($freespin);

            return $service;
        });

        $bonusCrudDto->shouldReceive('toArray')->with(false)
            ->andReturn([
                'supplier' => SupplierEnum::GAMICORP_INTEGRATION->value,
                'title' => $this->faker->title,
                'slots_id' => $this->faker->numberBetween(1, 100),
                'bonus_balance' => 1,
                'provider_id' => $this->faker->numberBetween(1, 100),
                'bonus_id' => $this->faker->numberBetween(1, 100),
                'type' => 'manual',
                'name' => fake()->name(),
                'count' => 0,
                'bet' => 0,
                'bet_id' => $this->faker->numberBetween(1, 100),
                'denomination' => '1',
                'currency' => 'USD',
                'status' => 'active',
                'is_active' => true,
                'author_id' => $this->faker->numberBetween(1, 100),
                'updated_by_author_id' => $this->faker->numberBetween(1, 100),
                'in_process' => false,
                'start_at' => Carbon::now(),
                'expired_at' => Carbon::now()->addMonth(),
                'auto_cancel_withdrawal' => false,
            ]);

        $bonusCrudDto->activeFrom = Carbon::now()->addDay();
        $bonusCrudDto->activeTil = Carbon::now()->addMonth();

        $result = $this->bonusService->getFreeSpinDto($bonus, $bonusCrudDto, 'update');

        /** @phpstan-ignore-next-line */
        $this->assertInstanceOf(FreeSpinCrudDto::class, $result);

        $bonus->triggerSessions = collect([(object)['start_at' => Carbon::now()->addDay(),
            'end_at' => Carbon::now()->addMonth()]]);
        $bonusCrudDto->activeFrom = null;
        $bonusCrudDto->activeTil = null;

        $result = $this->bonusService->getFreeSpinDto($bonus, $bonusCrudDto, 'update');

        /** @phpstan-ignore-next-line */
        $this->assertInstanceOf(FreeSpinCrudDto::class, $result);
    }

    /**
     * @throws ReflectionException
     */
    public function testSyncBonusRelatedModels(): void
    {
        $bonus = new Bonus();
        $bonus->casino = false;
        $this->repositoryMock
            ->shouldReceive('syncSlots')->with($bonus, [])
            ->once();
        $this->repositoryMock
            ->shouldReceive('syncSlotProviders')->with($bonus, [])
            ->once();

        $syncTriggerSessions = new ReflectionMethod(BonusService::class, 'syncBonusRelatedModels');
        $result = $syncTriggerSessions->invoke($this->bonusService, $bonus, null, null);
        $this->assertNull($result);
    }

    /**
     * @throws ReflectionException
     */
    public function testSyncTriggerSessions(): void
    {
        $triggerSession = new BonusTriggerSession();
        $triggerSession->id = 1;
        $triggerSession->start_at = Carbon::now()->addDay();
        $triggerSession->end_at = Carbon::now()->addMonth();
        $bonus = new Bonus();
        $bonus->id = 1;
        $this->bonusTriggerSessionServiceMock
            ->shouldReceive('sessionsByBonusId')->with($bonus->id, 'start_at')
            ->once()
            ->andReturn(new Collection([$triggerSession]));
        $this->bonusTriggerSessionServiceMock
            ->shouldReceive('getTriggerSessionsByDates')
            ->once()
            ->andReturn($triggerSession);

        $syncTriggerSessions = new ReflectionMethod(BonusService::class, 'syncTriggerSessions');
        $result = $syncTriggerSessions->invoke($this->bonusService, $bonus, [$triggerSession->toArray()]);
        $this->assertNull($result);
    }

    /**
     * @throws ReflectionException
     */
    public function testSyncTriggerSessions2(): void
    {
        $triggerSession = new BonusTriggerSession();
        $triggerSession->id = 1;
        $triggerSession->start_at = Carbon::now()->addDay();
        $triggerSession->end_at = Carbon::now()->addMonth();
        $bonus = new Bonus();
        $bonus->id = 1;
        $this->bonusTriggerSessionServiceMock
            ->shouldReceive('sessionsByBonusId')->with($bonus->id, 'start_at')
            ->once()
            ->andReturn(new Collection([$triggerSession]));
        $this->bonusTriggerSessionServiceMock
            ->shouldReceive('getTriggerSessionsByDates')
            ->once()
            ->andReturn(null);
        $this->bonusTriggerSessionServiceMock
            ->shouldReceive('createByBonusIdAndDates')
            ->once()
            ->andReturn($triggerSession);

        $this->bonusTriggerSessionServiceMock
            ->shouldReceive('remove')
            ->once();

        $syncTriggerSessions = new ReflectionMethod(BonusService::class, 'syncTriggerSessions');
        $result = $syncTriggerSessions->invoke($this->bonusService, $bonus, [$triggerSession->toArray()]);
        $this->assertNull($result);
    }

    /**
     * @throws ReflectionException
     */
    public function testSyncTriggerSessions3(): void
    {
        $triggerSession = new BonusTriggerSession();
        $triggerSession->id = 1;
        $triggerSession->start_at = Carbon::now()->subDay();
        $triggerSession->end_at = Carbon::now()->addMonth();
        $bonus = new Bonus();
        $bonus->id = 1;
        $this->bonusTriggerSessionServiceMock
            ->shouldReceive('sessionsByBonusId')->with($bonus->id, 'start_at')
            ->once()
            ->andReturn(new Collection([$triggerSession]));
        $this->bonusTriggerSessionServiceMock
            ->shouldReceive('getTriggerSessionsByDates')
            ->once()
            ->andReturn(null);
        $this->bonusTriggerSessionServiceMock
            ->shouldReceive('createByBonusIdAndDates')
            ->once()
            ->andReturn($triggerSession);

        $isError = false;
        $syncTriggerSessions = new ReflectionMethod(BonusService::class, 'syncTriggerSessions');

        try {
            $syncTriggerSessions->invoke($this->bonusService, $bonus, [$triggerSession->toArray()]);
        } catch (InternalLogicException) {
            $isError = true;
        }
        $this->assertTrue($isError);
    }

    /**
     * @throws Exception
     */
    public function testCreateBetbyBonus(): void
    {
        Event::fake();

        $betbyBonusDTO = new BetbyBonusDTO([
            'template_id' => 1,
            'amount' => 100,
            'currency' => 'USD',
            'win_type' => 'default',
        ]);

        $template = [
            'data' => [
                0 => [
                    0 => [
                        'id' => 1,
                        'descriptions' => [
                            'en' => 'Sample description',
                        ],
                        'name' => 'Sample bonus',
                        'is_active' => true,
                        'type' => BonusTypeEnum::FREEBET->value,
                        'freebet_data' => [
                            'type' => BonusTypeEnum::FREE_BET->value,
                            'is_api_amount' => true,
                            'min_odd' => 1.5,
                        ],
                        'from_time' => Carbon::parse(1672531200),
                        'to_time' => Carbon::parse(**********),
                        'days_to_use' => 7,
                    ],
                ],
            ],
        ];

        $bonus = Mockery::mock(Bonus::class)->makePartial();

        $this->betbyService->shouldReceive('getTemplates')
            ->once()
            ->andReturn($template);

        $this->mockBonusService->shouldReceive('saveBonus')
            ->once()
            ->andReturn($bonus);

        $bonus->shouldReceive('load')
            ->with(['bonusSlotProviders', 'bonusSlots', 'triggerSessions', 'freespin'])
            ->once()
            ->andReturnSelf();

        $this->slotServiceMock->shouldReceive('getSlotListByIds')->atLeast()->andReturn([]);
        $this->slotProviderServiceMock->shouldReceive('getSlotProvidersByIds')->atLeast()->once()
            ->andReturn([]);

        $result = $this->mockBonusService->createBetbyBonus($betbyBonusDTO);

        $this->assertSame($bonus, $result);
    }

    /**
     * @throws Exception|ApiException|InvalidArgumentException
     */
    public function testApplyWelcomeBonus(): void
    {
        $dto = new BonusApplyDTO([
            'player_uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'bonus_id' => 10,
            'amount' => 100,
        ]);

        $player = Mockery::mock(PlayerDto::class);
        $bonus = Mockery::mock(Bonus::class)->makePartial();

        $bonus->shouldReceive('getAttribute')->with('is_welcome_group')->andReturn(true);

        $this->playerService->shouldReceive('getPlayerByUUID')->with('795db193-4bc2-43b4-88f9-21e529c6adeb')->andReturn($player);
        $this->repositoryMock->shouldReceive('getBonusById')->with(10)->andReturn($bonus);
        $this->bonusApplyService->shouldReceive('applyWelcomeBonusForPlayer')->with($bonus, $player)->once();
        $this->slotServiceMock->shouldReceive('getSlotListByIds')->atLeast()->andReturn([]);
        $this->slotProviderServiceMock->shouldReceive('getSlotProvidersByIds')->atLeast()->once()
            ->andReturn([]);
        $result = $this->bonusService->apply($dto);

        $this->assertSame($bonus, $result);
    }

    /**
     * @throws Exception|ApiException|InvalidArgumentException
     */
    public function testApplyDepositBonus(): void
    {
        $dto = new BonusApplyDTO([
            'player_uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'bonus_id' => 10,
            'amount' => 100,
        ]);

        $player = Mockery::mock(PlayerDto::class);
        $bonus = Mockery::mock(Bonus::class)->makePartial();

        $bonus->shouldReceive('getAttribute')->with('is_deposit')->andReturn(true);

        $this->playerService->shouldReceive('getPlayerByUUID')->with('795db193-4bc2-43b4-88f9-21e529c6adeb')->andReturn($player);
        $this->repositoryMock->shouldReceive('getBonusById')->with(10)->andReturn($bonus);
        $this->bonusApplyService->shouldReceive('applyDepositBonusForPlayer')->with($bonus, $player)->once();
        $this->slotServiceMock->shouldReceive('getSlotListByIds')->atLeast()->andReturn([]);
        $this->slotProviderServiceMock->shouldReceive('getSlotProvidersByIds')->atLeast()->once()
            ->andReturn([]);

        $result = $this->bonusService->apply($dto);

        $this->assertSame($bonus, $result);
    }

    /**
     * @throws Exception|ApiException|InvalidArgumentException
     */
    public function testApplyFreeSpinForDepositBonus(): void
    {
        $dto = new BonusApplyDTO([
            'player_uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'bonus_id' => 10,
            'amount' => 100,
        ]);

        $player = Mockery::mock(PlayerDto::class);
        $bonus = Mockery::mock(Bonus::class)->makePartial();

        $bonus->shouldReceive('getAttribute')->with('is_free_spin_for_deposit')->andReturn(true);

        $this->playerService->shouldReceive('getPlayerByUUID')->with('795db193-4bc2-43b4-88f9-21e529c6adeb')->andReturn($player);
        $this->repositoryMock->shouldReceive('getBonusById')->with(10)->andReturn($bonus);
        $this->bonusApplyService->shouldReceive('applyFreeSpinForDepositBonusForPlayer')->with($bonus, $player)->once();
        $this->slotServiceMock->shouldReceive('getSlotListByIds')->atLeast()->andReturn([]);
        $this->slotProviderServiceMock->shouldReceive('getSlotProvidersByIds')->atLeast()->once()
            ->andReturn([]);

        $result = $this->bonusService->apply($dto);

        $this->assertSame($bonus, $result);
    }

    /**
     * @throws Exception|ApiException|InvalidArgumentException
     */
    public function testApplyOneTimeBonus(): void
    {
        /** @var BonusApplyDTO $dto */
        $dto = BonusApplyDTO::fromArray([
            'player_uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'bonus_id' => 1,
            'amount' => 100,
        ]);

        $player = Mockery::mock(PlayerDto::class);
        $bonus = Mockery::mock(Bonus::class)->makePartial();

        $bonus->shouldReceive('getAttribute')->with('is_onetime_group')->andReturn(true);

        $this->playerService->shouldReceive('getPlayerByUUID')->with('795db193-4bc2-43b4-88f9-21e529c6adeb')->andReturn($player);
        $this->repositoryMock->shouldReceive('getBonusById')->with(1)->andReturn($bonus);
        $this->bonusApplyService->shouldReceive('applyOnetimeForPlayer')->with($bonus, $player, $dto->amount)->once();
        $this->slotServiceMock->shouldReceive('getSlotListByIds')->atLeast()->andReturn([]);
        $this->slotProviderServiceMock->shouldReceive('getSlotProvidersByIds')->atLeast()->once()
            ->andReturn([]);

        $result = $this->bonusService->apply($dto);

        $this->assertSame($bonus, $result);
    }

    /**
     * @throws ApiException|InvalidArgumentException
     */
    public function testApplyWithInvalidBonusType(): void
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Invalid argument provided for bonusId');

        $dto = new BonusApplyDTO([
            'player_uuid' => '795db193-4bc2-43b4-88f9-21e529c6adeb',
            'bonus_id' => 1,
            'amount' => 0,
        ]);

        $player = Mockery::mock(PlayerDto::class);
        $bonus = Mockery::mock(Bonus::class);

        $bonus->shouldReceive('getAttribute')->with('is_welcome_group')->andReturn(false);
        $bonus->shouldReceive('getAttribute')->with('is_deposit')->andReturn(false);
        $bonus->shouldReceive('getAttribute')->with('is_free_spin_for_deposit')->andReturn(false);
        $bonus->shouldReceive('getAttribute')->with('is_onetime_group')->andReturn(false);


        $this->playerService->shouldReceive('getPlayerByUUID')->with('795db193-4bc2-43b4-88f9-21e529c6adeb')->andReturn($player);
        $this->repositoryMock->shouldReceive('getBonusById')->with(1)->andReturn($bonus);

        $this->bonusService->apply($dto);
    }

    /**
     * @throws \Exception
     */
    public function testGetBetbyTemplatesWithNullParameters(): void
    {
        $dto = new BetbySearchDTO([
            'id' => null,
            'type' => null,
            'is_active' => null,
        ]);

        $expectedResult = [['id' => '456', 'type' => 'default', 'isActive' => false]];
        $this->betbyService
            ->shouldReceive('getTemplates')
            ->with($dto)
            ->andReturn($expectedResult);

        $result = $this->bonusService->getBetbyTemplates($dto);

        $this->assertSame($expectedResult, $result);
    }

    /**
     * @throws \Exception
     */
    public function testGetBetbyTemplatesWithAllParameters(): void
    {
        $dto = new BetbySearchDTO([
            'id' => '1',
            'type' => 'freebet',
            'is_active' => true,
        ]);

        $expectedResult = [['id' => '123', 'type' => 'bonus', 'isActive' => true]];
        $this->betbyService
            ->shouldReceive('getTemplates')
            ->with($dto)
            ->andReturn($expectedResult);

        $result = $this->bonusService->getBetbyTemplates($dto);

        $this->assertSame($expectedResult, $result);
    }

    public function testIsWagerPayableReturnsTrueIfGeneralAndReady(): void
    {
        $bonus = new Bonus();

        Cache::shouldReceive('tags->remember')
            ->twice()
            ->andReturnUsing(static fn($key, $ttl, $callback) => $callback());

        $dto = IsWagerPayableDTO::fromArray([
            'bonus_id' => 1,
            'slot_id' => 2,
            'external_provider_id' => 3,
            'is_bonus_ready' => true,
        ]);

        $this->repositoryMock
            ->shouldReceive('getActualBonusById')
            ->andReturn($bonus);

        $result = $this->bonusService->isWagerPayable($dto);
        $this->assertTrue($result);
    }

    public function testIsWagerPayableReturnsTrueIfBonusIsNull(): void
    {
        Cache::shouldReceive('tags->remember')
            ->once()
            ->andReturnUsing(static fn($key, $ttl, $callback) => $callback());

        $dto = IsWagerPayableDTO::fromArray([
            'bonus_id' => 1,
            'slot_id' => 2,
            'external_provider_id' => 3,
            'is_bonus_ready' => true,
        ]);

        $this->repositoryMock
            ->shouldReceive('getActualBonusById')
            ->andReturn(null);

        $result = $this->bonusService->isWagerPayable($dto);
        $this->assertTrue($result);
    }

    public function testIsGeneralReturnsTrueWhenNoSlotsOrProviders(): void
    {
        Cache::shouldReceive('tags->remember')
            ->once()
            ->andReturn(true);

        $result = $this->bonusService->isGeneral(1);
        $this->assertTrue($result);
    }

    public function testGetBonusesIdsBySlotIdReturnsUniqueBonusIds(): void
    {
        $bonusSlots = new Collection([
            (object)['bonus_id' => 1],
            (object)['bonus_id' => 2],
            (object)['bonus_id' => 1],
        ]);

        $this->repositoryMock
            ->shouldReceive('getBonusSlotsBySlotId')
            ->andReturn($bonusSlots);

        $result = $this->bonusService->getBonusesIdsBySlotId(123);

        $this->assertSame([1, 2], $result);
    }

    public function test_create_bonus_with_genre_id_success()
    {
        $bonusCrudDto = Mockery::mock(BonusCrudDto::class);
        $bonusCrudDto->id = 1;
        $bonusCrudDto->genreId = 1;
        $bonusCrudDto->type = 'casino';
        $bonus = new Bonus();
        $bonus->id = 1;

        $this->repositoryMock
            ->shouldReceive('getBonus')->with($bonus->id)
            ->once()
            ->andReturn($bonus);
        $this->repositoryMock
            ->shouldReceive('save')->with($bonus)
            ->once()
            ->andReturn(true);
        $bonusCrudDto
            ->shouldReceive('getDataForCreatingBonus')
            ->once()
            ->andReturn([]);
        $this->slotServiceMock->shouldReceive('getSlotListByIds')
            ->once()
            ->andReturn([]);
        $this->slotProviderServiceMock->shouldReceive('getSlotProvidersByIds')
            ->once()
            ->andReturn([]);

        $result = $this->bonusService->createOrUpdate($bonusCrudDto);
        $this->assertInstanceOf(Bonus::class, $result);
    }

}
