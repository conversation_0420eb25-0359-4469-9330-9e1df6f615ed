## Использование гит хуков в проекте, для проврки кода перед коммитом

1. Создать файл .git/hooks/pre-commit. Содержимое файла:
```bash
    #!/usr/bin/env bash
    #
    # The hook script to verify what is about to be committed.

    # perform the pre-commit script
    bash ./.git-hooks/pre-commit

    # return the last code of the pre-commit script
    exit $?
```

2. В .env нужно добавить переменную окружения, в которой нужно указать путь к docker-compose.yml:
```dotenv
    # Path to docker-compose.yml file
    DOCKER_COMPOSE_FILE=./../.docker-backend-services/Deversin/docker-compose.yml
```
