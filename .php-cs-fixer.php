<?php

declare(strict_types=1);

use PhpCsFixer\Config;
use PhpCsFixer\Finder;

$rootDir = realpath(__DIR__);
$finder = Finder::create()->in([
    "$rootDir/app",
    "$rootDir/bootstrap",
    "$rootDir/config",
    "$rootDir/routes",
]);

// https://mlocati.github.io/php-cs-fixer-configurator/#version:3.18|configurator
return (new Config())
    ->setFinder($finder)
    ->setCacheFile("$rootDir/.php-cs-fixer.cache")
    ->setRiskyAllowed(true)
    ->setRules([
        '@PSR12' => true,
        '@PhpCsFixer' => true,
        '@PhpCsFixer:risky' => true,
        '@PER-CS' => true,
        '@PER-CS:risky' => true,
        '@PHP80Migration' => true,
        '@PHP80Migration:risky' => true,
        '@PHP81Migration' => true,
        'blank_line_before_statement' => [
            'statements' => [
                'break',
                'continue',
                'declare',
                'return',
                'throw',
                'try',
                'yield',
                'yield_from',
            ],
        ],
        'multiline_whitespace_before_semicolons' => [
            'strategy' => 'no_multi_line',
        ],
        'php_unit_test_case_static_method_calls' => [
            'call_type' => 'this',
        ],
        'cast_spaces' => [
            'space' => 'none',
        ],
        'concat_space' => [
            'spacing' => 'one',
        ],
        'date_time_immutable' => true,
        'explicit_string_variable' => false,
        'function_declaration' => [
            'closure_fn_spacing' => 'none',
            'closure_function_spacing' => 'one',
            'trailing_comma_single_line' => false,
        ],
        'global_namespace_import' => [
            'import_classes' => true,
            'import_constants' => false,
            'import_functions' => false,
        ],
        'mb_str_functions' => true,
        'php_unit_internal_class' => false,
        'php_unit_test_class_requires_covers' => false,
        'php_unit_data_provider_name' => false,
        'phpdoc_align' => [
            'align' => 'left',
        ],
        'phpdoc_line_span' => [
            'const' => 'multi',
            'method' => 'multi',
            'property' => 'multi',
        ],
        'phpdoc_summary' => false,
        'phpdoc_tag_casing' => true,
        'phpdoc_tag_type' => [
            'tags' => [
                'api' => 'annotation',
                'author' => 'annotation',
                'copyright' => 'annotation',
                'deprecated' => 'annotation',
                'example' => 'annotation',
                'global' => 'annotation',
                'inheritDoc' => 'annotation',
                'internal' => 'annotation',
                'license' => 'annotation',
                'method' => 'annotation',
                'package' => 'annotation',
                'param' => 'annotation',
                'property' => 'annotation',
                'return' => 'annotation',
                'see' => 'annotation',
                'since' => 'annotation',
                'throws' => 'annotation',
                'todo' => 'annotation',
                'uses' => 'annotation',
                'var' => 'annotation',
                'version' => 'annotation',
            ],
        ],
        'phpdoc_types_order' => [
            'null_adjustment' => 'always_first',
            'sort_algorithm' => 'none',
        ],
        'phpdoc_to_comment' => false,
        'regular_callable_call' => true,
        'return_assignment' => false,
        'simplified_if_return' => true,
        'static_lambda' => true,
        'trailing_comma_in_multiline' => [
            'elements' => [
                'arrays',
                'match',
            ],
        ],
        'yoda_style' => false,
        'single_line_empty_body' => false,
        'native_function_invocation' => false,
        'no_multiline_whitespace_around_double_arrow' => false,
        'phpdoc_no_alias_tag' => ['replacements' => ['property-read' => 'property-read', 'property-write' => 'property-write']],
        'php_unit_data_provider_return_type' => false,
        'declare_strict_types' => false,
    ]);
