#!/bin/bash

echo "Running PHPStan to check changes before commit..."

cd $(dirname "$0")/../
# Get the list of changed PHP files
FILES=$(git status --porcelain | grep '\.php$' | grep -v '^D' | awk '{print $2}')
# If there are no PHP files, exit
if [ -z "$FILES" ]; then
    echo -e "\033[32mNo changed PHP files. Commit allowed.\033[0m"
    exit 0
fi

# Run PHPStan to check the changed files
PHPSTAN_OUTPUT=$(./vendor/bin/phpstan analyse --memory-limit=1G --error-format=table $FILES 2>&1)
PHPSTAN_STATUS=$?

# If PHPStan returns an error code, block the commit and display the output
if [ $PHPSTAN_STATUS -ne 0 ]; then
    echo -e "\033[31mPHPStan detected errors. Commit denied.\033[0m"
    echo -e "\033[33m$PHPSTAN_OUTPUT\033[0m"
    exit 1
fi
echo -e "\033[32mPHPStan check passed. Commit allowed.\033[0m"
exit 0
