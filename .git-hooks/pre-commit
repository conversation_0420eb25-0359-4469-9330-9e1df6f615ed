#!/usr/bin/env bash
#
# Pre-commit hook to run code quality checks

# Load environment variables from .env file if it exists
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

# Define the base directory for scripts
SCRIPT_DIR=$(dirname "$0")

# Run the commands inside the Docker container
docker compose -f "$DOCKER_COMPOSE_FILE" exec \
    --user root --no-TTY bonus-management /usr/bin/env bash -c "\
    git config --global --add safe.directory /var/www/html
    bash $SCRIPT_DIR/phpstan && \
    bash $SCRIPT_DIR/php-cs-fixer-analyze"
