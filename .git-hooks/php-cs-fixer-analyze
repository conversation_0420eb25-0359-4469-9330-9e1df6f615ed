#!/bin/bash

echo "Running PHP CS Fixer to check changes before committing..."

cd $(dirname "$0")/../
# Get the list of modified PHP files
FILES=$(git status --porcelain | grep '\.php$' | grep -v '^D' | awk '{print $2}')

# If there are no PHP files, stop execution
if [ -z "$FILES" ]; then
    echo -e "\033[32mNo modified PHP files. Commit allowed.\033[0m"
    exit 0
fi

# Check the modified files using PHP CS Fixer (dry-run mode)
FIXER_OUTPUT=$(./vendor/bin/php-cs-fixer fix --dry-run --config=./.php-cs-fixer.php --diff $FILES 2>&1)
FIXER_STATUS=$?

# If PHP CS Fixer returned an error code, block the commit
if [ $FIXER_STATUS -ne 0 ]; then
    echo -e "\033[31mPHP CS Fixer found formatting issues. Commit rejected.\033[0m"
    echo -e "\033[33m$FIXER_OUTPUT\033[0m"  # Output in yellow for better readability
    exit 1
fi

echo -e "\033[32mPHP CS Fixer check passed. Commit allowed.\033[0m"
exit 0
